/* eslint-disable @next/next/no-img-element */
'use client';

import { Card, CardContent } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Sheet, SheetContent } from '@/components/ui/sheet';
import { Drawer, DrawerContent } from '@/components/ui/drawer';
import { useIsMobile } from '@/hooks/use-mobile';
import type { Research } from '@/lib/ai/tools/extreme-search';
import type { ToolUIPart } from 'ai';
import React, { useEffect, useState, memo, useMemo } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import {
  ChevronDown,
  ChevronRight,
  ArrowUpRight,
  Globe,
  Search,
  ExternalLink,
} from 'lucide-react';
import { TextShimmer } from '@/components/core/text-shimmer';
import { Skeleton } from '@/components/ui/skeleton';
import { cn } from '@/lib/utils';
// Define the annotation type locally since it doesn't exist in @/lib/types
interface DataExtremeSearchPart {
  type: 'data-extreme_search';
  data: {
    kind: 'plan' | 'query' | 'source' | 'content' | 'code';
    queryId?: string;
    codeId?: string;
    query?: string;
    status?:
      | 'started'
      | 'reading_content'
      | 'completed'
      | 'running'
      | 'error'
      | { title: string };
    plan?: Array<{ title: string; todos: string[] }>;
    source?: { title: string; url: string; favicon?: string };
    content?: { title: string; url: string; text: string; favicon?: string };
    title?: string;
    code?: string;
    result?: string;
    charts?: any[];
  };
}

// Types for timeline items
interface SearchQuery {
  id: string;
  query: string;
  status:
    | 'started'
    | 'reading_content'
    | 'completed'
    | 'running'
    | 'error'
    | { title: string };
  sources: Array<{
    title: string;
    url: string;
    favicon?: string;
    content?: string;
  }>;
}

interface CodeExecution {
  id: string;
  title: string;
  code: string;
  status: 'running' | 'completed' | 'error';
  result?: string;
  charts?: any[];
}

// Source Card Component
const ExtremeSourceCard: React.FC<{
  source: {
    title: string;
    url: string;
    content?: string;
    favicon?: string;
    publishedDate?: string;
    author?: string;
  };
  onClick?: () => void;
}> = ({ source, onClick }) => {
  const [imageLoaded, setImageLoaded] = useState(false);
  const faviconUrl =
    source.favicon ||
    `https://www.google.com/s2/favicons?sz=128&domain=${encodeURIComponent(new URL(source.url || 'example.com').hostname)}`;

  let hostname = '';
  try {
    hostname = new URL(source.url).hostname.replace('www.', '');
  } catch {
    hostname = source.url;
  }

  return (
    <div
      className={cn(
        'group relative bg-background',
        'border border-neutral-200 dark:border-neutral-800',
        'rounded-xl p-4 transition-all duration-200',
        'hover:border-neutral-300 dark:hover:border-neutral-700',
        onClick && 'cursor-pointer',
      )}
      {...(onClick && {
        role: 'button',
        tabIndex: 0,
        onClick,
        onKeyDown: (e) => {
          if (e.key === 'Enter' || e.key === ' ') {
            e.preventDefault();
            onClick();
          }
        },
      })}
    >
      <div className="flex items-start gap-3 mb-3">
        <div className="relative w-10 h-10 rounded-lg bg-neutral-100 dark:bg-neutral-800 flex items-center justify-center overflow-hidden shrink-0">
          {!imageLoaded && <div className="absolute inset-0 animate-pulse" />}
          {faviconUrl ? (
            <img
              src={faviconUrl}
              alt=""
              width={24}
              height={24}
              className={cn('object-contain', !imageLoaded && 'opacity-0')}
              onLoad={() => setImageLoaded(true)}
              onError={(e) => {
                setImageLoaded(true);
                e.currentTarget.style.display = 'none';
              }}
            />
          ) : (
            <Globe className="w-5 h-5 text-neutral-400" />
          )}
        </div>

        <div className="flex-1 min-w-0">
          <h3 className="font-medium text-sm text-neutral-900 dark:text-neutral-100 line-clamp-1 mb-1">
            {source.title || hostname}
          </h3>
          <div className="flex items-center gap-1.5 text-xs text-neutral-500 dark:text-neutral-400">
            <span className="truncate">{hostname}</span>
            <ExternalLink className="w-3 h-3 shrink-0 opacity-0 group-hover:opacity-100 transition-opacity" />
          </div>
        </div>
      </div>

      <p className="text-sm text-neutral-600 dark:text-neutral-400 line-clamp-2 leading-relaxed">
        {source.content || 'Loading content...'}
      </p>
    </div>
  );
};

// Sources Sheet Component
const ExtremeSourcesSheet: React.FC<{
  sources: any[];
  open: boolean;
  onOpenChange: (open: boolean) => void;
}> = ({ sources, open, onOpenChange }) => {
  const isMobile = useIsMobile();

  const SheetWrapper = isMobile ? Drawer : Sheet;
  const SheetContentWrapper = isMobile ? DrawerContent : SheetContent;

  return (
    <SheetWrapper open={open} onOpenChange={onOpenChange}>
      <SheetContentWrapper
        className={cn(
          isMobile ? 'h-[85vh]' : 'w-[600px] sm:max-w-[600px]',
          'p-0',
        )}
      >
        <div className="flex flex-col h-full">
          <div className="px-6 py-5 border-b border-neutral-200 dark:border-neutral-800">
            <div>
              <h2 className="text-lg font-semibold text-neutral-900 dark:text-neutral-100">
                All Sources
              </h2>
              <p className="text-sm text-neutral-500 dark:text-neutral-400 mt-0.5">
                {sources.length} research sources
              </p>
            </div>
          </div>

          <div className="flex-1 overflow-y-auto">
            <div className="p-6 space-y-3">
              {sources.map((source) => (
                <a
                  key={source.url}
                  href={source.url}
                  target="_blank"
                  rel="noopener noreferrer"
                  className="block"
                >
                  <ExtremeSourceCard source={source} />
                </a>
              ))}
            </div>
          </div>
        </div>
      </SheetContentWrapper>
    </SheetWrapper>
  );
};

const ExtremeSearchComponent = ({
  toolInvocation,
  annotations,
}: {
  toolInvocation: ToolUIPart;
  annotations?: DataExtremeSearchPart[];
}) => {
  const { state } = toolInvocation;
  const [expandedItems, setExpandedItems] = useState<Record<string, boolean>>(
    {},
  );
  const [userExpandedItems, setUserExpandedItems] = useState<
    Record<string, boolean>
  >({});
  const [researchProcessOpen, setResearchProcessOpen] = useState(false);
  const [sourcesAccordionOpen, setSourcesAccordionOpen] = useState(true);
  const [sourcesSheetOpen, setSourcesSheetOpen] = useState(false);
  const [researchReportOpen, setResearchReportOpen] = useState(true);

  // Debug what we're actually receiving
  useEffect(() => {
    console.log('[ExtremeSearch] ===================');
    console.log('[ExtremeSearch] Checking completion status...');
    console.log('[ExtremeSearch] toolInvocation:', toolInvocation);
    console.log('[ExtremeSearch] annotations:', annotations);
    console.log(
      '[ExtremeSearch] annotations length:',
      annotations?.length || 0,
    );

    // Log each annotation for debugging
    if (annotations?.length) {
      annotations.forEach((ann, index) => {
        console.log(`[ExtremeSearch] Annotation ${index}:`, ann);
      });
    }

    // Check if tool has output (primary completion indicator)
    if ('output' in toolInvocation && toolInvocation.output) {
      console.log(
        '[ExtremeSearch] ✅ Completed via output - tool has finished',
      );
    }

    // Check if toolInvocation has result property (alternative completion indicator)
    if ('result' in toolInvocation && toolInvocation.result) {
      console.log(
        '[ExtremeSearch] ✅ Completed via result - tool has finished',
      );
    }

    // Also check if annotations indicate completion
    if (annotations?.length) {
      const planAnnotations = annotations.filter(
        (ann) => ann.type === 'data-extreme_search' && ann.data.kind === 'plan',
      );
      const latestPlan = planAnnotations[planAnnotations.length - 1];
      const isResearchCompleted =
        latestPlan?.data?.kind === 'plan' &&
        typeof latestPlan.data.status === 'object' &&
        latestPlan.data.status?.title === 'Research completed';

      if (isResearchCompleted) {
        console.log(
          '[ExtremeSearch] ✅ Completed via annotations - Research completed status',
        );
      }
    }

    console.log('[ExtremeSearch] ===================');
  }, [toolInvocation, annotations, state]);

  // Check if we're in final result state
  const isCompleted = useMemo(() => {
    console.log('[ExtremeSearch] Checking completion status...');
    console.log('[ExtremeSearch] toolInvocation:', toolInvocation);
    console.log('[ExtremeSearch] annotations:', annotations);

    // Check if tool has output (primary completion indicator)
    if ('output' in toolInvocation && toolInvocation.output) {
      console.log(
        '[ExtremeSearch] ✅ Completed via output - tool has finished',
      );
      return true;
    }

    // Check if toolInvocation has result property (alternative completion indicator)
    if ('result' in toolInvocation && toolInvocation.result) {
      console.log(
        '[ExtremeSearch] ✅ Completed via result - tool has finished',
      );
      return true;
    }

    // Also check if annotations indicate completion
    if (annotations?.length) {
      const planAnnotations = annotations.filter(
        (ann) => ann.type === 'data-extreme_search' && ann.data.kind === 'plan',
      );
      const latestPlan = planAnnotations[planAnnotations.length - 1];
      const isResearchCompleted =
        latestPlan?.data?.kind === 'plan' &&
        typeof latestPlan.data.status === 'object' &&
        latestPlan.data.status?.title === 'Research completed';

      if (isResearchCompleted) {
        console.log(
          '[ExtremeSearch] ✅ Completed via annotations - Research completed status',
        );
        return true;
      }
    }

    console.log(
      '[ExtremeSearch] ❌ Not completed - no output and no completion annotation',
    );
    return false;
  }, [toolInvocation, annotations]);

  // Extract search queries from annotations
  const searchQueries = useMemo(() => {
    console.log(
      '[ExtremeSearch] Processing annotations:',
      annotations?.length || 0,
    );
    console.log('[ExtremeSearch] All annotations:', annotations);

    if (!annotations?.length) return [];

    const queryMap = new Map<string, SearchQuery>();

    annotations.forEach((ann, index) => {
      console.log(`[ExtremeSearch] Annotation ${index}:`, ann);
      if (ann.type !== 'data-extreme_search') {
        console.log(
          `[ExtremeSearch] Skipping annotation ${index} - wrong type:`,
          ann.type,
        );
        return;
      }

      const { data } = ann;
      console.log(
        `[ExtremeSearch] Processing data-extreme_search annotation:`,
        data,
      );

      if (data.kind === 'query' && data.queryId && data.query && data.status) {
        // Either create new query or update existing one
        const existingQuery = queryMap.get(data.queryId);
        if (existingQuery) {
          // Update existing query status
          existingQuery.status = data.status;
        } else {
          // Create new query
          queryMap.set(data.queryId, {
            id: data.queryId,
            query: data.query,
            status: data.status,
            sources: [],
          });
        }
      } else if (data.kind === 'source' && data.source && data.queryId) {
        const query = queryMap.get(data.queryId);
        if (query && !query.sources.find((s) => s.url === data.source?.url)) {
          query.sources.push({
            title: data.source.title || '',
            url: data.source.url,
            favicon:
              data.source.favicon ||
              `https://www.google.com/s2/favicons?sz=128&domain=${encodeURIComponent(new URL(data.source.url).hostname)}`,
          });
        }
      }
    });

    return Array.from(queryMap.values());
  }, [annotations]);

  // Extract current status
  const currentStatus = useMemo(() => {
    if (isCompleted) return 'Research completed';

    if (!annotations?.length) {
      return state === 'input-streaming'
        ? 'Starting research...'
        : 'Initializing...';
    }

    // Get the latest query status
    const queryAnnotations = annotations.filter(
      (ann) => ann.type === 'data-extreme_search' && ann.data.kind === 'query',
    );

    if (queryAnnotations.length > 0) {
      const latestQuery = queryAnnotations[queryAnnotations.length - 1];
      const queryStatus = latestQuery.data.status;
      const queryText = latestQuery.data.query;

      if (queryStatus && queryText) {
        switch (queryStatus) {
          case 'started':
            return `Searching: "${queryText}"`;
          case 'reading_content':
            return `Reading content for: "${queryText}"`;
          case 'completed':
            return 'Analyzing results...';
          default:
            return 'Processing research...';
        }
      }
    }

    return 'Processing research...';
  }, [annotations, state, isCompleted]);

  // Get all sources for final result view
  const allSources = useMemo(() => {
    console.log('[ExtremeSearch] === EXTRACTING SOURCES ===');
    console.log('[ExtremeSearch] isCompleted:', isCompleted);

    if (isCompleted && 'output' in toolInvocation) {
      // Completed with tool output
      const { output } = toolInvocation;
      console.log(
        '[ExtremeSearch] Tool output:',
        JSON.stringify(output, null, 2),
      );

      const researchData = output as { research?: Research } | null;
      const research = researchData?.research;

      console.log('[ExtremeSearch] Research object:', research);
      console.log('[ExtremeSearch] Research sources array:', research?.sources);
      console.log(
        '[ExtremeSearch] Research sources length:',
        research?.sources?.length,
      );

      if (research?.sources?.length) {
        const mappedSources = research.sources.map((s) => ({
          ...s,
          favicon:
            s.favicon ||
            `https://www.google.com/s2/favicons?sz=128&domain=${encodeURIComponent(new URL(s.url).hostname)}`,
        }));
        console.log(
          '[ExtremeSearch] ✅ Using research.sources:',
          mappedSources.length,
        );
        return mappedSources;
      }

      console.log(
        '[ExtremeSearch] ⚠️ No research.sources, checking toolResults...',
      );
      if (research?.toolResults) {
        const webSearchSources = research.toolResults
          .filter((result) => result.toolName === 'webSearch')
          .flatMap((result) =>
            (result.result || result.output || []).map((source: any) => ({
              title: source.title || '',
              url: source.url || '',
              content: source.content || '',
              publishedDate: source.publishedDate || '',
              favicon:
                source.favicon ||
                `https://www.google.com/s2/favicons?sz=128&domain=${encodeURIComponent(new URL(source.url || 'example.com').hostname)}`,
            })),
          );
        console.log(
          '[ExtremeSearch] ✅ Using toolResults sources:',
          webSearchSources.length,
        );
        return webSearchSources;
      }
    }

    // Try extracting from toolInvocation.result as well (some SDKs populate `result`)
    if (isCompleted && 'result' in toolInvocation && toolInvocation.result) {
      try {
        const resultAny = toolInvocation.result as any;
        const research = resultAny?.research as Research | undefined;
        console.log('[ExtremeSearch] Result-based research object:', research);
        if (research?.sources?.length) {
          const mappedSources = research.sources.map((s) => ({
            ...s,
            favicon:
              s.favicon ||
              `https://www.google.com/s2/favicons?sz=128&domain=${encodeURIComponent(new URL(s.url || 'example.com').hostname)}`,
          }));
          console.log(
            '[ExtremeSearch] ✅ Using result.research.sources:',
            mappedSources.length,
          );
          return mappedSources;
        }

        // Fallback: sometimes sources may be embedded under toolResults
        if (research?.toolResults) {
          const webSearchSources = research.toolResults
            .filter((result) => result.toolName === 'webSearch')
            .flatMap((result) =>
              (result.result || result.output || []).map((source: any) => ({
                title: source.title || '',
                url: source.url || '',
                content: source.content || '',
                publishedDate: source.publishedDate || '',
                favicon:
                  source.favicon ||
                  `https://www.google.com/s2/favicons?sz=128&domain=${encodeURIComponent(new URL(source.url || 'example.com').hostname)}`,
              })),
            );
          if (webSearchSources.length) {
            console.log(
              '[ExtremeSearch] ✅ Using result.research.toolResults sources:',
              webSearchSources.length,
            );
            return webSearchSources;
          }
        }
      } catch (e) {
        console.log('[ExtremeSearch] Error reading sources from result:', e);
      }
    }

    // Use sources from search queries (whether completed or not)
    const querySources = searchQueries.flatMap((q) => q.sources);
    console.log(
      '[ExtremeSearch] 🔄 Using sources from queries:',
      querySources.length,
    );

    // Remove duplicates by URL
    const uniqueSources = Array.from(
      new Map(querySources.map((s) => [s.url, s])).values(),
    );
    console.log(
      '[ExtremeSearch] ✅ Final unique sources:',
      uniqueSources.length,
    );

    return uniqueSources;
  }, [isCompleted, toolInvocation, searchQueries]);

  // Auto-expand logic
  useEffect(() => {
    if (isCompleted) return;

    setExpandedItems((prev) => {
      const newExpanded = { ...prev };
      let shouldUpdate = false;

      searchQueries.forEach((query) => {
        const isActive =
          query.status === 'started' || query.status === 'reading_content';
        const wasUserControlled = userExpandedItems[query.id];

        if (isActive && !prev[query.id] && !wasUserControlled) {
          newExpanded[query.id] = true;
          shouldUpdate = true;
        }

        if (
          query.status === 'completed' &&
          prev[query.id] &&
          !wasUserControlled
        ) {
          newExpanded[query.id] = false;
          shouldUpdate = true;
        }
      });

      return shouldUpdate ? newExpanded : prev;
    });
  }, [searchQueries, userExpandedItems, isCompleted]);

  const toggleItemExpansion = (itemId: string) => {
    setExpandedItems((prev) => ({ ...prev, [itemId]: !prev[itemId] }));
    setUserExpandedItems((prev) => ({ ...prev, [itemId]: true }));
  };

  const renderTimeline = () => (
    <div className="space-y-1 relative ml-3">
      <AnimatePresence>
        {searchQueries.map((query, itemIndex) => {
          const isLoading =
            query.status === 'started' || query.status === 'reading_content';
          const hasResults = query.sources.length > 0;

          const bulletColor = isLoading
            ? 'bg-primary/80 animate-[pulse_0.8s_ease-in-out_infinite]'
            : hasResults
              ? 'bg-primary'
              : 'bg-yellow-500';

          return (
            <motion.div
              key={query.id}
              className="space-y-0 relative"
              initial={{ opacity: 0, y: 2 }}
              animate={{ opacity: 1, y: 0 }}
              exit={{ opacity: 0 }}
              transition={{ duration: 0.1, delay: itemIndex * 0.01 }}
            >
              {/* Timeline bullet */}
              <div
                className={`absolute size-1 rounded-full ${bulletColor} transition-colors duration-300 z-10`}
                style={{
                  left: '-0.6rem',
                  top: '5.5px',
                  transform: 'translateX(-50%)',
                }}
              />

              {/* Vertical line */}
              {itemIndex > 0 && (
                <div
                  className="absolute w-0.25 bg-border"
                  style={{
                    left: '-0.6rem',
                    top: '-6px',
                    height: '12px',
                    transform: 'translateX(-50%)',
                  }}
                />
              )}

              <div
                className="flex items-center gap-1 cursor-pointer py-0.5 px-1 hover:bg-muted rounded-sm relative min-h-[18px]"
                role="button"
                tabIndex={0}
                onClick={() => toggleItemExpansion(query.id)}
                onKeyDown={(e) => {
                  if (e.key === 'Enter' || e.key === ' ') {
                    e.preventDefault();
                    toggleItemExpansion(query.id);
                  }
                }}
              >
                <Search className="w-2.5 h-2.5 text-muted-foreground flex-shrink-0" />

                <span className="text-foreground text-xs min-w-0 flex-1">
                  {isLoading && !isCompleted ? (
                    <TextShimmer className="w-full" duration={1.5}>
                      {query.query}
                    </TextShimmer>
                  ) : (
                    query.query
                  )}
                </span>

                {expandedItems[query.id] ? (
                  <ChevronDown className="w-2.5 h-2.5 text-muted-foreground flex-shrink-0 ml-auto" />
                ) : (
                  <ChevronRight className="w-2.5 h-2.5 text-muted-foreground flex-shrink-0 ml-auto" />
                )}
              </div>

              <AnimatePresence>
                {expandedItems[query.id] && (
                  <motion.div
                    initial={{ height: 0, opacity: 0 }}
                    animate={{ height: 'auto', opacity: 1 }}
                    exit={{ height: 0, opacity: 0 }}
                    transition={{
                      height: { duration: 0.2, ease: 'easeOut' },
                      opacity: { duration: 0.15 },
                    }}
                    className="overflow-hidden"
                  >
                    <div className="pl-0.5 py-0.5">
                      {query.sources.length > 0 && (
                        <motion.div
                          className="flex flex-wrap gap-1 py-0.5"
                          initial={{ opacity: 0 }}
                          animate={{ opacity: 1 }}
                          transition={{ duration: 0.15 }}
                        >
                          {query.sources.map((source, index) => (
                            <motion.a
                              key={`${source.url}-${index}`}
                              href={source.url}
                              target="_blank"
                              rel="noopener noreferrer"
                              className="flex items-center gap-1 bg-muted px-1.5 py-0.5 rounded-full text-xs hover:bg-muted/80 transition-colors"
                              initial={{ opacity: 0 }}
                              animate={{ opacity: 1 }}
                              transition={{
                                duration: 0.15,
                                delay: index * 0.02,
                              }}
                            >
                              <img
                                src={source.favicon}
                                alt=""
                                className="w-3 h-3 rounded-full"
                                onError={(e) => {
                                  e.currentTarget.src =
                                    'https://www.google.com/s2/favicons?sz=128&domain=example.com';
                                }}
                              />
                              <span
                                className="text-muted-foreground truncate max-w-[100px]"
                                title={source.title}
                              >
                                {source.title || 'source'}
                              </span>
                            </motion.a>
                          ))}
                        </motion.div>
                      )}

                      {isLoading &&
                        query.sources.length === 0 &&
                        !isCompleted && (
                          <TextShimmer
                            className="text-xs py-0.5"
                            duration={2.5}
                          >
                            Searching sources...
                          </TextShimmer>
                        )}
                    </div>
                  </motion.div>
                )}
              </AnimatePresence>
            </motion.div>
          );
        })}
      </AnimatePresence>
    </div>
  );

  // Render sources section
  const renderSources = (sources: any[]) => (
    <div className="w-full max-w-full">
      <div
        className={cn(
          'py-3 px-4 hover:no-underline group',
          'bg-background',
          'border border-neutral-200 dark:border-neutral-800',
          'cursor-pointer',
          sourcesAccordionOpen ? 'rounded-t-lg' : 'rounded-lg',
        )}
        role="button"
        tabIndex={0}
        onClick={() => setSourcesAccordionOpen(!sourcesAccordionOpen)}
        onKeyDown={(e) => {
          if (e.key === 'Enter' || e.key === ' ') {
            e.preventDefault();
            setSourcesAccordionOpen(!sourcesAccordionOpen);
          }
        }}
      >
        <div className="flex items-center justify-between w-full">
          <div className="flex items-center gap-2">
            <div className="p-1.5 rounded-md bg-neutral-100 dark:bg-neutral-800">
              <Globe className="h-3.5 w-3.5 text-neutral-500" />
            </div>
            <h2 className="font-medium text-sm">Sources</h2>
          </div>
          <div className="flex items-center gap-2">
            <Badge
              variant="secondary"
              className="rounded-full text-xs px-2.5 py-0.5"
            >
              {sources.length}
            </Badge>
            {sources.length > 0 && (
              <Button
                variant="ghost"
                size="sm"
                className="h-7 px-2 text-xs"
                onClick={(e) => {
                  e.stopPropagation();
                  setSourcesSheetOpen(true);
                }}
              >
                View all
                <ArrowUpRight className="w-3 h-3 ml-1" />
              </Button>
            )}
            <ChevronDown
              className={cn(
                'h-4 w-4 text-neutral-500 shrink-0 transition-transform duration-200',
                sourcesAccordionOpen ? 'rotate-180' : '',
              )}
            />
          </div>
        </div>
      </div>

      <AnimatePresence>
        {sourcesAccordionOpen && (
          <motion.div
            initial={{ height: 0, opacity: 0 }}
            animate={{ height: 'auto', opacity: 1 }}
            exit={{ height: 0, opacity: 0 }}
            className={cn(
              'overflow-hidden',
              'bg-background',
              'border-x border-b border-neutral-200 dark:border-neutral-800',
              'rounded-b-lg',
            )}
            style={{ width: '100%', maxWidth: '100%' }}
          >
            <div className="px-3 pt-3 pb-0">
              {sources.length > 0 ? (
                <div className="relative h-[160px]">
                  <div
                    className="absolute inset-0 overflow-x-auto overflow-y-hidden"
                    style={{
                      width: '100%',
                      height: '100%',
                    }}
                  >
                    <div className="flex gap-3 h-full items-start">
                      {sources.map((source) => (
                        <div
                          key={source.url}
                          className="flex-shrink-0"
                          style={{ width: '280px' }}
                        >
                          <a
                            href={source.url}
                            target="_blank"
                            rel="noopener noreferrer"
                            className="block"
                          >
                            <ExtremeSourceCard source={source} />
                          </a>
                        </div>
                      ))}
                    </div>
                  </div>
                </div>
              ) : (
                <p className="text-neutral-500 text-sm">No sources found</p>
              )}
            </div>
          </motion.div>
        )}
      </AnimatePresence>
    </div>
  );

  // Final result view
  if (isCompleted) {
    return (
      <div className="space-y-2 w-full max-w-full overflow-hidden">
        {/* Research Process */}
        <Card className="!p-0 !gap-0 rounded-lg shadow-none">
          <div
            className="flex items-center justify-between p-3 cursor-pointer"
            role="button"
            tabIndex={0}
            onClick={() => setResearchProcessOpen(!researchProcessOpen)}
            onKeyDown={(e) => {
              if (e.key === 'Enter' || e.key === ' ') {
                e.preventDefault();
                setResearchProcessOpen(!researchProcessOpen);
              }
            }}
          >
            <h3 className="font-medium">Research Process</h3>
            {researchProcessOpen ? (
              <ChevronDown className="w-4 h-4" />
            ) : (
              <ChevronRight className="w-4 h-4" />
            )}
          </div>
          <AnimatePresence>
            {researchProcessOpen && (
              <motion.div
                initial={{ height: 0, opacity: 0 }}
                animate={{ height: 'auto', opacity: 1 }}
                exit={{ height: 0, opacity: 0 }}
              >
                <CardContent className="px-4 pb-4">
                  <div className="max-h-[300px] overflow-y-auto pr-1">
                    {renderTimeline()}
                  </div>
                </CardContent>
              </motion.div>
            )}
          </AnimatePresence>
        </Card>

        {/* Final Research Report */}
        {(() => {
          console.log('[ExtremeSearch] Checking for final report...');
          console.log('[ExtremeSearch] toolInvocation:', toolInvocation);
          console.log(
            '[ExtremeSearch] Has output:',
            'output' in toolInvocation,
          );
          console.log(
            '[ExtremeSearch] Has result:',
            'result' in toolInvocation,
          );

          // Try to get research from output first
          if ('output' in toolInvocation) {
            const output = toolInvocation.output as {
              research?: Research;
            } | null;
            console.log('[ExtremeSearch] Output:', output);
            const research = output?.research;
            console.log('[ExtremeSearch] Research:', research);
            console.log('[ExtremeSearch] Research text:', research?.text);

            if (research?.text) {
              console.log(
                '[ExtremeSearch] ✅ Rendering research report from output',
              );
              return (
                <Card className="!p-0 !gap-0 rounded-lg shadow-none">
                  <div
                    className="flex items-center justify-between p-3 cursor-pointer"
                    role="button"
                    tabIndex={0}
                    onClick={() => setResearchReportOpen(!researchReportOpen)}
                    onKeyDown={(e) => {
                      if (e.key === 'Enter' || e.key === ' ') {
                        e.preventDefault();
                        setResearchReportOpen(!researchReportOpen);
                      }
                    }}
                  >
                    <h3 className="font-medium">Research Report</h3>
                    {researchReportOpen ? (
                      <ChevronDown className="w-4 h-4" />
                    ) : (
                      <ChevronRight className="w-4 h-4" />
                    )}
                  </div>
                  <AnimatePresence>
                    {researchReportOpen && (
                      <motion.div
                        initial={{ height: 0, opacity: 0 }}
                        animate={{ height: 'auto', opacity: 1 }}
                        exit={{ height: 0, opacity: 0 }}
                      >
                        <CardContent className="p-4">
                          <div className="prose prose-sm max-w-none dark:prose-invert">
                            <div className="whitespace-pre-wrap text-sm leading-relaxed">
                              {research.text}
                            </div>
                          </div>
                        </CardContent>
                      </motion.div>
                    )}
                  </AnimatePresence>
                </Card>
              );
            }
          }

          // Try to get research from result as fallback
          if ('result' in toolInvocation && toolInvocation.result) {
            console.log('[ExtremeSearch] Result:', toolInvocation.result);
            const result = toolInvocation.result as any;

            // Check if result contains research text directly
            if (typeof result === 'string' && result.length > 50) {
              console.log(
                '[ExtremeSearch] ✅ Rendering research report from result string',
              );
              return (
                <Card className="!p-0 !gap-0 rounded-lg shadow-none">
                  <div
                    className="flex items-center justify-between p-3 cursor-pointer"
                    role="button"
                    tabIndex={0}
                    onClick={() => setResearchReportOpen(!researchReportOpen)}
                    onKeyDown={(e) => {
                      if (e.key === 'Enter' || e.key === ' ') {
                        e.preventDefault();
                        setResearchReportOpen(!researchReportOpen);
                      }
                    }}
                  >
                    <h3 className="font-medium">Research Report</h3>
                    {researchReportOpen ? (
                      <ChevronDown className="w-4 h-4" />
                    ) : (
                      <ChevronRight className="w-4 h-4" />
                    )}
                  </div>
                  <AnimatePresence>
                    {researchReportOpen && (
                      <motion.div
                        initial={{ height: 0, opacity: 0 }}
                        animate={{ height: 'auto', opacity: 1 }}
                        exit={{ height: 0, opacity: 0 }}
                      >
                        <CardContent className="p-4">
                          <div className="prose prose-sm max-w-none dark:prose-invert">
                            <div className="whitespace-pre-wrap text-sm leading-relaxed">
                              {result}
                            </div>
                          </div>
                        </CardContent>
                      </motion.div>
                    )}
                  </AnimatePresence>
                </Card>
              );
            }

            // Check if result has research property
            if (result?.research?.text) {
              console.log(
                '[ExtremeSearch] ✅ Rendering research report from result.research',
              );
              return (
                <Card className="!p-0 !gap-0 rounded-lg shadow-none">
                  <div
                    className="flex items-center justify-between p-3 cursor-pointer"
                    role="button"
                    tabIndex={0}
                    onClick={() => setResearchReportOpen(!researchReportOpen)}
                    onKeyDown={(e) => {
                      if (e.key === 'Enter' || e.key === ' ') {
                        e.preventDefault();
                        setResearchReportOpen(!researchReportOpen);
                      }
                    }}
                  >
                    <h3 className="font-medium">Research Report</h3>
                    {researchReportOpen ? (
                      <ChevronDown className="w-4 h-4" />
                    ) : (
                      <ChevronRight className="w-4 h-4" />
                    )}
                  </div>
                  <AnimatePresence>
                    {researchReportOpen && (
                      <motion.div
                        initial={{ height: 0, opacity: 0 }}
                        animate={{ height: 'auto', opacity: 1 }}
                        exit={{ height: 0, opacity: 0 }}
                      >
                        <CardContent className="p-4">
                          <div className="prose prose-sm max-w-none dark:prose-invert">
                            <div className="whitespace-pre-wrap text-sm leading-relaxed">
                              {result.research.text}
                            </div>
                          </div>
                        </CardContent>
                      </motion.div>
                    )}
                  </AnimatePresence>
                </Card>
              );
            }
          }

          console.log(
            '[ExtremeSearch] ❌ No research text found in output or result',
          );
          return null;
        })()}

        {/* Charts */}
        {(() => {
          if ('output' in toolInvocation) {
            const output = toolInvocation.output as {
              research?: Research;
            } | null;
            const research = output?.research;

            if (research?.charts?.length) {
              return (
                <div className="space-y-3">
                  <h3 className="text-lg font-medium">Visualizations</h3>
                  {research.charts.map((chart, index) => (
                    <div
                      key={`chart-${chart.title || index}`}
                      className="bg-background border border-neutral-200 dark:border-neutral-800 rounded-lg p-4"
                    >
                      <pre className="text-xs overflow-auto">
                        {JSON.stringify(chart, null, 2)}
                      </pre>
                    </div>
                  ))}
                </div>
              );
            }
          }
          return null;
        })()}

        {/* Sources */}
        {(() => {
          console.log(
            '[ExtremeSearch] About to render sources:',
            allSources.length,
          );
          return renderSources(allSources);
        })()}

        <ExtremeSourcesSheet
          sources={allSources}
          open={sourcesSheetOpen}
          onOpenChange={setSourcesSheetOpen}
        />
      </div>
    );
  }

  // In-progress view
  return (
    <Card className="!p-0 !m-0 !gap-0 rounded-lg shadow-none">
      <div className="py-3 px-4 border-b bg-neutral-50 dark:bg-neutral-900 rounded-t-lg">
        <div className="text-sm font-medium">
          {state === 'input-streaming' ? (
            <TextShimmer duration={2}>{currentStatus}</TextShimmer>
          ) : (
            currentStatus
          )}
        </div>
      </div>

      <CardContent className="p-4">
        {searchQueries.length > 0 ? (
          <div className="max-h-[300px] overflow-y-auto pr-1">
            {renderTimeline()}
          </div>
        ) : (
          <div className="space-y-1 relative ml-3">
            {[1, 2, 3].map((i) => (
              <div key={`skeleton-${i}`} className="space-y-0 relative">
                <Skeleton
                  className="absolute size-1 rounded-full z-10"
                  style={{
                    left: '-0.6rem',
                    top: '5.5px',
                    transform: 'translateX(-50%)',
                  }}
                />
                <div className="flex items-center gap-1 py-0.5 px-1 rounded-sm relative min-h-[18px]">
                  <Skeleton className="w-2.5 h-2.5 rounded-full flex-shrink-0" />
                  <Skeleton className="h-3 flex-1" />
                </div>
              </div>
            ))}
          </div>
        )}
      </CardContent>
    </Card>
  );
};

export const ExtremeSearch = memo(ExtremeSearchComponent);
