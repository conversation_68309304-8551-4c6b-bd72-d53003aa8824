import React, { useEffect, useCallback, useRef, useMemo, memo } from 'react';
import { Streamdown } from 'streamdown';

interface MarkdownProps {
  /** Contenu Markdown à rendre */
  children: string;
  /** Classes CSS additionnelles */
  className?: string;
  /** Activer le proxy d'images (défaut: true) */
  enableImageProxy?: boolean;
  /** URL de base pour le proxy d'images */
  proxyBaseUrl?: string;
  /** Callback appelé en cas d'erreur de chargement d'image */
  onImageError?: (originalSrc: string, error: Event) => void;
}

/**
 * Composant pour rendre du Markdown avec gestion avancée des images et styles optimisés
 */
export const Markdown = memo<MarkdownProps>(
  ({
    children,
    className = '',
    enableImageProxy = true,
    proxyBaseUrl = '/api/proxy-image',
    onImageError,
  }) => {
    const containerRef = useRef<HTMLDivElement>(null);
    const processedImages = useRef(new Set<string>());

    /**
     * Gère le proxy des images avec fallback et gestion d'erreur améliorée
     */
    const handleImageProxy = useCallback(() => {
      if (!enableImageProxy || !containerRef.current) return;

      try {
        const images = containerRef.current.querySelectorAll('img');

        images.forEach((imgElement) => {
          const originalSrc = imgElement.src;

          // Éviter de traiter les images déjà proxifiées ou traitées
          if (
            originalSrc.includes(proxyBaseUrl) ||
            processedImages.current.has(originalSrc)
          )
            return;

          // Marquer comme traitée
          processedImages.current.add(originalSrc);

          // Améliorer l'accessibilité
          if (!imgElement.alt) {
            imgElement.alt = 'Image';
          }
          imgElement.setAttribute('loading', 'lazy');

          // Gestionnaire d'erreur amélioré
          const handleError = (event: Event) => {
            if (!imgElement.src.includes(proxyBaseUrl)) {
              let cleanUrl = originalSrc;

              try {
                if (cleanUrl.includes('%')) {
                  cleanUrl = decodeURIComponent(cleanUrl);
                }
              } catch (decodeError) {
                console.warn('Failed to decode URL:', originalSrc, decodeError);
                cleanUrl = originalSrc;
              }

              const proxyUrl = `${proxyBaseUrl}?url=${encodeURIComponent(cleanUrl)}`;
              imgElement.src = proxyUrl;
            } else {
              // Si même le proxy échoue, afficher une image de fallback
              imgElement.style.display = 'none';
              console.warn(
                'Image failed to load even through proxy:',
                originalSrc,
              );
            }

            // Appeler le callback d'erreur si fourni
            onImageError?.(originalSrc, event);
          };

          imgElement.addEventListener('error', handleError, { once: true });

          // Nettoyer l'event listener si l'image se charge correctement
          imgElement.addEventListener(
            'load',
            () => {
              imgElement.removeEventListener('error', handleError);
            },
            { once: true },
          );
        });
      } catch (error) {
        console.error('Error in handleImageProxy:', error);
      }
    }, [enableImageProxy, proxyBaseUrl, onImageError]);

    /**
     * Observe les changements dans le DOM pour traiter les nouvelles images
     */
    useEffect(() => {
      if (!containerRef.current) return;

      const processedImagesRef = processedImages.current;

      const observer = new MutationObserver((mutations) => {
        let hasNewImages = false;

        mutations.forEach((mutation) => {
          if (mutation.type === 'childList') {
            mutation.addedNodes.forEach((node) => {
              if (node.nodeType === Node.ELEMENT_NODE) {
                const element = node as Element;
                if (element.tagName === 'IMG' || element.querySelector('img')) {
                  hasNewImages = true;
                }
              }
            });
          }
        });

        if (hasNewImages) {
          // Délai court pour laisser le DOM se stabiliser
          setTimeout(handleImageProxy, 50);
        }
      });

      observer.observe(containerRef.current, {
        childList: true,
        subtree: true,
      });

      // Traitement initial
      handleImageProxy();

      return () => {
        observer.disconnect();
        processedImagesRef.clear();
      };
    }, [handleImageProxy]);

    /**
     * Styles CSS optimisés et mémorisés
     */
    const styles = useMemo(
      () => ({
        __html: `
      .prose-container {
        max-width: 100% !important;
      }
      
      .prose-container pre {
        overflow-x: auto;
        max-width: 100%;
        width: 100%;
        border-radius: 0.5rem;
        background-color: #1f2937;
        padding: 1rem;
        margin: 1rem 0;
        box-sizing: border-box;
        word-wrap: break-word;
        overflow-wrap: break-word;
        position: relative;
      }
      
      .prose-container code {
        background-color: #374151;
        color: #f9fafb;
        padding: 0.125rem 0.25rem;
        border-radius: 0.25rem;
        font-size: 0.875rem;
        word-break: break-words;
        font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
      }
      
      .prose-container pre code {
        background-color: transparent;
        padding: 0;
        color: #f9fafb;
        white-space: pre-wrap;
        overflow-x: auto;
        display: block;
        word-break: break-all;
        overflow-wrap: anywhere;
        max-width: 100%;
      }
      
      /* Syntax highlighting amélioré */
      .prose-container pre code .hljs-keyword { color: #c678dd; font-weight: 500; }
      .prose-container pre code .hljs-string { color: #98c379; }
      .prose-container pre code .hljs-number { color: #d19a66; }
      .prose-container pre code .hljs-comment { color: #5c6370; font-style: italic; opacity: 0.8; }
      .prose-container pre code .hljs-function { color: #61afef; font-weight: 500; }
      .prose-container pre code .hljs-variable { color: #e06c75; }
      .prose-container pre code .hljs-built_in { color: #e5c07b; font-weight: 500; }
      .prose-container pre code .hljs-tag { color: #e06c75; }
      .prose-container pre code .hljs-attr { color: #d19a66; }
      .prose-container pre code .hljs-attribute { color: #d19a66; }
      .prose-container pre code .hljs-title { color: #61afef; font-weight: 600; }
      .prose-container pre code .hljs-type { color: #e5c07b; }
      .prose-container pre code .hljs-literal { color: #56b6c2; }
      .prose-container pre code .hljs-meta { color: #abb2bf; }
      .prose-container pre code .hljs-selector-tag { color: #e06c75; }
      .prose-container pre code .hljs-selector-class { color: #e5c07b; }
      .prose-container pre code .hljs-selector-id { color: #61afef; }
      
      /* Images avec gestion d'erreur et performance améliorées */
      .prose-container img {
        max-width: 100%;
        height: auto;
        border-radius: 0.5rem;
        box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
        margin: 1rem 0;
        display: block;
        transition: opacity 0.3s ease, transform 0.2s ease;
      }
      
      .prose-container img:hover {
        transform: scale(1.02);
        box-shadow: 0 8px 12px -2px rgba(0, 0, 0, 0.15), 0 4px 6px -1px rgba(0, 0, 0, 0.1);
      }
      
      /* Placeholder pour images en cours de chargement */
      .prose-container img[src*="${proxyBaseUrl}"] {
        background: linear-gradient(45deg, #374151, #4b5563);
        background-size: 400% 400%;
        animation: gradientShift 2s ease-in-out infinite alternate;
        min-height: 200px;
      }
      
      @keyframes gradientShift {
        0% { background-position: 0% 50%; }
        100% { background-position: 100% 50%; }
      }
      
      /* Tableaux améliorés */
      .prose-container table {
        width: 100%;
        border-collapse: collapse;
        margin: 1.5rem 0;
        background-color: #1f2937;
        border-radius: 0.5rem;
        overflow: hidden;
        box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
      }
      
      .prose-container th {
        background-color: #374151;
        color: #f9fafb;
        padding: 1rem 0.75rem;
        text-align: left;
        font-weight: 600;
        border-bottom: 2px solid #4b5563;
        position: relative;
      }
      
      .prose-container td {
        padding: 0.75rem;
        border-bottom: 1px solid #374151;
        color: #d1d5db;
        transition: background-color 0.2s ease;
      }
      
      .prose-container tr:hover td {
        background-color: #374151;
      }
      
      .prose-container tr:last-child td {
        border-bottom: none;
      }
      
      /* Titres avec hiérarchie visuelle améliorée */
      .prose-container h1, .prose-container h2, .prose-container h3, 
      .prose-container h4, .prose-container h5, .prose-container h6 {
        color: #f9fafb;
        font-weight: 600;
        margin-top: 2rem;
        margin-bottom: 1rem;
        line-height: 1.25;
        scroll-margin-top: 2rem;
      }
      
      .prose-container h1 { 
        font-size: 2rem; 
        border-bottom: 2px solid #4b5563; 
        padding-bottom: 0.5rem;
      }
      .prose-container h2 { 
        font-size: 1.5rem; 
        color: #e5e7eb;
      }
      .prose-container h3 { 
        font-size: 1.25rem; 
        color: #d1d5db;
      }
      
      /* Listes améliorées */
      .prose-container ul, .prose-container ol {
        padding-left: 1.5rem;
        margin: 1rem 0;
      }
      
      .prose-container li {
        margin: 0.5rem 0;
        color: #d1d5db;
        line-height: 1.6;
      }
      
      /* Blockquotes */
      .prose-container blockquote {
        border-left: 4px solid #4b5563;
        padding-left: 1rem;
        margin: 1.5rem 0;
        font-style: italic;
        color: #9ca3af;
        background-color: #1f2937;
        border-radius: 0 0.25rem 0.25rem 0;
        padding: 1rem;
      }
      
      /* Liens */
      .prose-container a {
        color: #60a5fa;
        text-decoration: none;
        transition: color 0.2s ease;
      }
      
      .prose-container a:hover {
        color: #93c5fd;
        text-decoration: underline;
      }
      
      /* Responsive design */
      @media (max-width: 768px) {
        .prose-container {
          padding: 0.5rem;
        }
        
        .prose-container pre {
          padding: 0.75rem;
          border-radius: 0.25rem;
        }
        
        .prose-container h1 { font-size: 1.75rem; }
        .prose-container h2 { font-size: 1.5rem; }
        
        .prose-container table {
          font-size: 0.875rem;
        }
        
        .prose-container th, .prose-container td {
          padding: 0.5rem;
        }
      }
    `,
      }),
      [proxyBaseUrl],
    );

    return (
      <div
        ref={containerRef}
        className={`prose prose-invert max-w-full break-words prose-container ${className}`}
        role="article"
        aria-label="Contenu Markdown"
      >
        <style dangerouslySetInnerHTML={styles} />
        <Streamdown>{children}</Streamdown>
      </div>
    );
  },
);

// Définir le displayName pour le debugging
Markdown.displayName = 'Markdown';
