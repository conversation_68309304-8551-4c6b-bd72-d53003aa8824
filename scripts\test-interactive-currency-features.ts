/**
 * Test script pour vérifier les nouvelles fonctionnalités interactives :
 * - Carte interactive avec filtres
 * - <PERSON>ux d'achat des cartes de transport avec images
 * - ATMs sans frais et près des attractions
 * - Zones à risque et zones sûres
 * - Itinéraires optimisés
 */

import { CurrencyAgent } from '../lib/ai/workflows/agents/currency-agent';
import { openai } from '@ai-sdk/openai';
import type { DestinationInfo } from '../lib/ai/workflows/types';

async function testInteractiveCurrencyFeatures() {
  console.log('🧪 TEST: Fonctionnalités interactives de devise\n');

  // Configuration du test
  const model = openai('gpt-4o-mini');
  const currencyAgent = new CurrencyAgent(model);

  // Destination de test - Beijing pour tester les zones à risque
  const destinationInfo: DestinationInfo = {
    destination: 'Beijing',
    country: 'China',
    duration: 5,
    coordinates: {
      lat: '39.9042',
      lng: '116.4074',
    },
  };

  try {
    console.log('🔍 Récupération des informations interactives...\n');
    const startTime = Date.now();

    const currencyInfo = await currencyAgent.getCurrencyForDestination(
      destinationInfo,
    );

    const endTime = Date.now();
    console.log(`⏱️ Temps d'exécution: ${endTime - startTime}ms\n`);

    // Test des nouvelles fonctionnalités
    console.log('📊 VÉRIFICATION DES FONCTIONNALITÉS INTERACTIVES:\n');

    // 1. ATMs sans frais et près des attractions
    console.log('1. 🏧 ATMs optimisés:');
    if (currencyInfo.exchangeInfo?.atmInfo?.freeAtmLocations) {
      console.log(`   💰 ATMs sans frais: ${currencyInfo.exchangeInfo.atmInfo.freeAtmLocations}`);
    }
    if (currencyInfo.exchangeInfo?.atmInfo?.nearTouristSites) {
      console.log(`   📍 Près des attractions: ${currencyInfo.exchangeInfo.atmInfo.nearTouristSites}`);
    }
    console.log('');

    // 2. Cartes de transport avec lieux d'achat et images
    console.log('2. 🚌 Cartes de transport détaillées:');
    if (currencyInfo.paymentMethods?.transportCards) {
      console.log(`   Cartes: ${currencyInfo.paymentMethods.transportCards.available.join(', ')}`);
      console.log(`   📍 Où acheter: ${currencyInfo.paymentMethods.transportCards.purchaseLocations}`);
      if (currencyInfo.paymentMethods.transportCards.cardImages) {
        console.log(`   🎴 Images: ${currencyInfo.paymentMethods.transportCards.cardImages}`);
      }
    }
    console.log('');

    // 3. Zones de sécurité et zones à risque
    console.log('3. 🛡️ Zones de sécurité:');
    if (currencyInfo.safetyAndPrecautions?.localSafety) {
      if (currencyInfo.safetyAndPrecautions.localSafety.safeZones) {
        console.log(`   ✅ Zones sûres: ${currencyInfo.safetyAndPrecautions.localSafety.safeZones}`);
      }
      if (currencyInfo.safetyAndPrecautions.localSafety.riskZones) {
        console.log(`   ⚠️ Zones à risque: ${currencyInfo.safetyAndPrecautions.localSafety.riskZones}`);
      }
    }
    console.log('');

    // 4. Informations de localisation interactive
    console.log('4. 🗺️ Informations de localisation:');
    if (currencyInfo.interactiveLocations) {
      if (currencyInfo.interactiveLocations.atmLocations) {
        console.log(`   🏧 Emplacements ATM: ${currencyInfo.interactiveLocations.atmLocations}`);
      }
      if (currencyInfo.interactiveLocations.paymentAcceptance) {
        console.log(`   💳 Acceptation paiements: ${currencyInfo.interactiveLocations.paymentAcceptance}`);
      }
      if (currencyInfo.interactiveLocations.optimizedRoutes) {
        console.log(`   🗺️ Itinéraires optimisés: ${currencyInfo.interactiveLocations.optimizedRoutes}`);
      }
    }
    console.log('');

    // 5. Résumé des fonctionnalités interactives
    console.log('📊 RÉSUMÉ DES FONCTIONNALITÉS INTERACTIVES:\n');
    
    const interactiveFeatures = [];
    
    if (currencyInfo.exchangeInfo?.atmInfo?.freeAtmLocations) {
      interactiveFeatures.push('✅ ATMs sans frais localisés');
    }
    
    if (currencyInfo.exchangeInfo?.atmInfo?.nearTouristSites) {
      interactiveFeatures.push('✅ ATMs près des attractions');
    }
    
    if (currencyInfo.paymentMethods?.transportCards?.purchaseLocations) {
      interactiveFeatures.push('✅ Lieux d\'achat cartes transport');
    }
    
    if (currencyInfo.paymentMethods?.transportCards?.cardImages) {
      interactiveFeatures.push('✅ Images des cartes transport');
    }
    
    if (currencyInfo.safetyAndPrecautions?.localSafety?.safeZones) {
      interactiveFeatures.push('✅ Zones sûres identifiées');
    }
    
    if (currencyInfo.safetyAndPrecautions?.localSafety?.riskZones) {
      interactiveFeatures.push('✅ Zones à risque identifiées');
    }
    
    if (currencyInfo.interactiveLocations?.optimizedRoutes) {
      interactiveFeatures.push('✅ Itinéraires optimisés');
    }

    interactiveFeatures.forEach(feature => console.log(`   ${feature}`));
    
    if (interactiveFeatures.length === 0) {
      console.log('   ❌ Aucune fonctionnalité interactive détectée');
    }

    console.log('\n🎯 EXEMPLES ATTENDUS POUR LA CARTE INTERACTIVE:');
    
    console.log('\n💰 Filtres ATM sans frais:');
    console.log('   "Bank of China ATM - 123 Wangfujing Street - No fees for international cards"');
    console.log('   "ICBC ATM - Beijing Railway Station Exit A - Free withdrawals"');
    
    console.log('\n📱 Filtres Alipay accepté:');
    console.log('   "Silk Street Market 2nd floor - All shops accept Alipay"');
    console.log('   "Donghuamen Night Market - Cash only, no mobile payments"');
    
    console.log('\n🚌 Cartes de transport avec images:');
    console.log('   Available: Beijing Subway Card, Yikatong');
    console.log('   📍 Where to Buy: Subway stations, airports, convenience stores');
    console.log('   🎴 Images: Beijing Card - red dragon design, Yikatong - blue/white modern design');
    
    console.log('\n🛡️ Zones de sécurité sur la carte:');
    console.log('   ✅ Safe: Financial district, hotel lobbies, airport terminals');
    console.log('   ⚠️ Risk: Silk Street market (currency scams), Wangfujing (pickpockets)');
    
    console.log('\n🗺️ Itinéraires optimisés:');
    console.log('   "From Tiananmen Square: nearest fee-free ATM 300m east at Bank of China"');
    console.log('   "Then 200m south to Alipay-friendly restaurants on Qianmen Street"');

    console.log('\n🚀 FONCTIONNALITÉS DE LA CARTE INTERACTIVE:');
    console.log('   • 🔍 Filtres: Fee-Free ATMs, Open Now, Alipay Accepted, Cash Only, Safe Zones');
    console.log('   • 📍 Marqueurs: ATMs, restaurants, zones sûres, zones à risque');
    console.log('   • 🗺️ Itinéraires: Chemins optimisés entre ATMs et destinations');
    console.log('   • 📱 Interactivité: Clic sur marqueurs pour détails, filtres actifs/inactifs');
    console.log('   • 🎯 Contextuel: "Tu es près de X ? Voici les 3 ATMs les plus proches"');

  } catch (error) {
    console.error('❌ Erreur lors du test:', error);
  }
}

// Exécuter le test si le script est appelé directement
if (require.main === module) {
  testInteractiveCurrencyFeatures();
}

export { testInteractiveCurrencyFeatures };
