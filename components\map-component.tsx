'use client';

import React, { useEffect, useRef, useState, useCallback, useId } from 'react';
import type { Place, PlaceImage } from '@/lib/types';
import EnhancedLocationDisplay from './enhanced-location-display';
import ImageModal from './ImageModal';
import { useArtifactSelector } from '@/hooks/use-artifact';
import { useIsMobile } from '@/hooks/use-mobile';

interface MapComponentProps {
  places: Place[];
  query: string;
}

const MapComponent: React.FC<MapComponentProps> = ({ places, query }) => {
  const mapRef = useRef<HTMLDivElement>(null);
  const mapInstanceRef = useRef<any>(null);
  const markersRef = useRef<any[]>([]);
  const containerRef = useRef<HTMLDivElement>(null);
  const resizeTimeoutRef = useRef<NodeJS.Timeout | null>(null);

  const [isMapReady, setIsMapReady] = useState(false);
  const [selectedPlaceId, setSelectedPlaceId] = useState<string | null>(null);
  const [useElevatorMode, setUseElevatorMode] = useState(false); // Activer le mode liste par défaut
  const [forceMapReinit, setForceMapReinit] = useState(0); // Compteur pour forcer la réinitialisation

  // États pour le modal d'image
  const [isImageModalOpen, setIsImageModalOpen] = useState(false);
  const [modalImages, setModalImages] = useState<PlaceImage[]>([]);
  const [currentImageIndex, setCurrentImageIndex] = useState(0);
  const [modalPlaceName, setModalPlaceName] = useState('');

  // Utiliser uniquement les places avec images Serper (pas de fallback)

  // Vérifier si un artifact est visible
  const isArtifactVisible = useArtifactSelector((state) => state.isVisible);

  // SOLUTION RADICALE: Ignorer complètement l'état de l'artifact pour la carte
  const [ignoreArtifactState, setIgnoreArtifactState] = useState(false);

  // Détecter si on est sur mobile
  const isMobile = useIsMobile();

  // Générer un ID stable et compatible SSR/CSR pour cette instance de carte
  const reactId = useId();
  const mapId = `map-${reactId}`;

  // Fonction pour gérer le redimensionnement de la carte avec debounce
  const handleMapResize = useCallback(() => {
    if (resizeTimeoutRef.current) {
      clearTimeout(resizeTimeoutRef.current);
    }

    resizeTimeoutRef.current = setTimeout(() => {
      try {
        if (
          mapInstanceRef.current?.getContainer() &&
          document.contains(mapInstanceRef.current.getContainer())
        ) {
          console.log(`Map ${mapId}: Handling resize`);
          mapInstanceRef.current.invalidateSize();
        }
      } catch (resizeError) {
        console.warn(`Map ${mapId}: Error during resize:`, resizeError);
      }
    }, 150);
  }, [mapId]);

  // Observateur de redimensionnement pour détecter les changements de taille du conteneur
  const resizeObserverRef = useRef<ResizeObserver | null>(null);

  const setupResizeObserver = useCallback(() => {
    if (
      typeof window !== 'undefined' &&
      'ResizeObserver' in window &&
      containerRef.current
    ) {
      // Nettoyer l'observateur existant
      if (resizeObserverRef.current) {
        resizeObserverRef.current.disconnect();
      }

      resizeObserverRef.current = new ResizeObserver((entries) => {
        for (const entry of entries) {
          console.log(
            `Map ${mapId}: Container size changed, triggering resize`,
          );
          handleMapResize();
        }
      });

      resizeObserverRef.current.observe(containerRef.current);
      console.log(`Map ${mapId}: ResizeObserver setup completed`);
    }
  }, [mapId, handleMapResize]);

  // Fonctions pour gérer le modal d'image
  const openImageModal = useCallback(
    (images: PlaceImage[], index: number, placeName: string) => {
      setModalImages(images);
      setCurrentImageIndex(index);
      setModalPlaceName(placeName);
      setIsImageModalOpen(true);
    },
    [],
  );

  const closeImageModal = useCallback(() => {
    setIsImageModalOpen(false);
  }, []);

  const goToPreviousImage = useCallback(() => {
    setCurrentImageIndex((prev) =>
      prev > 0 ? prev - 1 : modalImages.length - 1,
    );
  }, [modalImages.length]);

  const goToNextImage = useCallback(() => {
    setCurrentImageIndex((prev) =>
      prev < modalImages.length - 1 ? prev + 1 : 0,
    );
  }, [modalImages.length]);

  const selectImage = useCallback((index: number) => {
    setCurrentImageIndex(index);
  }, []);

  // Fonction pour centrer la carte sur un lieu spécifique
  const handleLocationClick = useCallback(
    (place: Place) => {
      const map = mapInstanceRef.current;
      if (!map) return;

      console.log(`Centering map on: ${place.title}`);
      setSelectedPlaceId(place.cid);

      // Centrer la carte sur le lieu sélectionné uniquement quand la carte est prête
      if (map._loaded) {
        map.setView([place.latitude, place.longitude], 15, { animate: false });
      } else if (typeof map.whenReady === 'function') {
        map.whenReady(() => {
          try {
            map.setView([place.latitude, place.longitude], 15, {
              animate: false,
            });
          } catch (e) {
            console.warn('setView failed (map not ready yet):', e);
          }
        });
      }

      // Trouver et ouvrir le popup du marker correspondant
      const markerIndex = places.findIndex((p) => p.cid === place.cid);
      if (markerIndex !== -1 && markersRef.current[markerIndex]) {
        markersRef.current[markerIndex].openPopup();
      }
    },
    [places],
  );

  // Fonction pour gérer le clic sur un marker
  const handleMarkerClick = useCallback((place: Place) => {
    console.log(`Marker clicked: ${place.title}`);
    setSelectedPlaceId(place.cid);
  }, []);

  // Fonction pour initialiser la carte avec useCallback pour éviter les dépendances circulaires
  const initializeMap = useCallback(() => {
    console.log('Initializing map...');

    const L = window.L;
    if (!L) {
      console.error('Leaflet not loaded');
      return;
    }

    if (!mapRef.current) {
      console.error('Map container ref is not available');
      return;
    }

    try {
      // Cleanup any existing map instance
      if (mapInstanceRef.current) {
        console.log('Cleaning up existing map instance');
        try {
          mapInstanceRef.current.remove();
        } catch (cleanupError) {
          console.warn('Error during map cleanup:', cleanupError);
        }
        mapInstanceRef.current = null;
      }

      // Vérifier que le conteneur est toujours dans le DOM
      if (!document.contains(mapRef.current)) {
        console.error('Map container is not in the DOM');
        return;
      }

      // Get the first place coordinates or default to Paris
      const firstPlace = places[0];
      const initialLat = firstPlace?.latitude || 48.8566;
      const initialLng = firstPlace?.longitude || 2.3522;

      console.log(`Setting initial view to: [${initialLat}, ${initialLng}]`);

      // Create the map with mobile-responsive options
      const map = L.map(mapRef.current, {
        // Améliorer l'expérience mobile
        tap: true,
        touchZoom: true,
        zoomControl: !isMobile, // Masquer les contrôles de zoom sur mobile pour économiser l'espace
        attributionControl: !isMobile, // Masquer l'attribution sur mobile pour économiser l'espace
        // Désactiver les animations pour éviter les erreurs de position
        zoomAnimation: false,
        fadeAnimation: false,
        markerZoomAnimation: false,
      }).setView([initialLat, initialLng], isMobile ? 12 : 13);
      mapInstanceRef.current = map;

      // Ajouter les contrôles de zoom personnalisés pour mobile
      if (isMobile) {
        L.control
          .zoom({
            position: 'bottomright',
          })
          .addTo(map);

        // Attribution compacte pour mobile
        L.control
          .attribution({
            position: 'bottomleft',
            prefix: false,
          })
          .addTo(map);
      }

      // Add the OpenStreetMap tiles
      L.tileLayer('https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png', {
        attribution:
          '&copy; <a href="https://www.openstreetmap.org/copyright">OpenStreetMap</a> contributors',
        maxZoom: 19,
      }).addTo(map);

      // Add markers for all places
      markersRef.current = places.map((place, placeIndex) => {
        // Créer le contenu des images si disponibles
        const imagesContent =
          place.images && place.images.length > 0
            ? `
            <div style="margin: 12px 0;">
              <div style="display: flex; gap: 8px; overflow-x: auto; max-width: 100%; padding: 4px 0;">
                ${place.images
                  .slice(0, 3)
                  .map(
                    (image, imageIndex) => `
                  <div style="flex-shrink: 0;">
                    <img
                      src="${image.thumbnail || image.url}"
                      alt="${image.description || place.title}"
                      style="
                        width: 80px;
                        height: 60px;
                        object-fit: cover;
                        border-radius: 6px;
                        border: 1px solid #ddd;
                        cursor: pointer;
                      "
                      data-place-index="${placeIndex}"
                      data-image-index="${imageIndex}"
                      class="popup-image-thumbnail"
                      title="${image.description || place.title}"
                    />
                  </div>
                `,
                  )
                  .join('')}
                ${
                  place.images.length > 3
                    ? `
                  <div
                    style="
                      flex-shrink: 0;
                      width: 80px;
                      height: 60px;
                      background: #f0f0f0;
                      border-radius: 6px;
                      display: flex;
                      align-items: center;
                      justify-content: center;
                      font-size: 12px;
                      color: #666;
                      border: 1px solid #ddd;
                      cursor: pointer;
                    "
                    data-place-index="${placeIndex}"
                    data-image-index="0"
                    class="popup-image-more"
                    title="Voir toutes les images"
                  >
                    +${place.images.length - 3}
                  </div>
                `
                    : ''
                }
              </div>
            </div>
          `
            : '';

        // Créer un contenu de popup adaptatif pour mobile
        const popupContent = isMobile
          ? `
          <div style="min-width: 200px; max-width: 280px;">
            <strong style="font-size: 16px; line-height: 1.4;">${place.title}</strong><br>
            <div style="font-size: 14px; line-height: 1.3; margin: 8px 0;">
              ${place.address}
            </div>
            <div style="font-size: 13px; color: #666; margin: 4px 0;">
              ${place.category}
            </div>
            ${place.rating ? `<div style="font-size: 13px; margin: 4px 0;">⭐ ${place.rating}/5</div>` : ''}
            ${imagesContent}
            ${place.phoneNumber ? `<div style="margin: 8px 0;"><a href="tel:${place.phoneNumber}" style="color: #007AFF; text-decoration: none; font-size: 14px;">📞 Call</a></div>` : ''}
            ${place.website ? `<div style="margin: 8px 0;"><a href="${place.website}" target="_blank" style="color: #007AFF; text-decoration: none; font-size: 14px;">🌐 Website</a></div>` : ''}
          </div>
        `
          : `
          <div style="min-width: 250px; max-width: 350px;">
            <strong style="font-size: 18px;">${place.title}</strong><br>
            <div style="margin: 8px 0;">${place.address}</div>
            <div style="color: #666; margin: 4px 0;">${place.category}</div>
            ${place.rating ? `<div style="margin: 4px 0;">⭐ ${place.rating}/5</div>` : ''}
            ${imagesContent}
            ${place.phoneNumber ? `<div style="margin: 8px 0;">📞 ${place.phoneNumber}</div>` : ''}
            ${place.website ? `<div style="margin: 8px 0;"><a href="${place.website}" target="_blank" style="color: #007AFF;">🌐 Website</a></div>` : ''}
          </div>
        `;

        const marker = L.marker([place.latitude, place.longitude])
          .addTo(map)
          .bindPopup(popupContent, {
            maxWidth: isMobile ? 250 : 300,
            className: isMobile ? 'mobile-popup' : '',
          });

        // Ajouter un gestionnaire d'événement pour le clic sur le marker
        marker.on('click', () => {
          handleMarkerClick(place);
        });

        // Ajouter des event listeners pour les images dans les popups
        marker.on('popupopen', () => {
          console.log('Popup opened, setting up image listeners');

          // Utiliser un délai plus long et une approche plus simple
          setTimeout(() => {
            const popupElement = marker.getPopup()?.getElement();
            if (!popupElement) {
              console.log('Popup element not found');
              return;
            }

            console.log('Popup element found, setting up listeners');

            // Utiliser la délégation d'événements sur le conteneur du popup
            const handlePopupClick = (e: Event) => {
              const target = e.target as HTMLElement;

              // Vérifier si c'est une image miniature
              if (target.classList.contains('popup-image-thumbnail')) {
                console.log('Image thumbnail clicked via delegation!');
                e.preventDefault();
                e.stopPropagation();

                const placeIndex = Number.parseInt(
                  target.getAttribute('data-place-index') || '0',
                );
                const imageIndex = Number.parseInt(
                  target.getAttribute('data-image-index') || '0',
                );

                console.log(
                  `Opening image modal for place ${placeIndex}, image ${imageIndex}`,
                );

                const targetPlace = places[placeIndex];
                if (targetPlace?.images) {
                  openImageModal(
                    targetPlace.images,
                    imageIndex,
                    targetPlace.title,
                  );
                }
                return;
              }

              // Vérifier si c'est le bouton "plus d'images"
              if (target.classList.contains('popup-image-more')) {
                console.log('More images button clicked via delegation!');
                e.preventDefault();
                e.stopPropagation();

                const placeIndex = Number.parseInt(
                  target.getAttribute('data-place-index') || '0',
                );

                console.log(
                  `Opening image modal for place ${placeIndex}, all images`,
                );

                const targetPlace = places[placeIndex];
                if (targetPlace?.images) {
                  openImageModal(targetPlace.images, 0, targetPlace.title);
                }
                return;
              }
            };

            // Ajouter l'event listener au conteneur du popup
            popupElement.addEventListener('click', handlePopupClick, true);

            // Nettoyer l'event listener quand le popup se ferme
            marker.on('popupclose', () => {
              popupElement.removeEventListener('click', handlePopupClick, true);
            });
          }, 200); // Délai plus long pour s'assurer que le popup est complètement rendu
        });

        return marker;
      });

      // If we have multiple markers, fit the map to show all of them
      if (markersRef.current.length > 1) {
        const group = L.featureGroup(markersRef.current);
        // Ajuster le padding selon la taille de l'écran
        const padding = isMobile ? 0.2 : 0.1;
        // Désactiver l'animation pour éviter setZoomAround/_leaflet_pos avant init complète
        map.fitBounds(group.getBounds().pad(padding), { animate: false });
      }

      // Ajouter un gestionnaire de redimensionnement pour la fenêtre
      const resizeHandler = () => handleMapResize();
      window.addEventListener('resize', resizeHandler);
      window.addEventListener('orientationchange', resizeHandler);

      // Configurer l'observateur de redimensionnement
      setupResizeObserver();

      // Force a resize to ensure the map renders correctly
      setTimeout(() => {
        try {
          if (map?.getContainer() && document.contains(map.getContainer())) {
            map.invalidateSize();
          }
        } catch (resizeError) {
          console.warn('Error during map resize:', resizeError);
        }
      }, 100);

      // Nettoyer les gestionnaires d'événements lors de la destruction de la carte
      map.on('unload', () => {
        window.removeEventListener('resize', resizeHandler);
        window.removeEventListener('orientationchange', resizeHandler);

        // Nettoyer l'observateur de redimensionnement
        if (resizeObserverRef.current) {
          resizeObserverRef.current.disconnect();
          resizeObserverRef.current = null;
        }
      });
    } catch (error) {
      console.error('Error initializing map:', error);
    }
  }, [places, isMobile, handleMapResize, handleMarkerClick, openImageModal, setupResizeObserver]);

  // Effet pour charger Leaflet et initialiser la carte
  useEffect(() => {
    // Attendre que le DOM soit prêt
    const waitForDOM = () => {
      if (typeof window === 'undefined') {
        console.log('Window not defined yet, waiting...');
        setTimeout(waitForDOM, 100);
        return;
      }

      console.log('Window is defined, proceeding with map initialization');
      loadLeafletResources();
    };

    // Charger les ressources Leaflet
    const loadLeafletResources = () => {
      console.log('Loading Leaflet resources...');

      // Load Leaflet CSS
      if (!document.querySelector('link[href*="leaflet.css"]')) {
        const link = document.createElement('link');
        link.rel = 'stylesheet';
        link.href = 'https://unpkg.com/leaflet@1.9.4/dist/leaflet.css';
        link.integrity = 'sha256-p4NxAoJBhIIN+hmNHrzRCf9tD/miZyoHS5obTRR9BMY=';
        link.crossOrigin = '';
        document.head.appendChild(link);
      }

      // Load Leaflet JS
      if (typeof window !== 'undefined' && !window.L) {
        console.log('Loading Leaflet script...');
        const script = document.createElement('script');
        script.src = 'https://unpkg.com/leaflet@1.9.4/dist/leaflet.js';
        script.crossOrigin = '';
        script.onload = () => {
          console.log('Leaflet script loaded successfully');
          setIsMapReady(true);
        };
        script.onerror = (e) => {
          console.error('Error loading Leaflet script:', e);
        };
        document.head.appendChild(script);
      } else {
        console.log('Leaflet already loaded');
        setIsMapReady(true);
      }
    };

    waitForDOM();

    // Cleanup function
    return () => {
      // Nettoyer le timeout de redimensionnement
      if (resizeTimeoutRef.current) {
        clearTimeout(resizeTimeoutRef.current);
        resizeTimeoutRef.current = null;
      }

      // Nettoyer l'observateur de redimensionnement
      if (resizeObserverRef.current) {
        console.log(`Map ${mapId}: Disconnecting ResizeObserver on unmount`);
        resizeObserverRef.current.disconnect();
        resizeObserverRef.current = null;
      }

      if (mapInstanceRef.current) {
        console.log(`Map ${mapId}: Cleaning up map instance on unmount`);
        try {
          // Supprimer tous les event listeners avant de détruire la carte
          mapInstanceRef.current.off();
          // Supprimer tous les layers et markers
          mapInstanceRef.current.eachLayer((layer: any) => {
            mapInstanceRef.current.removeLayer(layer);
          });
          // Détruire la carte
          mapInstanceRef.current.remove();
        } catch (cleanupError) {
          console.warn(`Map ${mapId}: Error during cleanup:`, cleanupError);
        } finally {
          mapInstanceRef.current = null;
        }
      }

      // Supprimer les styles spécifiques à cette carte
      const styleElement = document.getElementById(`map-style-${mapId}`);
      if (styleElement) {
        console.log(`Map ${mapId}: Removing style element on unmount`);
        styleElement.remove();
      }
    };
  }, [mapId]);

  // Effet pour gérer la carte lorsqu'un artifact est ouvert/fermé
  useEffect(() => {
    console.log(
      `Map ${mapId}: Artifact visibility changed - isArtifactVisible: ${isArtifactVisible}`,
    );

    if (containerRef.current) {
      // SOLUTION RADICALE: Toujours afficher la carte, ignorer l'état de l'artifact
      containerRef.current.style.display = 'block';
      containerRef.current.style.visibility = 'visible';
      containerRef.current.style.opacity = '1';
      containerRef.current.style.position = 'relative';
      containerRef.current.style.zIndex = '1';
      console.log(`Map ${mapId}: Container forced to be visible`);

      // SOLUTION SIMPLIFIÉE: Ignorer complètement l'état de l'artifact
      console.log(
        `Map ${mapId}: Ensuring map is always visible and initialized`,
      );

      // Toujours ajouter les styles nécessaires
      containerRef.current.classList.add('map-always-visible');
      containerRef.current.setAttribute('data-map-id', mapId);

      // Ajouter un style permanent pour la carte
      let styleElement = document.getElementById(`map-style-${mapId}`);
      if (!styleElement) {
        styleElement = document.createElement('style');
        styleElement.id = `map-style-${mapId}`;
        styleElement.textContent = `
          /* Style permanent pour cette instance de carte */
          .map-always-visible[data-map-id="${mapId}"] {
            position: relative !important;
            z-index: 10 !important;
            display: block !important;
            visibility: visible !important;
            opacity: 1 !important;
          }

          /* Styles responsifs pour les popups mobiles */
          .mobile-popup .leaflet-popup-content-wrapper {
            border-radius: 8px;
            box-shadow: 0 4px 12px rgba(0,0,0,0.15);
          }

          .mobile-popup .leaflet-popup-content {
            margin: 12px 16px;
            line-height: 1.4;
          }

          /* Améliorer les contrôles sur mobile */
          @media (max-width: 768px) {
            .leaflet-control-zoom {
              border: none;
              box-shadow: 0 2px 8px rgba(0,0,0,0.2);
            }

            .leaflet-control-zoom a {
              width: 36px;
              height: 36px;
              line-height: 36px;
              font-size: 18px;
              border-radius: 6px;
              margin: 2px;
            }

            .leaflet-control-attribution {
              font-size: 10px;
              background: rgba(255,255,255,0.8);
              border-radius: 4px;
              padding: 2px 4px;
            }
          }
        `;
        document.head.appendChild(styleElement);
      }

      // Toujours s'assurer que la carte est redimensionnée correctement
      if (mapInstanceRef.current) {
        setTimeout(() => {
          try {
            if (
              mapInstanceRef.current?.getContainer() &&
              document.contains(mapInstanceRef.current.getContainer())
            ) {
              console.log(`Map ${mapId}: Forcing map resize`);
              mapInstanceRef.current.invalidateSize();
            }
          } catch (resizeError) {
            console.warn(`Map ${mapId}: Error during resize:`, resizeError);
          }
        }, 100);
      }

      // Si la carte n'est pas initialisée et que les conditions sont réunies, l'initialiser
      // IMPORTANT: Initialiser la carte même si l'artifact n'est pas visible
      if (isMapReady && places.length > 0 && !mapInstanceRef.current) {
        console.log(
          `Map ${mapId}: Initializing map (artifact visible: ${isArtifactVisible})`,
        );
        initializeMap();
      }

      // Si la carte est déjà initialisée, s'assurer qu'elle est correctement dimensionnée
      if (mapInstanceRef.current) {
        // Forcer un redimensionnement de la carte pour s'assurer qu'elle s'affiche correctement
        setTimeout(() => {
          try {
            if (
              mapInstanceRef.current?.getContainer() &&
              document.contains(mapInstanceRef.current.getContainer())
            ) {
              console.log(
                `Map ${mapId}: Invalidating map size to ensure proper display`,
              );
              mapInstanceRef.current.invalidateSize();
            } else {
              console.warn(
                `Map ${mapId}: Map container not found, marking for reinit`,
              );
              mapInstanceRef.current = null;
            }
          } catch (resizeError) {
            console.warn(
              `Map ${mapId}: Error during size invalidation:`,
              resizeError,
            );
            mapInstanceRef.current = null;
          }
        }, 100);
      }

      // Vérification supplémentaire pour s'assurer que la carte est visible
      // IMPORTANT: Cette vérification doit se faire même si l'artifact est visible
      setTimeout(() => {
        // Vérifier si le conteneur de la carte est visible
        if (mapRef.current && containerRef.current) {
          const mapRect = mapRef.current.getBoundingClientRect();
          const containerRect = containerRef.current.getBoundingClientRect();

          console.log(
            `Map ${mapId}: Container dimensions - map: ${mapRect.height}px, container: ${containerRect.height}px`,
          );

          if (mapRect.height === 0 || containerRect.height === 0) {
            console.warn(
              `Map ${mapId}: Map container has zero height, forcing reinit`,
            );
            if (mapInstanceRef.current) {
              try {
                mapInstanceRef.current.remove();
              } catch (e) {
                console.warn(`Map ${mapId}: Error removing map:`, e);
              }
              mapInstanceRef.current = null;
            }

            // Réinitialiser la carte
            if (isMapReady && places.length > 0) {
              setTimeout(() => setForceMapReinit((prev) => prev + 1), 100);
            }
          } else if (
            !mapInstanceRef.current &&
            isMapReady &&
            places.length > 0
          ) {
            // Si le conteneur a une taille mais pas de carte, en créer une
            console.log(
              `Map ${mapId}: Container has size but no map, creating one`,
            );
            setForceMapReinit((prev) => prev + 1);
          }
        }
      }, 300);
    }
  }, [isMapReady, places, initializeMap, isArtifactVisible, mapId]);

  // Effet pour forcer la réinitialisation de la carte quand nécessaire
  useEffect(() => {
    if (
      forceMapReinit > 0 &&
      isMapReady &&
      places.length > 0 &&
      !mapInstanceRef.current
    ) {
      console.log(
        `Map ${mapId}: Force reinitializing map (attempt ${forceMapReinit})`,
      );

      // Attendre un peu pour s'assurer que le DOM est stable
      setTimeout(() => {
        if (
          !mapInstanceRef.current &&
          mapRef.current &&
          document.contains(mapRef.current)
        ) {
          initializeMap();
        }
      }, 100);
    }
  }, [forceMapReinit, isMapReady, places, mapId, initializeMap]);

  // Effet pour initialiser la carte une fois que Leaflet est chargé
  useEffect(() => {
    if (!isMapReady || places.length === 0) {
      return;
    }

    console.log('Leaflet is ready and places are available, initializing map');

    // Attendre que la référence du conteneur soit disponible
    const waitForRef = () => {
      if (!mapRef.current) {
        console.log('Map container ref not available yet, waiting...');
        setTimeout(waitForRef, 100);
        return;
      }

      console.log('Map container ref is available, initializing map');
      initializeMap();
    };

    waitForRef();
  }, [isMapReady, places, initializeMap]);

  if (places.length === 0) {
    return null;
  }

  return (
    <div
      ref={containerRef}
      className="mt-4 rounded-lg overflow-hidden shadow-lg chat-map bg-white dark:bg-gray-800"
      id={mapId}
      data-map-id={mapId}
      style={{ display: 'block', minHeight: '400px' }} // Force l'affichage
    >
      <div className="bg-white dark:bg-gray-800 px-0 pt-2 pb-1 sm:px-0 sm:pt-4 sm:pb-2 rounded-b-none">
        <h2 className="text-base sm:text-lg font-semibold mb-2 text-black dark:text-white text-center">
          {query}
        </h2>
        <div
          ref={mapRef}
          className={`
            h-[320px] sm:h-[350px] md:h-[380px] lg:h-[400px]
            w-full rounded-t-lg rounded-b-none border border-gray-200 dark:border-gray-700
            map-container-${mapId}
          `}
          data-map-container-id={mapId}
          style={{
            minHeight: '320px',
            display: 'block',
            visibility: 'visible',
            opacity: 1,
          }}
        />
      </div>
      <EnhancedLocationDisplay
        places={places}
        selectedPlaceId={selectedPlaceId}
        onLocationClick={handleLocationClick}
        useElevator={useElevatorMode}
        onToggleMode={setUseElevatorMode}
      />

      {/* Modal d'image */}
      <ImageModal
        images={modalImages}
        currentIndex={currentImageIndex}
        isOpen={isImageModalOpen}
        onClose={closeImageModal}
        onPrevious={goToPreviousImage}
        onNext={goToNextImage}
        onImageSelect={selectImage}
        placeName={modalPlaceName}
      />
    </div>
  );
};

export default MapComponent;
