'use client';

import { DefaultChatTransport } from 'ai';
import { useChat } from '@ai-sdk/react';
import { useEffect, useRef, useState } from 'react';
import useSWR, { useSWRConfig } from 'swr';
import { ChatHeader } from '@/components/chat-header';
import type { Vote, Chat as DBChat } from '@/lib/db/schema';
import {
  fetchWithErrorHandlers,
  fetcher,
  generateUUID,
  getTextFromMessage,
} from '@/lib/utils';
import { Artifact } from './artifact';
import { MultimodalInput } from './multimodal-input';
import { Messages } from './messages';
import type { VisibilityType } from './visibility-selector';
import { useArtifactSelector } from '@/hooks/use-artifact';
import { toast } from './toast';
import type { Session } from 'next-auth';
import { unstable_serialize } from 'swr/infinite';
import { getChatHistoryPaginationKey } from './sidebar-history';
import { SuggestedActions } from './suggested-actions';
import { Greeting } from './greeting';
import { useSearchParams } from 'next/navigation';
import { ChatSDKError } from '@/lib/errors';
import { useAutoResume } from '@/hooks/use-auto-resume';
import { useChatVisibility } from '@/hooks/use-chat-visibility';
import type { Attachment, ChatMessage } from '@/lib/types';
import { useDataStream } from './data-stream-provider';

export function Chat({
  id,
  initialMessages,
  initialChatModel,
  initialVisibilityType,
  isReadonly,
  session,
  autoResume,
}: {
  id: string;
  initialMessages: ChatMessage[];
  initialChatModel: string;
  initialVisibilityType: VisibilityType;
  isReadonly: boolean;
  session: Session;
  autoResume: boolean;
}) {
  const { visibilityType } = useChatVisibility({
    chatId: id,
    initialVisibilityType,
  });

  // Filter out messages with incompatible roles for useChat hook and cast to Message type
  const compatibleInitialMessages = initialMessages
    .filter((message) => !['function', 'tool', 'data'].includes(message.role))
    .map((message) => {
      const { annotations, ...messageWithoutAnnotations } = message;
      return {
        ...messageWithoutAnnotations,
        role: message.role as 'user' | 'assistant' | 'system',
        // Convert annotations to JSONValue format if they exist
        ...(annotations && { annotations: annotations as any }),
      };
    });

  const { mutate } = useSWRConfig();
  const { setDataStream } = useDataStream();

  const [input, setInput] = useState<string>('');

  const {
    messages,
    setMessages,
    sendMessage,
    status,
    stop,
    regenerate,
    resumeStream,
  } = useChat<ChatMessage>({
    id,
    messages: compatibleInitialMessages,
    experimental_throttle: 100,
    generateId: generateUUID,
    transport: new DefaultChatTransport({
      api: '/api/chat',
      fetch: fetchWithErrorHandlers,
      prepareSendMessagesRequest({ messages, id, body }) {
        return {
          body: {
            id,
            message: messages.at(-1),
            selectedChatModel: initialChatModel,
            selectedVisibilityType: visibilityType,
            ...body,
          },
        };
      },
    }),
    onData: (dataPart) => {
      setDataStream((ds) => (ds ? [...ds, dataPart] : []));
    },
    onFinish: () => {
      mutate(unstable_serialize(getChatHistoryPaginationKey));
    },
    onError: (error) => {
      if (error instanceof ChatSDKError) {
        toast({
          type: 'error',
          description: error.message,
        });
      }
    },
  });

  // Gestion de la reprise automatique de la conversation
  // useEffect(() => {
  //   if (autoResume) {
  //     experimental_resume();
  //   }
  // note: this hook has no dependencies since it only needs to run once
  // eslint-disable-next-line react-hooks/exhaustive-deps
  //}, []);

  // Gestion des requêtes via URL
  const searchParams = useSearchParams();
  const query = searchParams.get('query');
  const [hasAppendedQuery, setHasAppendedQuery] = useState(false);

  useEffect(() => {
    if (query && !hasAppendedQuery) {
      sendMessage({
        role: 'user' as const,
        content: '',
        parts: [{ type: 'text', text: query }],
      });
      setHasAppendedQuery(true);
      window.history.replaceState({}, '', `/chat/${id}`);
    }
  }, [query, sendMessage, hasAppendedQuery, id]);

  // Récupération des votes
  const { data: votes } = useSWR<Array<Vote>>(
    messages.length >= 2 ? `/api/vote?chatId=${id}` : null,
    fetcher,
  );

  const [attachments, setAttachments] = useState<Array<Attachment>>([]);
  const isArtifactVisible = useArtifactSelector((state) => state.isVisible);

  useAutoResume({
    autoResume,
    initialMessages,
    resumeStream,
    setMessages,
  });

  const messagesWithoutPings: ChatMessage[] = messages.filter(
    (message) => getTextFromMessage(message) !== 'ping',
  );

  // Optimistically add the current chat to the sidebar history when the first message exists
  // so it appears instantly. It will be reconciled with the server on onFinish() via mutate().
  const hasOptimisticallyInserted = useRef(false);
  useEffect(() => {
    if (hasOptimisticallyInserted.current) return;
    if (messagesWithoutPings.length === 0) return;

    // Prepend a placeholder chat if it doesn't already exist in cache
    mutate(
      unstable_serialize(getChatHistoryPaginationKey),
      (
        pages: Array<{ chats: Array<DBChat>; hasMore: boolean }> | undefined,
      ) => {
        if (!pages || pages.length === 0) return pages;
        const exists = pages.some((p) => p.chats.some((c) => c.id === id));
        if (exists) return pages;

        const firstUserMsg = messagesWithoutPings.find(
          (m) => m.role === 'user',
        );
        const fallbackTitle =
          (firstUserMsg ? getTextFromMessage(firstUserMsg).slice(0, 80) : '') ||
          'New chat';

        const optimisticChat: DBChat = {
          id,
          createdAt: new Date(),
          title: fallbackTitle,
          userId: session.user.id,
          visibility: 'private',
        } as DBChat;

        return [
          { ...pages[0], chats: [optimisticChat, ...pages[0].chats] },
          ...pages.slice(1),
        ];
      },
      { revalidate: false },
    );

    hasOptimisticallyInserted.current = true;
  }, [messagesWithoutPings, id, mutate, session.user.id]);

  return (
    <>
      <div className="flex flex-col min-w-0 h-dvh bg-background">
        <ChatHeader
          chatId={id}
          selectedModelId={initialChatModel}
          selectedVisibilityType={initialVisibilityType}
          isReadonly={isReadonly}
          session={session}
        />

        {messagesWithoutPings.length === 0 ? (
          // Quand il n'y a pas de messages, afficher une structure personnalisée
          <div className="grow flex flex-col items-center justify-center">
            <div className="text-center mb-8">
              {/* Titre du Greeting sans les suggestions */}
              <h1 className="text-2xl font-bold mb-2">
                <Greeting />
              </h1>
            </div>

            <div className="w-full max-w-3xl mx-auto flex flex-col gap-4">
              <form className="flex mx-auto px-4 bg-background gap-2 w-full">
                {!isReadonly && (
                  <MultimodalInput
                    chatId={id}
                    input={input}
                    setInput={setInput}
                    status={status}
                    stop={stop}
                    attachments={attachments}
                    setAttachments={setAttachments}
                    messages={messagesWithoutPings}
                    setMessages={setMessages}
                    sendMessage={sendMessage}
                    selectedVisibilityType={visibilityType}
                    //showSuggestions={false} // Désactiver les suggestions ici
                  />
                )}
              </form>

              {/* Suggestions directement sous le champ de saisie */}
              <div className="w-full px-4">
                <SuggestedActions
                  sendMessage={sendMessage}
                  chatId={id}
                  selectedVisibilityType={visibilityType}
                />
              </div>
            </div>
          </div>
        ) : (
          // Structure normale quand il y a des messages
          <>
            <Messages
              chatId={id}
              status={status}
              votes={votes}
              messages={messagesWithoutPings}
              setMessages={setMessages}
              regenerate={regenerate}
              isReadonly={isReadonly}
              isArtifactVisible={isArtifactVisible}
            />

            <form className="flex mx-auto px-4 bg-background pb-4 md:pb-6 gap-2 w-full md:max-w-3xl">
              {!isReadonly && (
                <MultimodalInput
                  chatId={id}
                  input={input}
                  setInput={setInput}
                  status={status}
                  stop={stop}
                  attachments={attachments}
                  setAttachments={setAttachments}
                  messages={messagesWithoutPings}
                  setMessages={setMessages}
                  sendMessage={sendMessage}
                  selectedVisibilityType={visibilityType}
                />
              )}
            </form>
          </>
        )}
      </div>

      <Artifact
        chatId={id}
        input={input}
        setInput={setInput}
        status={status}
        stop={stop}
        attachments={attachments}
        setAttachments={setAttachments}
        sendMessage={sendMessage}
        messages={messagesWithoutPings}
        setMessages={setMessages}
        regenerate={regenerate}
        votes={votes}
        isReadonly={isReadonly}
        selectedVisibilityType={visibilityType}
      />
    </>
  );
}
