'use client';
/* eslint-disable react/no-unescaped-entities */

import cx from 'classnames';

// Global set to track rendered YouTube queries per message
const renderedYouTubeQueries = new Set<string>();

import { Markdown } from './markdown';
import { MessageActions } from './message-actions';
import { PreviewAttachment } from './preview-attachment';
import { Weather } from './weather';
import equal from 'fast-deep-equal';
import { cn, sanitizeText } from '@/lib/utils';
import { Button } from './ui/button';
import { Tooltip, TooltipContent, TooltipTrigger } from './ui/tooltip';
import { MessageEditor } from './message-editor';
import { DocumentPreview } from './document-preview';
import { MessageReasoning } from './message-reasoning';
import type { UseChatHelpers } from '@ai-sdk/react';
import { MemoryManager } from './memory-manager';
import { SearchLoadingState } from './ui/search-loading-state';
import { YouTubeLoadingState } from './ui/youtube-loading-state';
import { MapLoadingState } from './ui/map-loading-state';
import { Memory, MagnifyingGlass } from '@phosphor-icons/react';
import { SourceCitation } from './source-citation';
import { ImageGallery } from './image-gallery';
import { MessageAnnotations } from './chat/message-annotations';
import VideosComponent from './videos/VideosComponent';
import MapComponent from './map-component';
import FinancialChart from './FinancialChart';
import StockNews from './StockNews';
import StockScreener from './StockScreener';
import HeatmapsMarket from './HeatmapsMarket';
import CryptoCoinsHeatmap from './CryptoCoinsHeatmap';
import ETFHeatmap from './ETFHeatmap';
import ForexCrossRates from './ForexCrossRates';
import ForexHeatmap from './ForexHeatmap';
import CryptocurrencyMarket from './CryptocurrencyMarket';
import SymbolInfo from './SymbolInfo';
import TechnicalAnalysis from './TechnicalAnalysis';
import CompanyProfile from './CompanyProfile';
import EconomicCalendar from './EconomicCalendar';
import FloatingWidgetButton from './FloatingWidgetButton';
import { StockFinancials } from './StockFinancials';
import StockPrice from './StockPrice';
import MarketTrending from './MarketTrending';
import ToolInvocationListView from './tool-invocation-list-view';
import { AnimatePresence, motion } from 'framer-motion';
import { memo, useState } from 'react';
import { useArtifactSelector } from '@/hooks/use-artifact';
import type { Vote } from '@/lib/db/schema';
import { DocumentToolCall, DocumentToolResult } from './document';
import { PencilEditIcon, SparklesIcon } from './icons';
import type { ChatMessage } from '@/lib/types';
import { useDataStream } from './data-stream-provider';

const PurePreviewMessage = ({
  chatId,
  message,
  vote,
  isLoading,
  setMessages,
  regenerate,
  isReadonly,
  requiresScrollPadding,
}: {
  chatId: string;
  message: ChatMessage;
  vote: Vote | undefined;
  isLoading: boolean;
  setMessages: UseChatHelpers<ChatMessage>['setMessages'];
  regenerate: UseChatHelpers<ChatMessage>['regenerate'];
  isReadonly: boolean;
  requiresScrollPadding: boolean;
}) => {
  const [mode, setMode] = useState<'view' | 'edit'>('view');

  // Debug: Log annotations
  console.log('🔥 MESSAGE - Rendering with annotations:', {
    messageId: message.id,
    annotationsLength: message.annotations?.length || 0,
    annotations: message.annotations,
  });

  const attachmentsFromMessage = message.parts.filter(
    (part) => part.type === 'file',
  );

  useDataStream();

  return (
    <AnimatePresence>
      <motion.div
        data-testid={`message-${message.role}`}
        className={cn(
          'w-full mx-auto px-4 group/message',
          'max-w-3xl',
          // Reduce max width when artifact is visible to prevent overflow
          'data-[artifact-visible=true]:max-w-2xl',
        )}
        data-artifact-visible={useArtifactSelector((s) => s.isVisible)}
        initial={{ y: 5, opacity: 0 }}
        animate={{ y: 0, opacity: 1 }}
        data-role={message.role}
      >
        <div
          className={cn(
            'flex gap-4 w-full group-data-[role=user]/message:ml-auto group-data-[role=user]/message:max-w-2xl',
            {
              'w-full': mode === 'edit',
              // Ensure user message container doesn't shrink to fit attachments width
              // This keeps the bubble flush right regardless of number of attachments
              'group-data-[role=user]/message:w-full': mode !== 'edit',
            },
          )}
        >
          {message.role === 'assistant' && (
            <div className="size-8 flex items-center rounded-full justify-center ring-1 shrink-0 ring-border bg-background">
              <div className="translate-y-px">
                <SparklesIcon size={14} />
              </div>
            </div>
          )}

          <div
            className={cn('flex flex-col gap-4 w-full', {
              // Right-align user content within a stable width container
              'group-data-[role=user]/message:items-end': true,
              'min-h-96': message.role === 'assistant' && requiresScrollPadding,
            })}
          >
            {attachmentsFromMessage.length > 0 && (
              <div
                data-testid={`message-attachments`}
                className="flex flex-row justify-end gap-2 flex-wrap max-w-full overflow-hidden"
              >
                {attachmentsFromMessage.map((attachment) => (
                  <PreviewAttachment
                    key={attachment.url}
                    attachment={{
                      name: attachment.filename ?? 'file',
                      contentType: attachment.mediaType,
                      url: attachment.url,
                    }}
                  />
                ))}
              </div>
            )}

            {/* Display message annotations (like YouTube search results) */}
            {message.annotations && message.annotations.length > 0 && (
              <MessageAnnotations annotations={message.annotations} />
            )}

            {message.parts?.map((part, index) => {
              const { type } = part;
              const key = `message-${message.id}-part-${index}`;

              if (type === 'reasoning' && (isLoading || part.text?.trim())) {
                return (
                  <MessageReasoning
                    key={key}
                    isLoading={isLoading}
                    reasoning={part.text}
                  />
                );
              }

              // Handle YouTube data parts
              if (type === 'data-youtube') {
                const youtubeData = 'data' in part ? part.data : null;
                if (youtubeData && 'videos' in youtubeData) {
                  // Create unique identifier for this query in this message
                  const uniqueKey = `${message.id}-${youtubeData.query}`;

                  // Check if already rendered using memory-based tracking
                  if (renderedYouTubeQueries.has(uniqueKey)) {
                    return null; // Skip rendering if already exists
                  }

                  // Mark as rendered
                  renderedYouTubeQueries.add(uniqueKey);

                  return (
                    <div
                      key={key}
                      className="mt-2 mb-4"
                      data-youtube-query={youtubeData.query}
                    >
                      {youtubeData.videos.length > 0 ? (
                        <div className="rounded-xl overflow-hidden shadow-sm">
                          <div className="flex items-center justify-between p-3 bg-white">
                            <h3 className="text-md font-medium">
                              Videos YouTube - {youtubeData.query}
                            </h3>
                            <button
                              type="button"
                              className="text-sm text-gray-500 hover:text-gray-700"
                            >
                              ×
                            </button>
                          </div>
                          <VideosComponent videos={youtubeData.videos} />
                        </div>
                      ) : (
                        <p className="text-sm text-muted-foreground px-4">
                          Aucune vidéo YouTube trouvée pour "{youtubeData.query}
                          ".
                        </p>
                      )}
                    </div>
                  );
                }
                return null;
              }

              // Handle Map data parts
              if (type === 'data-map') {
                const mapData = 'data' in part ? part.data : null;
                if (mapData && 'places' in mapData) {
                  const { query, places } = mapData as {
                    query: string;
                    places: any[];
                  };
                  return (
                    <div key={key} className="mt-2 mb-4">
                      {Array.isArray(places) && places.length > 0 ? (
                        <MapComponent places={places as any} query={query} />
                      ) : (
                        <p className="text-sm text-muted-foreground px-4">
                          Aucun lieu trouvé pour "{query}".
                        </p>
                      )}
                    </div>
                  );
                }
                return null;
              }

              if (type === 'text') {
                if (mode === 'view') {
                  return (
                    <div key={key} className="flex flex-row gap-2 items-start">
                      {message.role === 'user' && !isReadonly && (
                        <Tooltip>
                          <TooltipTrigger asChild>
                            <Button
                              data-testid="message-edit-button"
                              variant="ghost"
                              className="px-2 h-fit rounded-full text-muted-foreground opacity-0 group-hover/message:opacity-100"
                              onClick={() => {
                                setMode('edit');
                              }}
                            >
                              <PencilEditIcon />
                            </Button>
                          </TooltipTrigger>
                          <TooltipContent>Edit message</TooltipContent>
                        </Tooltip>
                      )}

                      <div
                        data-testid="message-content"
                        className={cn('flex flex-col gap-4', {
                          'bg-primary text-primary-foreground px-3 py-2 rounded-xl':
                            message.role === 'user',
                        })}
                      >
                        <Markdown>{sanitizeText(part.text)}</Markdown>
                      </div>
                    </div>
                  );
                }

                if (mode === 'edit') {
                  return (
                    <div key={key} className="flex flex-row gap-2 items-start">
                      <div className="size-8" />

                      <MessageEditor
                        key={message.id}
                        message={message}
                        setMode={setMode}
                        setMessages={setMessages}
                        regenerate={regenerate}
                      />
                    </div>
                  );
                }
              }

              // Handle AI SDK tool types
              if (type.startsWith('tool-')) {
                // Extract tool information from the part
                const toolCallId =
                  'toolCallId' in part ? part.toolCallId : `${type}-${index}`;
                const toolName = type.replace('tool-', '');
                const state = 'state' in part ? part.state : 'call';
                const args = 'input' in part ? part.input : undefined;
                const result = 'output' in part ? part.output : undefined;

                // Create toolInvocation object for backward compatibility
                const toolInvocation = {
                  toolName,
                  toolCallId,
                  state: state as 'call' | 'result' | 'error' | 'partial-call',
                  args,
                  result,
                };

                // Support for extreme search and other generic tool invocations
                if (
                  toolName === 'extreme_search' ||
                  toolName === 'x_search' ||
                  toolName === 'reddit_search'
                ) {
                  // Use the generic ToolInvocationListView for these specific tools
                  console.log(
                    '🔥 MESSAGE - Passing annotations to ToolInvocationListView:',
                    {
                      messageId: message.id,
                      annotationsLength: message.annotations?.length || 0,
                      annotations: message.annotations,
                    },
                  );
                  return (
                    <ToolInvocationListView
                      key={`${message.id}-${index}-tool-${toolCallId}`}
                      toolInvocations={[toolInvocation]}
                      annotations={message.annotations}
                    />
                  );
                }

                // Only show loading state if this is the most recent call of its type
                const isMostRecentCall =
                  message.parts
                    ?.filter((p) => p.type === type)
                    ?.sort((a, b) =>
                      (
                        ('toolCallId' in b ? b.toolCallId : '') || ''
                      ).localeCompare(
                        ('toolCallId' in a ? a.toolCallId : '') || '',
                      ),
                    )[0] === part;

                if (
                  (state === 'call' || state === 'input-available') &&
                  isMostRecentCall
                ) {
                  return (
                    <div
                      key={toolCallId}
                      className={cx({
                        skeleton: ['getWeather'].includes(toolName),
                      })}
                    >
                      {toolName === 'getWeather' ? (
                        <Weather />
                      ) : toolName === 'createDocument' ? (
                        <DocumentPreview
                          isReadonly={isReadonly}
                          args={args as any}
                        />
                      ) : toolName === 'updateDocument' ? (
                        <DocumentToolCall
                          type="update"
                          args={args as any}
                          isReadonly={isReadonly}
                        />
                      ) : toolName === 'requestSuggestions' ? (
                        <DocumentToolCall
                          type="request-suggestions"
                          args={args as any}
                          isReadonly={isReadonly}
                        />
                      ) : toolName === 'retrieve' ? (
                        <SearchLoadingState
                          icon={Memory}
                          text="Searching the web..."
                          color="blue"
                        />
                      ) : toolName === 'web_search' ? (
                        <SearchLoadingState
                          icon={MagnifyingGlass}
                          text="Recherche sur le web..."
                          color="blue"
                        />
                      ) : toolName === 'youtube_search' ? (
                        <YouTubeLoadingState text="Recherche de vidéos YouTube..." />
                      ) : toolName === 'map_search' ? (
                        <MapLoadingState text="Recherche de lieux sur la carte..." />
                      ) : toolName === 'getStockChart' ? (
                        <div className="mt-4">
                          <div className="text-sm text-muted-foreground mb-2">
                            Chargement du graphique boursier pour{' '}
                            {(args as any)?.ticker}...
                          </div>
                          <div className="animate-pulse h-64 bg-muted rounded-lg" />
                        </div>
                      ) : toolName === 'getStockNews' ? (
                        <div className="mt-4">
                          <div className="text-sm text-muted-foreground mb-2">
                            Chargement des actualités pour{' '}
                            {(args as any)?.ticker}...
                          </div>
                          <div className="animate-pulse h-64 bg-muted rounded-lg" />
                        </div>
                      ) : toolName ===
                        'getStockScreener' ? // Don't show loading state for screener to avoid duplication with result rendering
                      null : toolName ===
                        'getHeatmapsMarket' ? // Don't show loading state for heatmap to avoid duplication with result rendering
                      null : toolName ===
                        'getCryptoCoinsHeatmap' ? // Don't show loading state for crypto heatmap to avoid duplication with result rendering
                      null : toolName === 'getStockFinancials' ? (
                        <div className="mt-4">
                          <div className="text-sm text-muted-foreground mb-2">
                            Chargement des informations financières pour{' '}
                            {(args as any)?.ticker}
                          </div>
                          <div className="animate-pulse h-64 bg-muted rounded-lg" />
                        </div>
                      ) : toolName === 'getStockPrice' ? (
                        <div className="mt-4">
                          <div className="text-sm text-muted-foreground mb-2">
                            Chargement du cours de l'action pour{' '}
                            {(args as any)?.ticker}
                          </div>
                          <div className="animate-pulse h-64 bg-muted rounded-lg" />
                        </div>
                      ) : null}
                    </div>
                  );
                }

                if (state === 'output-available') {
                  const finalResult =
                    result || ('output' in part ? part.output : undefined);

                  return (
                    <div key={toolCallId}>
                      {toolName === 'getWeather' ? (
                        <Weather weatherAtLocation={finalResult} />
                      ) : toolName === 'createDocument' ? (
                        <DocumentPreview
                          isReadonly={isReadonly}
                          result={finalResult}
                        />
                      ) : toolName === 'updateDocument' ? (
                        <DocumentToolResult
                          type="update"
                          result={finalResult}
                          isReadonly={isReadonly}
                        />
                      ) : toolName === 'requestSuggestions' ? (
                        <DocumentToolResult
                          type="request-suggestions"
                          result={finalResult}
                          isReadonly={isReadonly}
                        />
                      ) : toolName === 'getStockChart' ? (
                        <div className="mt-4">
                          <div className="flex items-center justify-between mb-2">
                            <h3 className="text-sm font-medium text-gray-700">
                              Financial Chart -{' '}
                              {finalResult?.ticker || (args as any)?.ticker}
                            </h3>
                            <FloatingWidgetButton
                              widgetType="financial_chart"
                              toolName={toolName}
                              result={finalResult}
                              toolInvocation={toolInvocation}
                            />
                          </div>
                          <FinancialChart
                            ticker={
                              finalResult?.ticker || (args as any)?.ticker
                            }
                          />
                        </div>
                      ) : toolName === 'getStockNews' ? (
                        <div className="mt-4">
                          <div className="flex items-center justify-between mb-2">
                            <h3 className="text-sm font-medium text-gray-700">
                              Stock News -{' '}
                              {finalResult?.ticker || (args as any)?.ticker}
                            </h3>
                            <FloatingWidgetButton
                              widgetType="stock_news"
                              toolName={toolName}
                              result={finalResult}
                              toolInvocation={toolInvocation}
                            />
                          </div>
                          <StockNews
                            ticker={
                              finalResult?.ticker || (args as any)?.ticker
                            }
                          />
                        </div>
                      ) : toolName === 'getStockScreener' &&
                        finalResult?.type === 'stock_screener' ? (
                        <div
                          className="mt-4"
                          key={`stock-screener-${toolCallId}`}
                        >
                          <div className="flex items-center justify-between mb-2">
                            <h3 className="text-sm font-medium text-gray-700">
                              Stock Screener
                            </h3>
                            <FloatingWidgetButton
                              widgetType="stock_screener"
                              toolName={toolName}
                              result={finalResult}
                              toolInvocation={toolInvocation}
                            />
                          </div>
                          <StockScreener />
                        </div>
                      ) : toolName === 'getHeatmapsMarket' &&
                        finalResult?.type === 'heatmaps_market' ? (
                        <div
                          className="mt-4"
                          key={`heatmaps-market-${toolCallId}`}
                        >
                          <div className="flex items-center justify-between mb-2">
                            <h3 className="text-sm font-medium text-gray-700">
                              Market Heatmap
                            </h3>
                            <FloatingWidgetButton
                              widgetType="heatmaps_market"
                              toolName={toolName}
                              result={finalResult}
                              toolInvocation={toolInvocation}
                            />
                          </div>
                          <HeatmapsMarket />
                        </div>
                      ) : toolName === 'getCryptoCoinsHeatmap' &&
                        finalResult?.type === 'crypto_coins_heatmap' ? (
                        <div
                          className="mt-4"
                          key={`crypto-coins-heatmap-${toolCallId}`}
                        >
                          <div className="flex items-center justify-between mb-2">
                            <h3 className="text-sm font-medium text-gray-700">
                              Crypto Coins Heatmap
                            </h3>
                            <FloatingWidgetButton
                              widgetType="crypto_coins_heatmap"
                              toolName={toolName}
                              result={finalResult}
                              toolInvocation={toolInvocation}
                            />
                          </div>
                          <CryptoCoinsHeatmap />
                        </div>
                      ) : toolName === 'getETFHeatmap' &&
                        finalResult?.type === 'etf_heatmap' ? (
                        <div className="mt-4" key={`etf-heatmap-${toolCallId}`}>
                          <div className="flex items-center justify-between mb-2">
                            <h3 className="text-sm font-medium text-gray-700">
                              ETF Heatmap
                            </h3>
                            <FloatingWidgetButton
                              widgetType="etf_heatmap"
                              toolName={toolName}
                              result={finalResult}
                              toolInvocation={toolInvocation}
                            />
                          </div>
                          <ETFHeatmap />
                        </div>
                      ) : toolName === 'getForexCrossRates' &&
                        finalResult?.type === 'forex_cross_rates' ? (
                        <div
                          className="mt-4"
                          key={`forex-cross-rates-${toolCallId}`}
                        >
                          <div className="flex items-center justify-between mb-2">
                            <h3 className="text-sm font-medium text-gray-700">
                              Forex Cross Rates
                            </h3>
                            <FloatingWidgetButton
                              widgetType="forex_cross_rates"
                              toolName={toolName}
                              result={finalResult}
                              toolInvocation={toolInvocation}
                            />
                          </div>
                          <ForexCrossRates />
                        </div>
                      ) : toolName === 'getForexHeatmap' &&
                        finalResult?.type === 'forex_heatmap' ? (
                        <div
                          className="mt-4"
                          key={`forex-heatmap-${toolCallId}`}
                        >
                          <div className="flex items-center justify-between mb-2">
                            <h3 className="text-sm font-medium text-gray-700">
                              Forex Heatmap
                            </h3>
                            <FloatingWidgetButton
                              widgetType="forex_heatmap"
                              toolName={toolName}
                              result={finalResult}
                              toolInvocation={toolInvocation}
                            />
                          </div>
                          <ForexHeatmap />
                        </div>
                      ) : toolName === 'getCryptocurrencyMarket' &&
                        finalResult?.type === 'cryptocurrency_market' ? (
                        <div
                          className="mt-4"
                          key={`cryptocurrency-market-${toolCallId}`}
                        >
                          <div className="flex items-center justify-between mb-2">
                            <h3 className="text-sm font-medium text-gray-700">
                              Cryptocurrency Market
                            </h3>
                            <FloatingWidgetButton
                              widgetType="cryptocurrency_market"
                              toolName={toolName}
                              result={finalResult}
                              toolInvocation={toolInvocation}
                            />
                          </div>
                          <CryptocurrencyMarket />
                        </div>
                      ) : toolName === 'getSymbolInfo' &&
                        finalResult?.type === 'symbol_info' ? (
                        <div className="mt-4" key={`symbol-info-${toolCallId}`}>
                          <div className="flex items-center justify-between mb-2">
                            <h3 className="text-sm font-medium text-gray-700">
                              Symbol Info -{' '}
                              {finalResult?.data?.symbol ||
                                (args as any)?.symbol}
                            </h3>
                            <FloatingWidgetButton
                              widgetType="symbol_info"
                              toolName={toolName}
                              result={finalResult}
                              toolInvocation={toolInvocation}
                            />
                          </div>
                          <SymbolInfo
                            symbol={
                              finalResult?.data?.symbol || (args as any)?.symbol
                            }
                          />
                        </div>
                      ) : toolName === 'getTechnicalAnalysis' &&
                        finalResult?.type === 'technical_analysis' ? (
                        <div
                          className="mt-4"
                          key={`technical-analysis-${toolCallId}`}
                        >
                          <div className="flex items-center justify-between mb-2">
                            <h3 className="text-sm font-medium text-gray-700">
                              Technical Analysis -{' '}
                              {finalResult?.data?.symbol ||
                                (args as any)?.symbol}
                            </h3>
                            <FloatingWidgetButton
                              widgetType="technical_analysis"
                              toolName={toolName}
                              result={finalResult}
                              toolInvocation={toolInvocation}
                            />
                          </div>
                          <TechnicalAnalysis
                            symbol={
                              finalResult?.data?.symbol || (args as any)?.symbol
                            }
                            interval={
                              finalResult?.data?.interval ||
                              (args as any)?.interval
                            }
                          />
                        </div>
                      ) : toolName === 'getCompanyProfile' &&
                        finalResult?.type === 'company_profile' ? (
                        <div
                          className="mt-4"
                          key={`company-profile-${toolCallId}`}
                        >
                          <div className="flex items-center justify-between mb-2">
                            <h3 className="text-sm font-medium text-gray-700">
                              Company Profile -{' '}
                              {finalResult?.data?.symbol ||
                                (args as any)?.symbol}
                            </h3>
                            <FloatingWidgetButton
                              widgetType="company_profile"
                              toolName={toolName}
                              result={finalResult}
                              toolInvocation={toolInvocation}
                            />
                          </div>
                          <CompanyProfile
                            symbol={
                              finalResult?.data?.symbol || (args as any)?.symbol
                            }
                          />
                        </div>
                      ) : toolName === 'getEconomicCalendar' &&
                        finalResult?.type === 'economic_calendar' ? (
                        <div
                          className="mt-4"
                          key={`economic-calendar-${toolCallId}`}
                        >
                          <div className="flex items-center justify-between mb-2">
                            <h3 className="text-sm font-medium text-gray-700">
                              Economic Calendar
                            </h3>
                            <FloatingWidgetButton
                              widgetType="economic_calendar"
                              toolName={toolName}
                              result={finalResult}
                              toolInvocation={toolInvocation}
                            />
                          </div>
                          <EconomicCalendar />
                        </div>
                      ) : toolName === 'getStockFinancials' ? (
                        <div className="mt-4">
                          <div className="flex items-center justify-between mb-2">
                            <h3 className="text-sm font-medium text-gray-700">
                              Stock Financials -{' '}
                              {finalResult?.ticker || (args as any)?.ticker}
                            </h3>
                            <FloatingWidgetButton
                              widgetType="stock_financials"
                              toolName={toolName}
                              result={finalResult}
                              toolInvocation={toolInvocation}
                            />
                          </div>
                          <StockFinancials
                            symbol={
                              finalResult?.ticker || (args as any)?.ticker
                            }
                          />
                        </div>
                      ) : toolName === 'getStockPrice' ? (
                        <div className="mt-4">
                          <div className="flex items-center justify-between mb-2">
                            <h3 className="text-sm font-medium text-gray-700">
                              Stock Price -{' '}
                              {finalResult?.ticker || (args as any)?.ticker}
                            </h3>
                            <FloatingWidgetButton
                              widgetType="stock_price"
                              toolName={toolName}
                              result={finalResult}
                              toolInvocation={toolInvocation}
                            />
                          </div>
                          <StockPrice
                            symbol={
                              finalResult?.ticker || (args as any)?.ticker
                            }
                          />
                        </div>
                      ) : toolName === 'showMarketTrending' ? (
                        <div className="mt-4">
                          <div className="flex items-center justify-between mb-2">
                            <h3 className="text-sm font-medium text-gray-700">
                              Market Trending
                            </h3>
                            <FloatingWidgetButton
                              widgetType="market_trending"
                              toolName={toolName}
                              result={finalResult}
                              toolInvocation={toolInvocation}
                            />
                          </div>
                          <MarketTrending />
                        </div>
                      ) : toolName === 'memory_manager' ? (
                        !finalResult ? (
                          <SearchLoadingState
                            icon={Memory}
                            text="Managing memories..."
                            color="purple"
                          />
                        ) : (
                          <MemoryManager result={finalResult} />
                        )
                      ) : toolName === 'retrieve' ? (
                        !finalResult ? (
                          <SearchLoadingState
                            icon={Memory}
                            text="Searching the web..."
                            color="blue"
                          />
                        ) : (
                          <MemoryManager result={finalResult} />
                        )
                      ) : toolName === 'web_search' ? (
                        finalResult &&
                        typeof finalResult === 'object' &&
                        'searches' in finalResult ? (
                          <>
                            {/* Afficher les résultats de recherche avec images dans un seul cadre */}
                            <div className="mb-4">
                              {finalResult.searches.some(
                                (search: { images?: any[] }) =>
                                  search.images && search.images.length > 0,
                              ) && (
                                <div className="mb-4">
                                  <h3 className="text-md font-medium mb-3 px-4">
                                    Images illustratives
                                  </h3>
                                  {/* Regrouper toutes les images dans un seul cadre */}
                                  <div>
                                    {(() => {
                                      // Collecter toutes les images de toutes les recherches
                                      const allImages =
                                        finalResult.searches.flatMap(
                                          (search: {
                                            images?: any[];
                                            query: string;
                                          }) =>
                                            search.images &&
                                            Array.isArray(search.images)
                                              ? search.images.map(
                                                  (img: any) => ({
                                                    ...(typeof img === 'string'
                                                      ? { url: img }
                                                      : img),
                                                    searchQuery: search.query,
                                                  }),
                                                )
                                              : [],
                                        );

                                      // S'il y a des images, les afficher dans un seul ImageGallery
                                      return allImages.length > 0 ? (
                                        <ImageGallery
                                          images={allImages}
                                          query={
                                            finalResult.searches[0]?.query || ''
                                          }
                                          maxPreviewImages={8}
                                        />
                                      ) : null;
                                    })()}
                                  </div>
                                </div>
                              )}
                            </div>

                            {/* Afficher les sources */}
                            <SourceCitation
                              sources={finalResult.searches.flatMap(
                                (search: { results: any[] }) =>
                                  search.results.map(
                                    (item: {
                                      url: string;
                                      title: string;
                                      content: string;
                                    }) => ({
                                      url: item.url,
                                      title: item.title,
                                      content: item.content,
                                    }),
                                  ),
                              )}
                            />
                          </>
                        ) : null
                      ) : toolName === 'youtube_search' ? (
                        finalResult &&
                        typeof finalResult === 'object' &&
                        'videos' in finalResult ? (
                          <div className="mt-2 mb-4">
                            {finalResult.videos.length > 0 ? (
                              <div className="rounded-xl overflow-hidden shadow-sm">
                                <div className="flex items-center justify-between p-3 bg-white">
                                  <h3 className="text-md font-medium">
                                    Videos
                                  </h3>
                                  <button
                                    type="button"
                                    className="text-sm text-gray-500 hover:text-gray-700"
                                  >
                                    ×
                                  </button>
                                </div>
                                <VideosComponent videos={finalResult.videos} />
                              </div>
                            ) : (
                              <p className="text-sm text-muted-foreground px-4">
                                Aucune vidéo YouTube trouvée pour cette
                                recherche.
                              </p>
                            )}
                          </div>
                        ) : null
                      ) : null}
                    </div>
                  );
                }
              }
            })}

            {!isReadonly && (
              <MessageActions
                key={`action-${message.id}`}
                chatId={chatId}
                message={message}
                vote={vote}
                isLoading={isLoading}
              />
            )}
          </div>
        </div>
      </motion.div>
    </AnimatePresence>
  );
};

export const PreviewMessage = memo(
  PurePreviewMessage,
  (prevProps, nextProps) => {
    if (prevProps.isLoading !== nextProps.isLoading) return false;
    if (prevProps.message.id !== nextProps.message.id) return false;
    if (prevProps.requiresScrollPadding !== nextProps.requiresScrollPadding)
      return false;
    if (!equal(prevProps.message.parts, nextProps.message.parts)) return false;
    if (!equal(prevProps.vote, nextProps.vote)) return false;

    return false;
  },
);

export const ThinkingMessage = () => {
  const role = 'assistant';

  return (
    <motion.div
      data-testid="message-assistant-loading"
      className="w-full mx-auto max-w-3xl px-4 group/message min-h-96"
      initial={{ y: 5, opacity: 0 }}
      animate={{ y: 0, opacity: 1, transition: { delay: 1 } }}
      data-role={role}
    >
      <div
        className={cx(
          'flex gap-4 group-data-[role=user]/message:px-3 w-full group-data-[role=user]/message:w-fit group-data-[role=user]/message:ml-auto group-data-[role=user]/message:max-w-2xl group-data-[role=user]/message:py-2 rounded-xl',
          {
            'group-data-[role=user]/message:bg-muted': true,
          },
        )}
      >
        <div className="size-8 flex items-center rounded-full justify-center ring-1 shrink-0 ring-border">
          <SparklesIcon size={14} />
        </div>

        <div className="flex flex-col gap-2 w-full">
          <div className="flex flex-col gap-4 text-muted-foreground">
            Hmm...
          </div>
        </div>
      </div>
    </motion.div>
  );
};
