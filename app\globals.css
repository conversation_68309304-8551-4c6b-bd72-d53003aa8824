@tailwind base;
@tailwind components;
@tailwind utilities;

:root {
    --foreground-rgb: 0, 0, 0;
    --background-start-rgb: 214, 219, 220;
    --background-end-rgb: 255, 255, 255;
    /* autres variables */
}

@media (prefers-color-scheme: dark) {
    :root {
        --foreground-rgb: 255, 255, 255;
        --background-start-rgb: 0, 0, 0;
        --background-end-rgb: 0, 0, 0;
    }
}

@layer utilities {
    .text-balance {
        text-wrap: balance;
    }
}

@layer base {
    :root {
        --background: 0 0% 100%;
        --foreground: 240 10% 3.9%;
        --card: 0 0% 100%;
        --card-foreground: 240 10% 3.9%;
        --popover: 0 0% 100%;
        --popover-foreground: 240 10% 3.9%;
        --primary: 240 5.9% 10%;
        --primary-foreground: 0 0% 98%;
        --secondary: 240 4.8% 95.9%;
        --secondary-foreground: 240 5.9% 10%;
        --muted: 240 4.8% 95.9%;
        --muted-foreground: 240 3.8% 46.1%;
        --accent: 240 4.8% 95.9%;
        --accent-foreground: 240 5.9% 10%;
        --destructive: 0 84.2% 60.2%;
        --destructive-foreground: 0 0% 98%;
        --border: 240 5.9% 90%;
        --input: 240 5.9% 90%;
        --ring: 240 10% 3.9%;
        --chart-1: 12 76% 61%;
        --chart-2: 173 58% 39%;
        --chart-3: 197 37% 24%;
        --chart-4: 43 74% 66%;
        --chart-5: 27 87% 67%;
        --radius: 0.5rem;
        --sidebar-background: 0 0% 98%;
        --sidebar-foreground: 240 5.3% 26.1%;
        --sidebar-primary: 240 5.9% 10%;
        --sidebar-primary-foreground: 0 0% 98%;
        --sidebar-accent: 240 4.8% 95.9%;
        --sidebar-accent-foreground: 240 5.9% 10%;
        --sidebar-border: 220 13% 91%;
        --sidebar-ring: 217.2 91.2% 59.8%;
    }
    .dark {
        --background: 240 10% 3.9%;
        --foreground: 0 0% 98%;
        --card: 240 10% 3.9%;
        --card-foreground: 0 0% 98%;
        --popover: 240 10% 3.9%;
        --popover-foreground: 0 0% 98%;
        --primary: 0 0% 98%;
        --primary-foreground: 240 5.9% 10%;
        --secondary: 240 3.7% 15.9%;
        --secondary-foreground: 0 0% 98%;
        --muted: 240 3.7% 15.9%;
        --muted-foreground: 240 5% 64.9%;
        --accent: 240 3.7% 15.9%;
        --accent-foreground: 0 0% 98%;
        --destructive: 0 62.8% 30.6%;
        --destructive-foreground: 0 0% 98%;
        --border: 240 3.7% 15.9%;
        --input: 240 3.7% 15.9%;
        --ring: 240 4.9% 83.9%;
        --chart-1: 220 70% 50%;
        --chart-2: 160 60% 45%;
        --chart-3: 30 80% 55%;
        --chart-4: 280 65% 60%;
        --chart-5: 340 75% 55%;
        --sidebar-background: 240 5.9% 10%;
        --sidebar-foreground: 240 4.8% 95.9%;
        --sidebar-primary: 224.3 76.3% 48%;
        --sidebar-primary-foreground: 0 0% 100%;
        --sidebar-accent: 240 3.7% 15.9%;
        --sidebar-accent-foreground: 240 4.8% 95.9%;
        --sidebar-border: 240 3.7% 15.9%;
        --sidebar-ring: 217.2 91.2% 59.8%;
    }
}

@layer base {
  * {
    @apply border-neutral-200 dark:border-neutral-800;
  }

  body {
    @apply bg-background text-foreground;
  }
}

.skeleton {
    * {
        pointer-events: none !important;
    }

    *[class^="text-"] {
        color: transparent;
        @apply rounded-md bg-foreground/20 select-none animate-pulse;
    }

    .skeleton-bg {
        @apply bg-foreground/10;
    }

    .skeleton-div {
        @apply bg-foreground/20 animate-pulse;
    }
}

.ProseMirror {
    outline: none;
}

.cm-editor,
.cm-gutters {
    @apply bg-background dark:bg-zinc-800 outline-none selection:bg-zinc-900 !important;
}

.ͼo.cm-focused > .cm-scroller > .cm-selectionLayer .cm-selectionBackground,
.ͼo.cm-selectionBackground,
.ͼo.cm-content::selection {
    @apply bg-zinc-200 dark:bg-zinc-900 !important;
}

.cm-activeLine,
.cm-activeLineGutter {
    @apply bg-transparent !important;
}

.cm-activeLine {
    @apply rounded-r-sm !important;
}

.cm-lineNumbers {
    @apply min-w-7;
}

.cm-foldGutter {
    @apply min-w-3;
}

.cm-lineNumbers .cm-activeLineGutter {
    @apply rounded-l-sm !important;
}

/* Styles pour les suggestions */
.suggestion-highlight {
  background-color: rgba(255, 220, 0, 0.2);
  border-bottom: 2px dotted rgba(255, 220, 0, 0.8);
  position: relative;
}

/* Styles pour les widgets de suggestion */
.suggestion-widget {
  position: relative;
  display: inline-block;
  z-index: 9999 !important;
  pointer-events: auto !important;
}

/* Positionnement spécifique pour les widgets de suggestion */
.ProseMirror .suggestion-widget {
  position: absolute;
  right: -30px;
  transform: translateY(-50%);
}

/* S'assurer que les widgets sont au-dessus de tout */
.ProseMirror {
  position: relative;
}

/* Forcer la visibilité des icônes */
.suggestion-icon {
  opacity: 1 !important;
  visibility: visible !important;
  pointer-events: auto !important;
}

/* Rendre les URLs cliquables automatiquement */
.ProseMirror a[href],
.artifact-content a[href] {
  color: #2563eb; /* Bleu */
  text-decoration: none;
}

.ProseMirror a[href]:hover,
.artifact-content a[href]:hover {
  text-decoration: underline;
}

/* Activer le comportement de lien automatique pour les URLs */
.ProseMirror,
.artifact-content {
  overflow-wrap: break-word;
  word-wrap: break-word;
}

/* Ajouter ces styles pour s'assurer que le formatage Markdown persiste */
.markdown-container {
  /* Empêcher d'autres styles de remplacer notre formatage */
  display: block !important;
}

.markdown-container h1,
.markdown-container h2,
.markdown-container h3,
.markdown-container h4,
.markdown-container h5,
.markdown-container h6 {
  font-weight: bold !important;
  margin-top: 1em !important;
  margin-bottom: 0.5em !important;
}

.markdown-container h1 {
  font-size: 2rem !important;
}

.markdown-container h2 {
  font-size: 1.5rem !important;
}

.markdown-container h3 {
  font-size: 1.25rem !important;
}

.markdown-container p {
  margin-bottom: 1em !important;
}

.markdown-container ul {
  list-style-type: disc !important;
  padding-left: 1.5em !important;
  margin-bottom: 1em !important;
}

.markdown-container ol {
  list-style-type: decimal !important;
  padding-left: 1.5em !important;
  margin-bottom: 1em !important;
}

.markdown-container blockquote {
  border-left: 4px solid #e2e8f0 !important;
  padding-left: 1em !important;
  margin: 1em 0 !important;
  font-style: italic !important;
}

.markdown-container pre {
  background-color: #f7fafc !important;
  padding: 1em !important;
  border-radius: 0.25em !important;
  overflow-x: auto !important;
  margin: 1em 0 !important;
}

.dark .markdown-container pre {
  background-color: #2d3748 !important;
}

.markdown-container code {
  background-color: #f7fafc !important;
  padding: 0.1em 0.3em !important;
  border-radius: 0.25em !important;
  font-size: 0.9em !important;
}

.dark .markdown-container code {
  background-color: #2d3748 !important;
}

/* Floating Widget Styles */
.floating-widget {
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15), 0 4px 10px rgba(0, 0, 0, 0.1);
  transition: box-shadow 0.2s ease-in-out;
  user-select: none;
  will-change: transform;
  transform: translateZ(0);
  backface-visibility: hidden;
}

.floating-widget:hover {
  box-shadow: 0 15px 35px rgba(0, 0, 0, 0.2), 0 6px 15px rgba(0, 0, 0, 0.15);
}

/* Optimisations pour le drag ultra-fluide */
.floating-widget.dragging {
  transition: none !important;
  pointer-events: none;
  transform: translateZ(0);
  will-change: transform;
  contain: layout style paint;
}

.floating-widget.dragging * {
  pointer-events: none;
  user-select: none;
}

.floating-widget.resizing {
  transition: none !important;
  will-change: width, height;
  contain: layout style;
}

/* Optimisations supplémentaires pour la performance */
.floating-widget {
  contain: layout style paint;
  isolation: isolate;
}

.floating-widget .widget-content {
  contain: layout style paint;
  transform: translateZ(0);
}

.floating-widget .drag-handle {
  cursor: move;
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
  border-bottom: 1px solid #dee2e6;
}

.floating-widget .drag-handle:hover {
  background: linear-gradient(135deg, #e9ecef 0%, #dee2e6 100%);
}

.floating-widget .widget-content {
  overflow: hidden;
  position: relative;
}

/* Resize handle styles */
.floating-widget .resize-handle {
  position: absolute;
  bottom: 0;
  right: 0;
  width: 16px;
  height: 16px;
  cursor: se-resize;
  background: linear-gradient(-45deg, transparent 30%, #9ca3af 30%, #9ca3af 70%, transparent 70%);
  opacity: 0.7;
  transition: opacity 0.2s ease;
}

.floating-widget:hover .resize-handle {
  opacity: 1;
}

/* Window control buttons */
.floating-widget .window-controls button {
  transition: all 0.15s ease;
  border-radius: 4px;
}

.floating-widget .window-controls button:hover {
  transform: scale(1.1);
}

/* Floating widget overlay for better visibility */
.floating-widget-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  pointer-events: none;
  z-index: 999;
}

/* Ensure floating widgets are above other content */
.floating-widget {
  z-index: 1000;
}

/* Smooth animations for minimize/maximize */
.floating-widget {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

/* Dark theme support for floating widgets */
.dark .floating-widget {
  background-color: #1f2937;
  border-color: #374151;
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.3), 0 4px 10px rgba(0, 0, 0, 0.2);
}

.dark .floating-widget .drag-handle {
  background: linear-gradient(135deg, #374151 0%, #4b5563 100%);
  border-bottom-color: #4b5563;
}

.dark .floating-widget .drag-handle:hover {
  background: linear-gradient(135deg, #4b5563 0%, #6b7280 100%);
}





/* Hide floating chat controls while the image modal is open */
body.image-modal-open [data-testid="scroll-to-bottom-button"] {
  display: none !important;
}

body.image-modal-open [data-testid="microphone-button"] {
  display: none !important;
}



