/**
 * Composant de carte interactive pour les informations financières
 * Inclut ATMs, zones de paiement, zones à risque, et itinéraires optimisés
 */

import type { CurrencyInfo } from '../types';

interface MapLocation {
  lat: number;
  lng: number;
  name: string;
  type: 'atm' | 'bank' | 'exchange' | 'restaurant' | 'shop' | 'risk' | 'safe';
  details: {
    fees?: string;
    hours?: string;
    paymentMethods?: string[];
    riskLevel?: 'low' | 'medium' | 'high';
    notes?: string;
  };
}

interface MapFilter {
  type: 'fee-free' | 'open-now' | 'alipay' | 'cash-only' | 'safe-zones';
  label: string;
  active: boolean;
}

/**
 * Génère le HTML pour la carte interactive des informations financières
 */
export function generateInteractiveCurrencyMap(
  currencyInfo: CurrencyInfo,
  destination: string,
  coordinates: { lat: string; lng: string }
): string {
  const mapId = `currency-map-${Date.now()}`;
  
  return `
    <div id="interactive-currency-map" style="margin: 25px 0; background: white; border-radius: 12px; box-shadow: 0 4px 8px rgba(0,0,0,0.1); overflow: hidden;">
      <!-- Map Header -->
      <div style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; padding: 20px;">
        <h4 style="margin: 0 0 10px 0; font-size: 1.3em; font-weight: 600; display: flex; align-items: center;">
          <span style="margin-right: 10px; font-size: 1.4em;">🗺️</span> Interactive Financial Map
        </h4>
        <p style="margin: 0; opacity: 0.9; font-size: 0.9em;">Find ATMs, payment options, and safe zones near your location</p>
      </div>

      <!-- Map Filters -->
      <div style="padding: 15px; background: #f8f9fa; border-bottom: 1px solid #e9ecef;">
        <div style="display: flex; flex-wrap: wrap; gap: 10px; align-items: center;">
          <span style="font-weight: 600; color: #495057; margin-right: 10px;">Filters:</span>
          
          <button class="map-filter" data-filter="fee-free" style="
            padding: 6px 12px; 
            border: 1px solid #28a745; 
            background: white; 
            color: #28a745; 
            border-radius: 20px; 
            font-size: 0.8em; 
            cursor: pointer;
            transition: all 0.2s;
          ">
            💰 Fee-Free ATMs
          </button>
          
          <button class="map-filter" data-filter="open-now" style="
            padding: 6px 12px; 
            border: 1px solid #007bff; 
            background: white; 
            color: #007bff; 
            border-radius: 20px; 
            font-size: 0.8em; 
            cursor: pointer;
            transition: all 0.2s;
          ">
            🕒 Open Now
          </button>
          
          <button class="map-filter" data-filter="alipay" style="
            padding: 6px 12px; 
            border: 1px solid #1890ff; 
            background: white; 
            color: #1890ff; 
            border-radius: 20px; 
            font-size: 0.8em; 
            cursor: pointer;
            transition: all 0.2s;
          ">
            📱 Alipay Accepted
          </button>
          
          <button class="map-filter" data-filter="cash-only" style="
            padding: 6px 12px; 
            border: 1px solid #ffc107; 
            background: white; 
            color: #ffc107; 
            border-radius: 20px; 
            font-size: 0.8em; 
            cursor: pointer;
            transition: all 0.2s;
          ">
            💵 Cash Only
          </button>
          
          <button class="map-filter" data-filter="safe-zones" style="
            padding: 6px 12px; 
            border: 1px solid #28a745; 
            background: white; 
            color: #28a745; 
            border-radius: 20px; 
            font-size: 0.8em; 
            cursor: pointer;
            transition: all 0.2s;
          ">
            🛡️ Safe Zones
          </button>
        </div>
      </div>

      <!-- Map Container -->
      <div style="position: relative; height: 400px; background: #e9ecef;">
        <div id="${mapId}" style="width: 100%; height: 100%; position: relative; background: linear-gradient(45deg, #f8f9fa 25%, transparent 25%), linear-gradient(-45deg, #f8f9fa 25%, transparent 25%), linear-gradient(45deg, transparent 75%, #f8f9fa 75%), linear-gradient(-45deg, transparent 75%, #f8f9fa 75%); background-size: 20px 20px; background-position: 0 0, 0 10px, 10px -10px, -10px 0px;">
          
          <!-- Map Placeholder with Location Info -->
          <div style="position: absolute; top: 50%; left: 50%; transform: translate(-50%, -50%); text-align: center; background: white; padding: 20px; border-radius: 8px; box-shadow: 0 2px 8px rgba(0,0,0,0.1);">
            <div style="font-size: 2em; margin-bottom: 10px;">📍</div>
            <h5 style="margin: 0 0 10px 0; color: #495057;">${destination}</h5>
            <p style="margin: 0; color: #6c757d; font-size: 0.9em;">Interactive map will load here</p>
            <p style="margin: 5px 0 0 0; color: #6c757d; font-size: 0.8em;">Coordinates: ${coordinates.lat}, ${coordinates.lng}</p>
          </div>

          <!-- Sample Location Markers -->
          ${generateSampleMarkers(currencyInfo)}
        </div>
      </div>

      <!-- Location Details Panel -->
      <div style="padding: 20px; background: white;">
        <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: 15px;">
          
          <!-- ATM Locations -->
          ${generateAtmLocationsList(currencyInfo)}
          
          <!-- Payment Acceptance -->
          ${generatePaymentAcceptanceList(currencyInfo)}
          
          <!-- Safety Information -->
          ${generateSafetyZonesList(currencyInfo)}
        </div>
      </div>

      <!-- Optimized Routes -->
      ${generateOptimizedRoutes(currencyInfo)}
    </div>

    <style>
      .map-filter:hover {
        background: var(--filter-color) !important;
        color: white !important;
      }
      
      .map-filter.active {
        background: var(--filter-color) !important;
        color: white !important;
      }
      
      .location-marker {
        position: absolute;
        width: 30px;
        height: 30px;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 14px;
        cursor: pointer;
        transition: transform 0.2s;
        box-shadow: 0 2px 4px rgba(0,0,0,0.2);
      }
      
      .location-marker:hover {
        transform: scale(1.2);
        z-index: 10;
      }
      
      .marker-atm { background: #28a745; color: white; }
      .marker-bank { background: #007bff; color: white; }
      .marker-restaurant { background: #ffc107; color: #212529; }
      .marker-risk { background: #dc3545; color: white; }
      .marker-safe { background: #17a2b8; color: white; }
    </style>

    <script>
      // Initialize map filters
      document.querySelectorAll('.map-filter').forEach(button => {
        button.addEventListener('click', function() {
          this.classList.toggle('active');
          const filter = this.dataset.filter;
          toggleMapFilter(filter);
        });
      });
      
      function toggleMapFilter(filterType) {
        const markers = document.querySelectorAll(\`[data-filter-type="\${filterType}"]\`);
        markers.forEach(marker => {
          marker.style.display = marker.style.display === 'none' ? 'flex' : 'none';
        });
      }
      
      // Location marker click handlers
      document.querySelectorAll('.location-marker').forEach(marker => {
        marker.addEventListener('click', function() {
          const info = this.dataset.info;
          showLocationDetails(info);
        });
      });
      
      function showLocationDetails(info) {
        alert('Location Details: ' + info);
        // In a real implementation, this would show a detailed popup
      }
    </script>
  `;
}

function generateSampleMarkers(currencyInfo: CurrencyInfo): string {
  // Generate sample markers based on currency info
  const markers = [];
  
  // ATM markers
  if (currencyInfo.exchangeInfo?.atmInfo) {
    markers.push(`
      <div class="location-marker marker-atm" data-filter-type="fee-free" data-info="Bank of China ATM - No fees for international cards" style="top: 30%; left: 25%;">🏧</div>
      <div class="location-marker marker-atm" data-filter-type="open-now" data-info="7-Eleven ATM - 24/7 access, English interface" style="top: 60%; left: 70%;">🏧</div>
    `);
  }
  
  // Restaurant markers
  if (currencyInfo.paymentMethods?.digitalPayments) {
    markers.push(`
      <div class="location-marker marker-restaurant" data-filter-type="alipay" data-info="Restaurant accepting Alipay and WeChat Pay" style="top: 45%; left: 50%;">🍽️</div>
      <div class="location-marker marker-restaurant" data-filter-type="cash-only" data-info="Traditional restaurant - Cash only" style="top: 70%; left: 30%;">🍜</div>
    `);
  }
  
  // Safety zones
  if (currencyInfo.safetyAndPrecautions?.localSafety) {
    markers.push(`
      <div class="location-marker marker-safe" data-filter-type="safe-zones" data-info="Safe area for ATM withdrawals" style="top: 25%; left: 60%;">🛡️</div>
      <div class="location-marker marker-risk" data-info="Tourist area - Watch for pickpockets" style="top: 80%; left: 80%;">⚠️</div>
    `);
  }
  
  return markers.join('');
}

function generateAtmLocationsList(currencyInfo: CurrencyInfo): string {
  if (!currencyInfo.exchangeInfo?.atmInfo) return '';
  
  return `
    <div style="background: #f8f9fa; padding: 15px; border-radius: 8px;">
      <h6 style="margin: 0 0 10px 0; color: #495057; display: flex; align-items: center;">
        <span style="margin-right: 8px;">🏧</span> Nearby ATMs
      </h6>
      ${currencyInfo.exchangeInfo.atmInfo.nearTouristSites ? `
        <div style="margin-bottom: 10px; padding: 10px; background: white; border-radius: 4px; border-left: 3px solid #28a745;">
          <p style="margin: 0; font-size: 0.85em; font-weight: 600;">Near Tourist Sites:</p>
          <p style="margin: 2px 0 0 0; font-size: 0.8em;">${currencyInfo.exchangeInfo.atmInfo.nearTouristSites}</p>
        </div>
      ` : ''}
      ${currencyInfo.exchangeInfo.atmInfo.freeAtmLocations ? `
        <div style="margin-bottom: 10px; padding: 10px; background: white; border-radius: 4px; border-left: 3px solid #007bff;">
          <p style="margin: 0; font-size: 0.85em; font-weight: 600;">💰 Fee-Free Options:</p>
          <p style="margin: 2px 0 0 0; font-size: 0.8em;">${currencyInfo.exchangeInfo.atmInfo.freeAtmLocations}</p>
        </div>
      ` : ''}
    </div>
  `;
}

function generatePaymentAcceptanceList(currencyInfo: CurrencyInfo): string {
  if (!currencyInfo.interactiveLocations) return '';
  
  return `
    <div style="background: #f8f9fa; padding: 15px; border-radius: 8px;">
      <h6 style="margin: 0 0 10px 0; color: #495057; display: flex; align-items: center;">
        <span style="margin-right: 8px;">💳</span> Payment Acceptance
      </h6>
      <div style="margin-bottom: 10px; padding: 10px; background: white; border-radius: 4px;">
        <p style="margin: 0; font-size: 0.8em;">${currencyInfo.interactiveLocations.paymentAcceptance}</p>
      </div>
    </div>
  `;
}

function generateSafetyZonesList(currencyInfo: CurrencyInfo): string {
  if (!currencyInfo.safetyAndPrecautions?.localSafety) return '';
  
  return `
    <div style="background: #f8f9fa; padding: 15px; border-radius: 8px;">
      <h6 style="margin: 0 0 10px 0; color: #495057; display: flex; align-items: center;">
        <span style="margin-right: 8px;">🛡️</span> Safety Zones
      </h6>
      ${currencyInfo.safetyAndPrecautions.localSafety.safeZones ? `
        <div style="margin-bottom: 8px; padding: 8px; background: #d4edda; border-radius: 4px; border-left: 3px solid #28a745;">
          <p style="margin: 0; font-size: 0.8em; font-weight: 600;">✅ Safe Areas:</p>
          <p style="margin: 2px 0 0 0; font-size: 0.75em;">${currencyInfo.safetyAndPrecautions.localSafety.safeZones}</p>
        </div>
      ` : ''}
      ${currencyInfo.safetyAndPrecautions.localSafety.riskZones ? `
        <div style="padding: 8px; background: #f8d7da; border-radius: 4px; border-left: 3px solid #dc3545;">
          <p style="margin: 0; font-size: 0.8em; font-weight: 600;">⚠️ Risk Areas:</p>
          <p style="margin: 2px 0 0 0; font-size: 0.75em;">${currencyInfo.safetyAndPrecautions.localSafety.riskZones}</p>
        </div>
      ` : ''}
    </div>
  `;
}

function generateOptimizedRoutes(currencyInfo: CurrencyInfo): string {
  if (!currencyInfo.interactiveLocations?.optimizedRoutes) return '';
  
  return `
    <div style="margin-top: 20px; padding: 20px; background: linear-gradient(135deg, #ffecd2 0%, #fcb69f 100%); border-radius: 8px;">
      <h5 style="margin: 0 0 15px 0; color: #495057; display: flex; align-items: center;">
        <span style="margin-right: 10px; font-size: 1.2em;">🗺️</span> Optimized Routes
      </h5>
      <div style="background: rgba(255,255,255,0.8); padding: 15px; border-radius: 6px;">
        <p style="margin: 0; font-size: 0.9em; line-height: 1.4;">${currencyInfo.interactiveLocations.optimizedRoutes}</p>
      </div>
    </div>
  `;
}
