/**
 * Core types for trip planning workflows
 * These types are shared between different workflow implementations
 */

/**
 * Interface for currency denomination (bills and coins)
 */
export interface CurrencyDenomination {
  value: number;
  type: 'bill' | 'coin';
  description?: string;
  color?: string;
  material?: string;
  size?: string;
  imageUrl?: string;
}

/**
 * Interface for enhanced currency information with practical travel details
 */
export interface CurrencyInfo {
  code: string;
  symbol: string;
  name: string;
  country: string;

  // Enhanced financial information for travelers
  exchangeInfo?: {
    currentRate: {
      usd: number;
      eur: number;
      lastUpdated: string;
    };
    bestExchangeLocations: Array<{
      type: string;
      name: string;
      fees: string;
      rate: string;
      locations: string;
      openingHours: string;
      notes?: string;
    }>;
    atmInfo: {
      networks: string[];
      fees: string;
      dailyLimits: string;
      locations: string;
      languageSupport: string;
      concreteExample: string;
      freeAtmLocations: string;
      nearTouristSites: string;
    };
  };

  paymentMethods?: {
    creditCards: {
      acceptance: string;
      preferredCards: string[];
      fees: string;
      restrictions: string;
      contactless: string;
    };
    cash: {
      importance: string;
      recommendations: string;
      usefulDenominations: string;
      cashOnlyPlaces: string;
    };
    digitalPayments: {
      localApps: string[];
      internationalAccess: string;
      accessibilityDetails: string;
      modernSolutions: string[];
      qrCodePayments: string;
    };
    transportCards: {
      available: string[];
      touristFriendly: string;
      depositRefund: string;
      usageAreas: string;
      purchaseLocations: string;
      cardImages: string;
    };
  };

  tipping?: {
    culture: string;
    restaurants: string;
    taxis: string;
    hotels: string;
    other: string;
  };

  typicalCosts?: {
    meals: {
      budget: string;
      midRange: string;
      upscale: string;
    };
    transport: {
      publicTransport: string;
      taxi: string;
      rideshare: string;
    };
    accommodation: {
      budget: string;
      midRange: string;
      luxury: string;
    };
  };

  safetyAndPrecautions?: {
    avoidFees: string;
    cardActivation: string;
    safetyTips: string;
    backupPayments: string;
    localSafety: {
      atmSafety: string;
      cashLimits: string;
      cardSkimming: string;
      emergencyAccess: string;
      riskZones: string;
      safeZones: string;
    };
  };

  practicalTips?: {
    bestStrategy: string;
    avoidTouristTraps: string;
    specialConsiderations: string;
    costMinimization: string;
  };

  concreteExamples?: {
    atmWithdrawal: string;
    restaurantPayment: string;
    transportPayment: string;
    shoppingPayment: string;
    hotelPayment: string;
  };

  interactiveLocations?: {
    atmLocations: string;
    paymentAcceptance: string;
    optimizedRoutes: string;
  };

  denominations?: {
    bills: CurrencyDenomination[];
    coins: CurrencyDenomination[];
  };
}

/**
 * Interface for destination information
 */
export interface DestinationInfo {
  destination: string;
  country: string;
  duration: number | null;
  coordinates: {
    lat: string;
    lng: string;
  };
}

/**
 * Interface for a single activity in a day
 */
export interface Activity {
  time: string;
  activity: string;
  location: string;
  description: string;
  poiIndex?: number;
}

/**
 * Interface for a day's itinerary
 */
export interface DayItinerary {
  day: number;
  activities: Activity[];
}

/**
 * Interface for a point of interest
 */
export interface PointOfInterest {
  name: string;
  lat: string;
  lng: string;
  day: string;
  type: 'attraction' | 'restaurant' | 'hotel';
  description: string;
}

/**
 * Interface for local phrases
 */
export interface LocalPhrase {
  phrase: string;
  translation: string;
  pronunciation: string;
}

/**
 * Interface for travel tips
 */
export interface TravelTip {
  category: string;
  tips: string[];
}

/**
 * Interface for budget information
 */
export interface BudgetItem {
  category: string;
  estimatedCost: string;
  notes: string;
}

/**
 * Base interface for trip plan
 */
export interface TripPlan {
  destination: DestinationInfo;
  days: DayItinerary[];
  pois: PointOfInterest[];
  localPhrases: LocalPhrase[];
  travelTips: TravelTip[];
  budget: BudgetItem[];
  heroImage?: string;
  specializedActivityData?: any;
  tripClassification?: any;
}
