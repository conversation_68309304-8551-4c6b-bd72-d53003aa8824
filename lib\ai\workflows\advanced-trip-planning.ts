import { z } from 'zod';
import { generateObject, type UIMessageStreamWriter } from 'ai';
import type { ChatMessage } from '@/lib/types';
import { myProvider } from '@/lib/ai/providers';
import { DestinationAgent } from './agents/destination-agent';
import {
  OrchestratorAgent,
  type ComprehensiveTripPlan,
} from './agents/orchestrator-agent';
import { web_search } from '@/lib/ai/tools/web-search';
import { ClassifierAgent } from './agents/classifier-agent';
import {
  ValidationAgent,
  type ValidationResult,
} from './agents/validation-agent';

// Enhanced layout CSS with travel tips styling
const SIMPLE_LAYOUT_CSS = `
/* Simple grid layout styles */
.simple-layout-section {
  margin: 30px 0;
}

.items-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 20px;
}

.item-card {
  border: 1px solid #ddd;
  border-radius: 12px;
  padding: 20px;
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
  box-shadow: 0 4px 6px rgba(0,0,0,0.1);
  transition: transform 0.3s ease;
}

.item-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 8px 15px rgba(0,0,0,0.15);
}

.item-card img {
  width: 100%;
  height: 200px;
  object-fit: cover;
  border-radius: 8px;
  margin-bottom: 15px;
}

.item-card h4 {
  color: #2c3e50;
  margin-bottom: 10px;
  font-size: 1.2em;
}

.item-card p {
  color: #555;
  line-height: 1.6;
  margin-bottom: 15px;
}

.item-details {
  font-size: 0.9em;
}

.item-details div {
  margin-bottom: 8px;
}

/* Travel Tips Interactive Styles */
.travel-tips-container {
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

.tab-navigation {
  scrollbar-width: thin;
  scrollbar-color: #667eea #f1f1f1;
}

.tab-navigation::-webkit-scrollbar {
  height: 4px;
}

.tab-navigation::-webkit-scrollbar-track {
  background: #f1f1f1;
}

.tab-navigation::-webkit-scrollbar-thumb {
  background: #667eea;
  border-radius: 2px;
}

.tab-btn:hover {
  background: #5a67d8 !important;
  color: white !important;
  border-bottom-color: #4c51bf !important;
}

.tab-panel {
  animation: fadeIn 0.3s ease-in-out;
}

@keyframes fadeIn {
  from { opacity: 0; transform: translateY(10px); }
  to { opacity: 1; transform: translateY(0); }
}

/* Responsive design for travel tips */
@media (max-width: 768px) {
  .tab-navigation {
    flex-direction: column;
  }

  .tab-btn {
    min-width: auto !important;
    width: 100% !important;
  }
}

/* TradingView Widget Styles */
.tradingview-widget-container {
  width: 100%;
  position: relative;
}

.tradingview-widget-container__widget {
  width: 100%;
  min-height: 400px;
}

.tradingview-widget-container iframe {
  width: 100% !important;
  border-radius: 8px;
  border: none;
}

/* Responsive TradingView widget */
@media (max-width: 768px) {
  .tradingview-widget-container__widget {
    min-height: 300px;
  }
}

/* Restaurant Carousel Styles */
.restaurant-carousel {
  margin: 30px 0;
  position: relative;
}

.carousel-container {
  position: relative;
  display: flex;
  align-items: center;
}

.carousel-track {
  display: flex;
  overflow-x: auto;
  scroll-behavior: smooth;
  gap: 20px;
  padding: 10px 0;
  scrollbar-width: none;
  -ms-overflow-style: none;
}

.carousel-track::-webkit-scrollbar {
  display: none;
}

.restaurant-slide {
  flex: 0 0 300px;
  min-width: 300px;
}

.carousel-btn {
  position: absolute;
  top: 50%;
  transform: translateY(-50%);
  background: rgba(0,0,0,0.7);
  color: white;
  border: none;
  width: 40px;
  height: 40px;
  border-radius: 50%;
  font-size: 20px;
  cursor: pointer;
  z-index: 10;
  transition: background 0.3s ease;
}

.carousel-btn:hover {
  background: rgba(0,0,0,0.9);
}

.prev-btn {
  left: -20px;
}

.next-btn {
  right: -20px;
}

.restaurant-slide .card {
  background: white;
  border-radius: 12px;
  box-shadow: 0 4px 12px rgba(0,0,0,0.1);
  overflow: hidden;
  height: 350px;
  display: flex;
  flex-direction: column;
  transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.restaurant-slide .card:hover {
  transform: translateY(-5px);
  box-shadow: 0 8px 25px rgba(0,0,0,0.15);
}

.restaurant-slide .card img {
  width: 100%;
  height: 200px;
  object-fit: cover;
}

.restaurant-slide .card-content {
  padding: 15px;
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}

/* Responsive carousel */
@media (max-width: 768px) {
  .restaurant-slide {
    flex: 0 0 250px;
    min-width: 250px;
  }

  .carousel-btn {
    width: 35px;
    height: 35px;
    font-size: 18px;
  }

  .prev-btn {
    left: -15px;
  }

  .next-btn {
    right: -15px;
  }
}

/* Currency Bills and Coins Styles */
.currency-bill {
  transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.currency-bill:hover {
  transform: translateY(-3px) scale(1.05);
  box-shadow: 0 6px 20px rgba(0,0,0,0.25) !important;
}

.currency-coin {
  transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.currency-coin:hover {
  transform: translateY(-2px) scale(1.1);
  box-shadow: 0 4px 15px rgba(0,0,0,0.3) !important;
}

.currency-denomination-grid {
  animation: fadeInUp 0.6s ease-out;
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Responsive currency display */
@media (max-width: 768px) {
  .currency-bill, .currency-coin {
    font-size: 0.9em;
  }
}
`;

// No carousel JavaScript needed - simple layouts only

// Enhanced JavaScript with travel tips interactivity and carousel functionality
const SIMPLE_LAYOUT_JS = `
// Carousel functionality
window.scrollCarousel = function(carouselId, direction) {
  console.log('🎠 Scrolling carousel:', carouselId, 'direction:', direction);
  const carousel = document.querySelector('#' + carouselId + ' .carousel-track');
  if (carousel) {
    const scrollAmount = 320;
    carousel.scrollBy({ left: direction * scrollAmount, behavior: 'smooth' });
    console.log('✅ Carousel scrolled');
  } else {
    console.warn('❌ Carousel not found:', carouselId);
  }
};

// Define the function immediately and globally
window.showTravelTipTab = function(tabId) {
  console.log('🎯 Switching to travel tip tab:', tabId);

  // Hide all tab panels
  const panels = document.querySelectorAll('.tab-panel');
  console.log('📋 Found', panels.length, 'tab panels');
  panels.forEach(panel => {
    panel.style.display = 'none';
  });

  // Remove active class from all buttons
  const buttons = document.querySelectorAll('.tab-btn');
  console.log('🔘 Found', buttons.length, 'tab buttons');
  buttons.forEach(btn => {
    btn.classList.remove('active');
    btn.style.background = '#f8f9fa';
    btn.style.color = '#666';
    btn.style.borderBottomColor = 'transparent';
  });

  // Show selected panel
  const selectedPanel = document.getElementById(tabId);
  if (selectedPanel) {
    selectedPanel.style.display = 'block';
    console.log('✅ Showed panel:', tabId);
  } else {
    console.warn('❌ Panel not found:', tabId);
  }

  // Activate selected button
  const selectedButton = document.querySelector('[data-tab="' + tabId + '"]');
  if (selectedButton) {
    selectedButton.classList.add('active');
    selectedButton.style.background = '#667eea';
    selectedButton.style.color = 'white';
    selectedButton.style.borderBottomColor = '#5a67d8';
    console.log('✅ Activated button for:', tabId);
  } else {
    console.warn('❌ Button not found for:', tabId);
  }
};

// Also make it available as a global function (for compatibility)
function showTravelTipTab(tabId) {
  window.showTravelTipTab(tabId);
}

function scrollCarousel(carouselId, direction) {
  window.scrollCarousel(carouselId, direction);
}

console.log('🎯 Travel tips function defined globally');
console.log('🎠 Carousel functions defined globally');

// Simple initialization
document.addEventListener('DOMContentLoaded', function() {
  console.log('🎯 Travel tips interface ready');
  console.log('🎠 Carousel interface ready');

  // Debug: Log all tab buttons found
  setTimeout(() => {
    const tabButtons = document.querySelectorAll('.tab-btn');
    console.log('🔍 Found', tabButtons.length, 'tab buttons:');
    tabButtons.forEach((btn, index) => {
      const tabId = btn.getAttribute('data-tab');
      console.log('  ' + (index + 1) + '. Button with data-tab="' + tabId + '"');
    });

    // Debug: Log all tab panels found
    const tabPanels = document.querySelectorAll('.tab-panel');
    console.log('🔍 Found', tabPanels.length, 'tab panels:');
    tabPanels.forEach((panel, index) => {
      console.log('  ' + (index + 1) + '. Panel with id="' + panel.id + '"');
    });

    // Debug: Log all carousels found
    const carousels = document.querySelectorAll('.restaurant-carousel, .hotel-carousel, .poi-carousel');
    console.log('🔍 Found', carousels.length, 'carousels:');
    carousels.forEach((carousel, index) => {
      console.log('  ' + (index + 1) + '. Carousel with id="' + carousel.id + '"');
    });
  }, 500);
});

console.log('💰 Enhanced travel tips with interactive Money Guide loaded');
console.log('🎠 Carousel functionality loaded');
`;

// All remaining carousel functions removed

// All carousel functions completely removed

// Define types for the advanced trip planning workflow
export interface AdvancedTripPlanningInput {
  query: string;
  dataStream: UIMessageStreamWriter<ChatMessage>;
}

export interface AdvancedTripPlanningOutput {
  htmlContent: string;
  cssContent: string;
  jsContent: string;
}

/**
 * Function to get a hero image URL for a destination
 */
async function getDestinationHeroImage(
  destination: string,
  country: string,
): Promise<string | null> {
  try {
    // Create a mock session and dataStream for the web_search tool
    const mockSession = {
      user: { id: 'system', type: 'guest' as const },
      expires: new Date(Date.now() + 24 * 60 * 60 * 1000).toISOString(),
    };
    // Create a mock UIMessageStreamWriter compatible with web_search
    const mockDataStream: UIMessageStreamWriter<ChatMessage> = {
      write: () => {},
    } as any;

    // Use the web_search tool to search for images of the destination
    const searchTool = web_search({
      session: mockSession,
      dataStream: mockDataStream,
    });

    // Try multiple search queries to increase chances of finding good images
    const searchQueries = [
      `beautiful ${destination} ${country} travel photos high resolution`,
      `${destination} ${country} landmark tourist attractions photos`,
      `${destination} ${country} travel guide images scenic`,
      `${destination} ${country} tourism official photos high quality`,
      `${destination} ${country} scenic views panorama`,
    ];

    // Execute all search queries in parallel with specific parameters to get images
    const searchPromises = searchQueries.map((query) =>
      (searchTool.execute as any)({
        queries: [query],
        maxResults: [10], // Increase max results to get more content
        topics: ['general'],
        searchDepth: ['advanced'], // Use advanced search for better results
      }),
    );

    const searchResults = await Promise.all(searchPromises);

    // Extract image URLs from all search results
    const imageUrls: string[] = [];

    // First, try to get images directly from the search results
    for (const result of searchResults) {
      if (
        result?.searches?.[0]?.images &&
        Array.isArray(result.searches[0].images)
      ) {
        // Check if images are objects with url property or direct strings
        const images = result.searches[0].images;
        for (const image of images) {
          if (typeof image === 'string') {
            imageUrls.push(image);
          } else if (image && typeof image === 'object' && image.url) {
            imageUrls.push(image.url);
          }
        }
      }
    }

    console.log(`Found ${imageUrls.length} direct images from search results`);

    // Process all search results
    for (const results of searchResults) {
      if (results?.searches?.[0]?.results?.length > 0) {
        for (const result of results.searches[0].results) {
          // Look for image URLs in the search results - try multiple patterns

          // Pattern 1: Direct image URLs
          const directImageMatches = result.content.matchAll(
            /(https?:\/\/[^"\s]+\.(?:jpg|jpeg|png|webp)(?:\?[^"\s]*)?)/gi,
          );

          for (const match of Array.from(directImageMatches)) {
            const m = match as RegExpExecArray;
            if (m[1] && !imageUrls.includes(m[1])) {
              imageUrls.push(m[1]);
            }
          }

          // Pattern 2: Image URLs in HTML/markdown image tags
          const imgTagMatches = result.content.matchAll(
            /(?:src|href)=["'](https?:\/\/[^"']+\.(?:jpg|jpeg|png|webp)(?:\?[^"']*)?)['"]/gi,
          );

          for (const match of Array.from(imgTagMatches)) {
            const m = match as RegExpExecArray;
            if (m[1] && !imageUrls.includes(m[1])) {
              imageUrls.push(m[1]);
            }
          }

          // Pattern 3: Look for image URLs in markdown format
          const markdownMatches = result.content.matchAll(
            /!\[.*?\]\((https?:\/\/[^)]+\.(?:jpg|jpeg|png|webp)(?:\?[^)]*)?)\)/gi,
          );

          for (const match of Array.from(markdownMatches)) {
            const m = match as RegExpExecArray;
            if (m[1] && !imageUrls.includes(m[1])) {
              imageUrls.push(m[1]);
            }
          }
        }
      }
    }

    // Filter out low-quality or unwanted images
    const filteredUrls = imageUrls.filter((url) => {
      // Filter out small images (often icons or thumbnails)
      if (
        url.includes('icon') ||
        url.includes('thumb') ||
        url.includes('small')
      ) {
        return false;
      }

      // Prefer high-quality images
      if (
        url.includes('large') ||
        url.includes('high') ||
        url.includes('full') ||
        url.includes('1200') ||
        url.includes('1600') ||
        url.includes('2000')
      ) {
        return true;
      }

      return true; // Include by default
    });

    // If we have enough images, return one
    if (filteredUrls.length > 0) {
      // Choose a random image from the top 5 to get variety
      const randomIndex = Math.floor(
        Math.random() * Math.min(5, filteredUrls.length),
      );
      return filteredUrls[randomIndex];
    }

    // If we still don't have good images, try a direct image search
    try {
      console.log('Trying direct image search...');

      // Use a more specific image search query
      const imageSearchQuery = `${destination} ${country} travel destination landscape photography`;

      const imageSearchResult = await (searchTool.execute as any)({
        queries: [imageSearchQuery],
        maxResults: [15],
        topics: ['general'],
        searchDepth: ['advanced'],
      });

      // Extract images from the image search
      const directImageUrls: string[] = [];

      if (
        imageSearchResult?.searches?.[0]?.images &&
        Array.isArray(imageSearchResult.searches[0].images)
      ) {
        const images = imageSearchResult.searches[0].images;
        for (const image of images) {
          if (typeof image === 'string') {
            directImageUrls.push(image);
          } else if (image && typeof image === 'object' && image.url) {
            directImageUrls.push(image.url);
          }
        }
      }

      console.log(
        `Found ${directImageUrls.length} images from direct image search`,
      );

      if (directImageUrls.length > 0) {
        // Choose a random image from the direct image search results
        const randomIndex = Math.floor(
          Math.random() * Math.min(5, directImageUrls.length),
        );
        return directImageUrls[randomIndex];
      }
    } catch (imageSearchError) {
      console.error('Error during direct image search:', imageSearchError);
    }

    // Fallback to web image search if absolutely no images were found
    console.log('No images found, trying web image search as final fallback');
    return await getWebImageWithFallback(
      `${destination} ${country} travel destination`,
      'attraction',
      destination,
    );
  } catch (error) {
    console.error('Error getting destination hero image:', error);
    // Return a real destination image instead of SVG fallback
    return await getDestinationImageAsync(destination, country);
  }
}

/**
 * Intelligently convert content to carousels using LLM analysis
 */
async function intelligentCarouselConversion(
  htmlContent: string,
): Promise<string> {
  console.log('Starting intelligent carousel conversion using LLM...');

  try {
    // Use the LLM to analyze the HTML content and identify sections that should be carousels
    const { object } = await generateObject({
      model: myProvider.languageModel('artifact-model'),
      system: `You are an expert HTML analyzer. Your task is to identify sections in HTML content that should be converted to carousels.

      Look for sections containing:
      1. Restaurant/dining information (restaurants, cafes, food places)
      2. Accommodation/hotel information (hotels, hostels, lodging)
      3. Points of interest/attractions (museums, landmarks, local recommendations)

      For each section you identify, extract the individual items and their details.

      Return a JSON object with sections to convert, where each section contains:
      - type: "restaurant", "hotel", or "poi"
      - items: array of objects with name, description, and extra info
      - originalHtml: the original HTML content to replace
      - confidence: confidence level (0-1) that this should be a carousel`,
      prompt: `Analyze this HTML content and identify sections that should be converted to carousels:

${htmlContent}

Look for any content that represents:
1. Restaurants, dining options, food places (including "Traditional Restaurant", "Local - Moderate to Expensive", etc.)
2. Hotels, accommodations, lodging
3. Tourist attractions, local recommendations, points of interest (including "Complesso Monumentale", "Santa Maria", "La Cantina", etc.)

Extract the individual items from each section with their names, descriptions, and any additional info (prices, ratings, etc.).
Be very thorough and look for content that might be in paragraphs, lists, or any other format.`,
      schema: z.object({
        sections: z.array(
          z.object({
            type: z.enum(['restaurant', 'hotel', 'poi']),
            items: z.array(
              z.object({
                name: z.string(),
                description: z.string(),
                extra: z.string().optional(),
              }),
            ),
            originalHtml: z.string(),
            confidence: z.number().min(0).max(1),
          }),
        ),
      }),
      temperature: 0.3, // Low temperature for consistent analysis
    });

    let modifiedHtml = htmlContent;

    // Process each identified section with async/await
    for (const section of object.sections) {
      if (section.confidence > 0.6 && section.items.length > 0) {
        console.log(
          `Converting ${section.type} section with ${section.items.length} items (confidence: ${section.confidence})`,
        );

        let carouselHtml = '';

        switch (section.type) {
          case 'restaurant':
            carouselHtml = await createSimpleLayout(
              section.items.map((item: any) => ({
                name: item.name,
                description: item.description,
                extra: item.extra || '⭐⭐⭐⭐ 4.0/5',
                category: item.cuisine || 'Local Cuisine',
                rating: item.rating,
                type: 'restaurant',
              })),
              'restaurant',
              'destination', // We don't have destination context here, using generic
            );
            break;
          case 'hotel':
            carouselHtml = await createSimpleLayout(
              section.items.map((item: any) => ({
                name: item.name,
                description: item.description,
                extra: item.extra || 'Contact for pricing',
                category: 'Premium Accommodation',
                rating: item.rating,
                type: 'hotel',
              })),
              'hotel',
              'destination', // We don't have destination context here, using generic
            );
            break;
          case 'poi':
            carouselHtml = await createSimpleLayout(
              section.items.map((item: any) => ({
                name: item.name,
                description: item.description,
                extra: item.extra || 'Point of Interest',
                category: item.category || 'Attraction',
                rating: item.rating,
                type: 'poi',
              })),
              'poi',
              'destination', // We don't have destination context here, using generic
            );
            break;
        }

        // Replace the original content with the carousel
        if (carouselHtml && section.originalHtml) {
          modifiedHtml = modifiedHtml.replace(
            section.originalHtml,
            carouselHtml,
          );
        }
      }
    }

    return modifiedHtml;
  } catch (error) {
    console.error('Error in intelligent carousel conversion:', error);
    // Fallback to original content if LLM analysis fails
    return htmlContent;
  }
}

// Legacy createHotelCarousel function removed - using premium carousel system

// Legacy restaurant functions removed - using premium carousel system

// Legacy POI functions removed - using premium carousel system

// Rate limiting for image API calls
const imageApiRateLimit = {
  lastCall: 0,
  minInterval: 1000, // 1 second between calls
  requestQueue: [] as Array<() => Promise<void>>,
  isProcessing: false,
};

// Image cache to avoid repeated API calls
const imageCache = new Map<string, string>();

// Fallback SVG images by category (no more Unsplash)
// Cache global pour les images de destinations
const destinationImageCache = new Map<string, string>();

const fallbackImages = {
  hotel: [
    'https://images.unsplash.com/photo-1566073771259-6a8506099945?w=400&h=200&fit=crop&crop=center',
    'https://images.unsplash.com/photo-1551882547-ff40c63fe5fa?w=400&h=200&fit=crop&crop=center',
    'https://images.unsplash.com/photo-1582719478250-c89cae4dc85b?w=400&h=200&fit=crop&crop=center',
  ],
  restaurant: [
    'https://images.unsplash.com/photo-1517248135467-4c7edcad34c4?w=400&h=200&fit=crop&crop=center',
    'https://images.unsplash.com/photo-1555396273-367ea4eb4db5?w=400&h=200&fit=crop&crop=center',
    'https://images.unsplash.com/photo-1514933651103-005eec06c04b?w=400&h=200&fit=crop&crop=center',
  ],
  attraction: [
    'https://images.unsplash.com/photo-1539650116574-75c0c6d73f6e?w=400&h=200&fit=crop&crop=center',
    'https://images.unsplash.com/photo-1513635269975-59663e0ac1ad?w=400&h=200&fit=crop&crop=center',
    'https://images.unsplash.com/photo-1506905925346-21bda4d32df4?w=400&h=200&fit=crop&crop=center',
  ],
};

/**
 * Get a high-quality fallback image based on category
 */
/**
 * Get destination image using intelligent web search (synchronous version for templates)
 */
function getDestinationImageSync(
  destination: string,
  country?: string,
): string | null {
  const cacheKey = `${destination}_${country || ''}`.toLowerCase();

  // Return cached image if available (from previous searches)
  if (destinationImageCache.has(cacheKey)) {
    const cachedImage = destinationImageCache.get(cacheKey);
    if (cachedImage) {
      console.log(`[Smart Image] ✅ Using cached image for ${destination}`);
      return cachedImage;
    }
  }

  // No fallback - return null if no cached image available
  console.log(
    `[Smart Image] ❌ No cached image for ${destination}, returning null`,
  );

  return null;
}

/**
 * Get specific day image based on activities
 */
async function getDaySpecificImage(
  dayNumber: number,
  activities: any[],
  destination: string,
  country?: string,
): Promise<string | null> {
  // Create a unique cache key for this specific day
  const activitiesText = activities
    .map((a) => a.activity || a.location || '')
    .join(' ');
  const cacheKey = `day_${dayNumber}_${destination}_${activitiesText}`
    .toLowerCase()
    .replace(/[^a-z0-9_]/g, '');

  // Check cache first
  if (destinationImageCache.has(cacheKey)) {
    const cachedImage = destinationImageCache.get(cacheKey);
    if (cachedImage) {
      return cachedImage;
    }
  }

  // Build search query based on day activities
  let searchQuery = '';
  if (activities.length > 0) {
    const mainActivity = activities[0];
    const activityName = mainActivity.activity || mainActivity.location || '';
    const location = mainActivity.location || destination;

    // Create specific search based on activity type
    if (activityName.toLowerCase().includes('museum')) {
      searchQuery = `${location} ${destination} museum art culture interior`;
    } else if (
      activityName.toLowerCase().includes('church') ||
      activityName.toLowerCase().includes('cathedral')
    ) {
      searchQuery = `${location} ${destination} church cathedral architecture religious`;
    } else if (
      activityName.toLowerCase().includes('park') ||
      activityName.toLowerCase().includes('garden')
    ) {
      searchQuery = `${location} ${destination} park garden nature green`;
    } else if (
      activityName.toLowerCase().includes('market') ||
      activityName.toLowerCase().includes('shopping')
    ) {
      searchQuery = `${location} ${destination} market shopping street local`;
    } else if (
      activityName.toLowerCase().includes('restaurant') ||
      activityName.toLowerCase().includes('food')
    ) {
      searchQuery = `${location} ${destination} restaurant food cuisine local dining`;
    } else if (
      activityName.toLowerCase().includes('beach') ||
      activityName.toLowerCase().includes('sea')
    ) {
      searchQuery = `${location} ${destination} beach sea ocean coast`;
    } else if (
      activityName.toLowerCase().includes('mountain') ||
      activityName.toLowerCase().includes('hiking')
    ) {
      searchQuery = `${location} ${destination} mountain hiking nature landscape`;
    } else {
      // Generic activity-based search
      searchQuery = `${activityName} ${location} ${destination} ${country || ''} tourist attraction`;
    }
  } else {
    // Fallback to destination with day variation
    const dayVariations = [
      'landmark',
      'architecture',
      'street',
      'culture',
      'local life',
      'scenic view',
    ];
    const variation = dayVariations[(dayNumber - 1) % dayVariations.length];
    searchQuery = `${destination} ${country || ''} ${variation} travel`;
  }

  console.log(`[Day Image] Day ${dayNumber} search: "${searchQuery}"`);

  try {
    // Try Serper API first
    const apiKey = process.env.SERPER_API as string;
    if (apiKey) {
      const url = `https://google.serper.dev/images?q=${encodeURIComponent(searchQuery)}`;

      const response = await fetch(url, {
        method: 'GET',
        headers: {
          'X-API-KEY': apiKey,
          'Content-Type': 'application/json',
        },
      });

      if (response.ok) {
        const data = await response.json();
        if (data.images && data.images.length > 0) {
          const validImages = data.images.filter(
            (img: any) =>
              img.imageUrl &&
              !img.imageUrl.includes('data:image') &&
              (img.imageWidth || 0) >= 400 &&
              (img.imageHeight || 0) >= 200 &&
              !img.imageUrl.includes('icon') &&
              !img.imageUrl.includes('thumb'),
          );

          if (validImages.length > 0) {
            // Use different image based on day number to ensure variety
            const imageIndex = (dayNumber - 1) % validImages.length;
            const imageUrl = validImages[imageIndex].imageUrl;
            destinationImageCache.set(cacheKey, imageUrl);
            console.log(
              `[Day Image] ✅ Day ${dayNumber} image found: ${imageUrl}`,
            );
            return imageUrl;
          }
        }
      }
    }

    // Fallback to web search tool
    const mockSession = {
      user: { id: 'system', type: 'guest' as const },
      expires: new Date(Date.now() + 24 * 60 * 60 * 1000).toISOString(),
    };

    const mockDataStream: UIMessageStreamWriter<ChatMessage> = {
      write: () => {},
    } as any;

    const searchTool = web_search({
      session: mockSession,
      dataStream: mockDataStream,
    });

    const searchResult = await (searchTool.execute as any)({
      queries: [searchQuery],
      maxResults: [5],
      topics: ['general'],
      searchDepth: ['advanced'],
    });

    if (
      searchResult?.searches?.[0]?.images &&
      Array.isArray(searchResult.searches[0].images)
    ) {
      const images = searchResult.searches[0].images;
      for (let i = 0; i < images.length; i++) {
        const image = images[(i + dayNumber - 1) % images.length]; // Rotate based on day
        let imageUrl = '';
        if (typeof image === 'string') {
          imageUrl = image;
        } else if (image && typeof image === 'object' && image.url) {
          imageUrl = image.url;
        }

        if (
          imageUrl &&
          !imageUrl.includes('data:image') &&
          !imageUrl.includes('icon')
        ) {
          destinationImageCache.set(cacheKey, imageUrl);
          console.log(
            `[Day Image] ✅ Day ${dayNumber} image via web search: ${imageUrl}`,
          );
          return imageUrl;
        }
      }
    }
  } catch (error) {
    console.error(`[Day Image] Error for day ${dayNumber}:`, error);
  }

  // No generic fallback - return null if web search fails
  console.log(
    `[Day Image] ❌ No image found for day ${dayNumber}, returning null`,
  );
  return null;
}

/**
 * Get destination image using intelligent web search (async version)
 */
async function getDestinationImageAsync(
  destination: string,
  country?: string,
): Promise<string | null> {
  const cacheKey = `${destination}_${country || ''}`.toLowerCase();

  // Check cache first
  if (destinationImageCache.has(cacheKey)) {
    const cachedImage = destinationImageCache.get(cacheKey);
    if (cachedImage) {
      return cachedImage;
    }
  }

  console.log(
    `[Smart Image] Searching for: "${destination} ${country || ''} travel landmark tourist destination beautiful"`,
  );

  try {
    // Try Serper API first
    const apiKey = process.env.SERPER_API as string;
    if (apiKey) {
      const searchQuery = `${destination} ${country || ''} travel landmark tourist destination beautiful`;
      const url = `https://google.serper.dev/images?q=${encodeURIComponent(searchQuery)}`;

      const response = await fetch(url, {
        method: 'GET',
        headers: {
          'X-API-KEY': apiKey,
          'Content-Type': 'application/json',
        },
      });

      if (response.ok) {
        const data = await response.json();
        if (data.images && data.images.length > 0) {
          const validImages = data.images.filter(
            (img: any) =>
              img.imageUrl &&
              !img.imageUrl.includes('data:image') &&
              (img.imageWidth || 0) >= 400 &&
              (img.imageHeight || 0) >= 200 &&
              !img.imageUrl.includes('icon') &&
              !img.imageUrl.includes('thumb'),
          );

          if (validImages.length > 0) {
            const imageUrl = validImages[0].imageUrl;
            destinationImageCache.set(cacheKey, imageUrl);
            console.log(`[Smart Image] ✅ Image found via Serper: ${imageUrl}`);
            return imageUrl;
          }
        }
      }
    }

    // Fallback to web search tool
    const mockSession = {
      user: { id: 'system', type: 'guest' as const },
      expires: new Date(Date.now() + 24 * 60 * 60 * 1000).toISOString(),
    };

    const mockDataStream: UIMessageStreamWriter<ChatMessage> = {
      write: () => {},
    } as any;

    const searchTool = web_search({
      session: mockSession,
      dataStream: mockDataStream,
    });

    const searchQuery = `${destination} ${country || ''} travel landmark tourist destination beautiful`;
    const searchResult = await (searchTool.execute as any)({
      queries: [searchQuery],
      maxResults: [5],
      topics: ['general'],
      searchDepth: ['advanced'],
    });

    if (
      searchResult?.searches?.[0]?.images &&
      Array.isArray(searchResult.searches[0].images)
    ) {
      const images = searchResult.searches[0].images;
      for (const image of images) {
        let imageUrl = '';
        if (typeof image === 'string') {
          imageUrl = image;
        } else if (image && typeof image === 'object' && image.url) {
          imageUrl = image.url;
        }

        if (
          imageUrl &&
          !imageUrl.includes('data:image') &&
          !imageUrl.includes('icon')
        ) {
          destinationImageCache.set(cacheKey, imageUrl);
          console.log(
            `[Smart Image] ✅ Image found via web search: ${imageUrl}`,
          );
          return imageUrl;
        }
      }
    }
  } catch (error) {
    console.error(`[Smart Image] Error searching for ${destination}:`, error);
  }

  // No fallback - return null if search fails
  console.log(
    `[Smart Image] ❌ No image found for ${destination}, returning null`,
  );
  return null;
}

function getFallbackImage(
  category: 'hotel' | 'restaurant' | 'attraction',
  name?: string,
): string {
  const images = fallbackImages[category];
  if (!name) {
    return images[0];
  }

  // Use name hash to consistently select the same image for the same place
  const hash = name.split('').reduce((acc, char) => {
    const newAcc = (acc << 5) - acc + char.charCodeAt(0);
    return newAcc & newAcc;
  }, 0);

  return images[Math.abs(hash) % images.length];
}

/**
 * Récupérer une image via les APIs web (Tavily/Serper) avec fallback SVG
 */
async function getWebImageWithFallback(
  searchQuery: string,
  category: 'hotel' | 'restaurant' | 'attraction',
  name?: string,
): Promise<string> {
  console.log(`[Web Image] Recherche d'image pour: "${searchQuery}"`);

  try {
    // Essayer d'abord avec l'API Serper
    const apiKey = process.env.SERPER_API as string;
    if (apiKey) {
      const url = `https://google.serper.dev/images?q=${encodeURIComponent(searchQuery)}`;

      const response = await fetch(url, {
        method: 'GET',
        headers: {
          'X-API-KEY': apiKey,
          'Content-Type': 'application/json',
        },
      });

      if (response.ok) {
        const data = await response.json();
        if (data.images && data.images.length > 0) {
          // Filtrer les images de bonne qualité
          const validImages = data.images.filter(
            (img: any) =>
              img.imageUrl &&
              !img.imageUrl.includes('data:image') &&
              (img.imageWidth || 0) >= 200 &&
              (img.imageHeight || 0) >= 150,
          );

          if (validImages.length > 0) {
            console.log(
              `[Web Image] ✅ Image trouvée via Serper: ${validImages[0].imageUrl}`,
            );
            return validImages[0].imageUrl;
          }
        }
      }
    }

    // Essayer avec Tavily si Serper échoue
    console.log(`[Web Image] Tentative avec Tavily pour: "${searchQuery}"`);
    const tavilyImages = await getLocationImagesWithTavily(searchQuery, '', '');
    if (tavilyImages && tavilyImages.length > 0) {
      console.log(
        `[Web Image] ✅ Image trouvée via Tavily: ${tavilyImages[0]}`,
      );
      return tavilyImages[0];
    }
  } catch (error) {
    console.warn(
      `[Web Image] Erreur lors de la récupération d'image pour "${searchQuery}":`,
      error,
    );
  }

  // Fallback to real images instead of SVG
  console.log(
    `[Web Image] ⚠️ Utilisation du fallback image pour: "${searchQuery}"`,
  );
  if (category === 'attraction' && name) {
    const image = await getDestinationImageAsync(name);
    if (image) {
      return image;
    }
  }
  return getFallbackImage(category, name);
}

/**
 * Rate-limited image API call wrapper
 */
async function rateLimitedImageSearch(
  searchFunction: () => Promise<string>,
): Promise<string> {
  return new Promise((resolve) => {
    imageApiRateLimit.requestQueue.push(async () => {
      try {
        const result = await searchFunction();
        resolve(result);
      } catch (error) {
        console.error('Rate-limited image search failed:', error);
        resolve(getFallbackImage('hotel'));
      }
    });

    processImageQueue();
  });
}

/**
 * Process image request queue with rate limiting
 */
async function processImageQueue() {
  if (
    imageApiRateLimit.isProcessing ||
    imageApiRateLimit.requestQueue.length === 0
  ) {
    return;
  }

  imageApiRateLimit.isProcessing = true;

  while (imageApiRateLimit.requestQueue.length > 0) {
    const now = Date.now();
    const timeSinceLastCall = now - imageApiRateLimit.lastCall;

    if (timeSinceLastCall < imageApiRateLimit.minInterval) {
      await new Promise((resolve) =>
        setTimeout(resolve, imageApiRateLimit.minInterval - timeSinceLastCall),
      );
    }

    const request = imageApiRateLimit.requestQueue.shift();
    if (request) {
      imageApiRateLimit.lastCall = Date.now();
      await request();
    }
  }

  imageApiRateLimit.isProcessing = false;
}

// getHotelImageUrl function removed - not needed for simple layouts

/**
 * Force carousel creation for missing sections
 */
async function forceCarouselCreation(
  htmlContent: string,
  tripPlan: ComprehensiveTripPlan,
): Promise<string> {
  let modifiedHtml = htmlContent;

  console.log('Checking for broken sections...');

  // Fix Accommodations section if it contains [object Promise] or is missing carousel
  if (
    modifiedHtml.includes('[object Promise]') ||
    !modifiedHtml.includes('hotel-carousel')
  ) {
    console.log(
      '🔧 Fixing accommodations section with professional carousel...',
    );

    // Extract accommodation data from trip plan if available
    const accommodationData =
      tripPlan.accommodationDining?.accommodations?.recommended || [];

    // Create enhanced accommodation items
    const accommodationItems =
      accommodationData.length > 0
        ? accommodationData.map((acc) => ({
            name: acc.name || 'Premium Hotel',
            description:
              acc.description || 'Excellent accommodation with great amenities',
            extra: acc.priceRange || '€100-200/night',
            rating: acc.rating || '⭐⭐⭐⭐ 4.5/5',
            amenities: acc.amenities || ['WiFi', 'Breakfast', 'Parking'],
            imageUrl: acc.imageUrl,
          }))
        : [
            {
              name: 'Luxury Hotel',
              description:
                'Premium accommodation with excellent amenities and stunning city views',
              extra: '€150-300/night',
              rating: '⭐⭐⭐⭐⭐ 4.8/5',
              amenities: ['Spa', 'Pool', 'Restaurant', 'Concierge'],
            },
            {
              name: 'Boutique Hotel',
              description:
                'Charming local hotel with unique character and personalized service',
              extra: '€80-150/night',
              rating: '⭐⭐⭐⭐ 4.5/5',
              amenities: ['WiFi', 'Breakfast', 'Bar', 'Garden'],
            },
            {
              name: 'Budget Hotel',
              description:
                'Comfortable and affordable accommodation with essential amenities',
              extra: '€40-80/night',
              rating: '⭐⭐⭐ 4.0/5',
              amenities: ['WiFi', 'Reception', 'Parking'],
            },
          ];

    const accommodationsCarousel = await createSimpleLayout(
      accommodationItems.map((item) => ({
        name: item.name,
        description: item.description,
        extra: item.extra || 'Contact for pricing',
        category: 'Premium Accommodation',
        rating: item.rating,
        type: 'hotel',
      })),
      'hotel',
      tripPlan.destination.destination,
    );

    // Replace [object Promise] with carousel
    modifiedHtml = modifiedHtml.replace(
      /\[object Promise\]/g,
      accommodationsCarousel,
    );

    // Also replace any empty accommodations sections
    modifiedHtml = modifiedHtml.replace(
      /(<h[1-6][^>]*>.*?Accommodations.*?<\/h[1-6]>)\s*(<h[1-6]|$)/gis,
      `$1\n${accommodationsCarousel}\n$2`,
    );
  }

  // Fix Local Recommendations section if needed
  if (!modifiedHtml.includes('poi-carousel')) {
    console.log('Adding local recommendations carousel...');

    const poiCarousel = await createSimpleLayout(
      [
        {
          name: 'Historic Center',
          description:
            'Explore the charming historic district with centuries-old architecture and cobblestone streets',
          extra: 'Historic Site',
          category: 'Historical',
          type: 'poi',
        },
        {
          name: 'Local Market',
          description:
            'Experience authentic local culture and cuisine at this bustling traditional market',
          extra: 'Market',
          category: 'Shopping',
          type: 'poi',
        },
        {
          name: 'Scenic Viewpoint',
          description:
            'Breathtaking panoramic views of the city and surrounding landscapes',
          extra: 'Viewpoint',
          category: 'Nature',
          type: 'poi',
        },
        {
          name: 'Artisan Quarter',
          description:
            'Discover local craftspeople and traditional artisans at work in their studios',
          extra: 'Cultural District',
          category: 'Arts & Culture',
          type: 'poi',
        },
        {
          name: 'Riverside Promenade',
          description:
            'Peaceful waterfront walkway perfect for evening strolls and sunset watching',
          extra: 'Waterfront',
          category: 'Nature',
          type: 'poi',
        },
        {
          name: 'Underground Caves',
          description:
            'Hidden underground network of natural caves with stunning rock formations',
          extra: 'Natural Wonder',
          category: 'Adventure',
          type: 'poi',
        },
        {
          name: 'Rooftop Gardens',
          description:
            'Secret rooftop garden oasis with exotic plants and city views',
          extra: 'Hidden Gem',
          category: 'Nature',
          type: 'poi',
        },
        {
          name: 'Street Art District',
          description:
            'Vibrant neighborhood showcasing the best local street art and murals',
          extra: 'Art District',
          category: 'Arts & Culture',
          type: 'poi',
        },
        {
          name: 'Traditional Tea House',
          description:
            'Authentic tea ceremony experience in a centuries-old traditional setting',
          extra: 'Cultural Experience',
          category: 'Cultural',
          type: 'poi',
        },
        {
          name: 'Observatory Deck',
          description:
            'Stargazing spot with telescopes and guided astronomy sessions',
          extra: 'Observatory',
          category: 'Science',
          type: 'poi',
        },
        {
          name: 'Floating Gardens',
          description:
            'Unique floating botanical gardens accessible by traditional boats',
          extra: 'Botanical Wonder',
          category: 'Nature',
          type: 'poi',
        },
        {
          name: 'Spice Bazaar',
          description:
            'Aromatic spice market with exotic flavors and traditional cooking demonstrations',
          extra: 'Culinary Experience',
          category: 'Food & Drink',
          type: 'poi',
        },
      ],
      'poi',
      tripPlan.destination.destination,
    );

    // Add POI carousel after Local Recommendations heading
    modifiedHtml = modifiedHtml.replace(
      /(<h[1-6][^>]*>.*?Local Recommendations.*?<\/h[1-6]>)/gis,
      `$1\n${poiCarousel}`,
    );
  }

  return modifiedHtml;
}

// createAccommodationCarousel function removed - using simple layouts

/**
 * Generate the HTML output from the comprehensive trip plan using AI model (backup method)
 */
async function generateAdvancedTripPlanHtmlWithAI(
  tripPlan: ComprehensiveTripPlan,
): Promise<AdvancedTripPlanningOutput> {
  try {
    console.log(
      'Generating HTML for advanced trip plan:',
      tripPlan.destination.destination,
    );

    // Generate the HTML content using the model
    const { object } = await generateObject({
      model: myProvider.languageModel('artifact-model'),
      system: `You are an expert web developer specializing in creating beautiful, interactive travel itineraries.
      Create a visually stunning HTML travel handbook for ${tripPlan.destination.destination} for ${tripPlan.destination.duration} days.
      Include all the provided information in a well-structured, interactive format with tabs for different sections.

      IMPORTANT: You MUST include Leaflet map library to display all points of interest. Add these lines in your HTML:
      <link rel="stylesheet" href="https://unpkg.com/leaflet@1.9.4/dist/leaflet.css" integrity="sha256-p4NxAoJBhIIN+hmNHrzRCf9tD/miZyoHS5obTRR9BMY=" crossorigin=""/>
      <script src="https://unpkg.com/leaflet@1.9.4/dist/leaflet.js" integrity="sha256-20nQCchB9co0qIjJZRGuk2/Z9VM+kNiyxNV1lvTlZBo=" crossorigin=""></script>

      HERO IMAGE: The trip plan includes a heroImage URL. You MUST use this image as the main hero image at the top of the page.
      If the heroImage fails to load, provide a fallback using SVG placeholder:
      <img src="\${tripPlan.heroImage}" alt="Destination" onerror="this.onerror=null; this.src='data:image/svg+xml,%3Csvg xmlns=\\'http://www.w3.org/2000/svg\\' width=\\'1600\\' height=\\'900\\' viewBox=\\'0 0 1600 900\\'%3E%3Crect width=\\'1600\\' height=\\'900\\' fill=\\'%23f8f9fa\\'/%3E%3Ctext x=\\'800\\' y=\\'450\\' font-family=\\'Arial\\' font-size=\\'48\\' fill=\\'%236c757d\\' text-anchor=\\'middle\\' dominant-baseline=\\'middle\\'%3E${tripPlan.destination.destination}%3C/text%3E%3C/svg%3E'" />

      VISUAL CUSTOMIZATION: The trip plan includes a visualTemplate object with detailed styling information. You MUST use these styles to customize the appearance of the page:
      - Use the colors specified in visualTemplate.colors for the color scheme
      - Use the fonts specified in visualTemplate.fonts for typography
      - Follow the layout instructions in visualTemplate.layout
      - Use the specified icon style in visualTemplate.icons
      - Apply the image styling from visualTemplate.images
      - Include animations as specified in visualTemplate.animations
      - Add special elements as specified in visualTemplate.specialElements
      - Include the CSS from visualTemplate.css

      TRIP TYPE ADAPTATION: The trip plan includes a tripClassification object that indicates the primary type of trip (${tripPlan.tripClassification?.primaryType || 'general'}).
      Adapt the content and presentation based on this classification:
      - For cycling trips: Emphasize cycling routes, bike rental information, and elevation profiles
      - For hiking trips: Highlight trail information, difficulty levels, and required gear
      - For beach trips: Focus on beach information, water activities, and beach facilities
      - For cultural trips: Emphasize museums, historical sites, and cultural experiences
      - For food trips: Highlight dining options, local cuisine, and food experiences

      SPECIALIZED ACTIVITY DATA: If the trip includes specializedActivityData, prominently feature this information in dedicated sections.

      Make sure the HTML is responsive and works well on all devices.

      This is an ADVANCED trip plan with comprehensive information. Include ALL of the following sections:
      - Hero image with destination name and duration
      - Interactive map with all points of interest
      - Day-by-day itinerary with detailed activities
      - Accommodation recommendations with details and map locations (MUST use carousel with ID "hotel-carousel")
      - Dining options categorized by meal type (MUST use carousel with ID "restaurant-carousel")
      - Local recommendations including hidden gems and local events (MUST use carousel with ID "poi-carousel")
      - Practical information including transportation, weather, emergency info
      - Travel tips and local phrases
      - Budget information

      ⚠️ CRITICAL CAROUSEL REQUIREMENTS - ABSOLUTELY MANDATORY - NO EXCEPTIONS ⚠️
      For Accommodations, Dining Options, and Local Recommendations sections, you MUST implement carousels with EXACTLY this structure.
      DO NOT USE SIMPLE TEXT LISTS OR PARAGRAPHS. ONLY USE CAROUSELS:

      RESTAURANTS SECTION:
      <div id="restaurant-carousel" class="restaurant-carousel">
        <div class="restaurant-slide">
          <div class="card">
            <img src="[restaurant-image]" alt="[restaurant-name]">
            <div class="card-content">
              <h3>[restaurant-name]</h3>
              <p>[restaurant-description]</p>
              <div class="rating">[rating]</div>
            </div>
          </div>
        </div>
        <!-- Repeat for each restaurant -->
      </div>

      HOTELS SECTION:
      <div id="hotel-carousel" class="hotel-carousel">
        <div class="hotel-slide">
          <div class="card">
            <img src="[hotel-image]" alt="[hotel-name]">
            <div class="card-content">
              <h3>[hotel-name]</h3>
              <p>[hotel-description]</p>
              <div class="price">[price]</div>
            </div>
          </div>
        </div>
        <!-- Repeat for each hotel -->
      </div>

      POI SECTION:
      <div id="poi-carousel" class="poi-carousel">
        <div class="poi-slide">
          <div class="card">
            <img src="[poi-image]" alt="[poi-name]">
            <div class="card-content">
              <h3>[poi-name]</h3>
              <p>[poi-description]</p>
              <div class="type">[poi-type]</div>
            </div>
          </div>
        </div>
        <!-- Repeat for each POI -->
      </div>

      ⚠️ ABSOLUTELY CRITICAL ⚠️: DO NOT use simple lists, paragraphs, or text for accommodations, restaurants, or POI.
      ONLY use the carousel structure above. NO EXCEPTIONS. NO SIMPLE LISTS ALLOWED.
      The JavaScript will automatically initialize these carousels

      Use tabs or accordion sections to organize the content in a user-friendly way.
      Make the design visually appealing with appropriate colors, fonts, and spacing.
      Include interactive elements like hover effects, tooltips, and collapsible sections.

      IMPORTANT: Your response MUST be a valid JSON object with exactly these three properties:
      - htmlContent: The HTML structure of the page (string)
      - cssContent: The CSS styling for the page (string)
      - jsContent: The JavaScript code for interactivity (string)

      SIDEBAR IMAGES: When a user clicks on a location name, a sidebar should open with images of that location.
      Make sure to properly format the location data as a valid JSON string in the data-location attribute.
      Include imageUrl or imageUrls in the location data to display images in the sidebar.
      Use proper error handling for image loading with fallbacks to SVG placeholders.`,
      prompt: `Create a comprehensive travel itinerary for ${tripPlan.destination.destination}, ${tripPlan.destination.country} with the following data:

${JSON.stringify(tripPlan, null, 2)}

IMPORTANT: Make sure to include a proper Leaflet map initialization in your JavaScript code. The map should display all points of interest with markers. Make sure to include the Leaflet CSS and JavaScript libraries in your HTML.

SPECIALIZED CONTENT: This trip has been classified as a "${tripPlan.tripClassification?.primaryType || 'general'}" trip.
${
  tripPlan.tripClassification?.primaryType === 'cycling'
    ? 'Include dedicated sections for cycling routes with elevation profiles, bike rental information, and cycling-specific tips. Use appropriate cycling icons for map markers.'
    : ''
}
${
  tripPlan.tripClassification?.primaryType === 'hiking'
    ? 'Include dedicated sections for hiking trails with difficulty information, elevation profiles, and required gear. Use appropriate hiking icons for map markers.'
    : ''
}
${
  tripPlan.tripClassification?.primaryType === 'beach'
    ? 'Include dedicated sections for beaches with facilities information, water activities, and beach-specific tips. Use appropriate beach icons for map markers.'
    : ''
}

VISUAL STYLING: Apply the visual styling specified in the visualTemplate object. Use the colors, fonts, layout, and other styling elements provided.

Your HTML should include:
- A hero image section at the top with a large, beautiful image of the destination (use the heroImage URL provided in the data)
- A header with the destination name and duration overlaid on the hero image
- An interactive map section with a div element that has an ID for the map
- A day-by-day itinerary section with detailed activities
- Accommodation recommendations with details and map locations (MUST use carousel with ID "hotel-carousel" and slides with class "hotel-slide")
- Dining options categorized by meal type (MUST use carousel with ID "restaurant-carousel" and slides with class "restaurant-slide")
- Local recommendations including hidden gems and local events (MUST use carousel with ID "poi-carousel" and slides with class "poi-slide")
- Practical information including transportation, weather, emergency info
- Travel tips and local phrases
- Budget information
${tripPlan.specializedActivityData ? '- Specialized sections for the primary activity type with detailed information' : ''}

⚠️ MANDATORY CAROUSEL HTML STRUCTURE - ABSOLUTELY CRITICAL - NO EXCEPTIONS ⚠️
You MUST use carousels for restaurants, hotels, and POI. NEVER USE SIMPLE LISTS, PARAGRAPHS, OR TEXT.
ONLY CAROUSELS ARE ALLOWED FOR THESE SECTIONS.

EXAMPLE RESTAURANT CAROUSEL (MANDATORY - ALWAYS USE THIS FORMAT):
<div id="restaurant-carousel" class="restaurant-carousel">
  <div class="carousel-container">
    <button class="carousel-btn prev-btn" onclick="scrollCarousel('restaurant-carousel', -1)">‹</button>
    <div class="carousel-track">
      <div class="restaurant-slide">
        <div class="card">
          <img src="[restaurant-image-url]" alt="Le Comptoir du Relais">
          <div class="card-content">
            <h3>Le Comptoir du Relais</h3>
            <p>Traditional French bistro in Saint-Germain</p>
            <div class="rating">⭐⭐⭐⭐⭐ 4.5/5</div>
          </div>
        </div>
      </div>
      <div class="restaurant-slide">
        <div class="card">
          <img src="[restaurant-image-url]" alt="L'Ami Jean">
          <div class="card-content">
            <h3>L'Ami Jean</h3>
            <p>Cozy bistro with excellent wine selection</p>
            <div class="rating">⭐⭐⭐⭐ 4.2/5</div>
          </div>
        </div>
      </div>
    </div>
    <button class="carousel-btn next-btn" onclick="scrollCarousel('restaurant-carousel', 1)">›</button>
  </div>
</div>

REQUIRED CSS FOR RESTAURANT CAROUSEL:
.restaurant-carousel {
  margin: 20px 0;
  position: relative;
}
.carousel-container {
  position: relative;
  display: flex;
  align-items: center;
}
.carousel-track {
  display: flex;
  overflow-x: auto;
  scroll-behavior: smooth;
  gap: 20px;
  padding: 10px 0;
  scrollbar-width: none;
  -ms-overflow-style: none;
}
.carousel-track::-webkit-scrollbar {
  display: none;
}
.restaurant-slide {
  flex: 0 0 300px;
  min-width: 300px;
}
.carousel-btn {
  position: absolute;
  top: 50%;
  transform: translateY(-50%);
  background: rgba(0,0,0,0.7);
  color: white;
  border: none;
  width: 40px;
  height: 40px;
  border-radius: 50%;
  font-size: 20px;
  cursor: pointer;
  z-index: 10;
}
.prev-btn { left: -20px; }
.next-btn { right: -20px; }
.card {
  background: white;
  border-radius: 12px;
  box-shadow: 0 4px 12px rgba(0,0,0,0.1);
  overflow: hidden;
  height: 350px;
  display: flex;
  flex-direction: column;
}
.card img {
  width: 100%;
  height: 200px;
  object-fit: cover;
}
.card-content {
  padding: 15px;
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}

REQUIRED JAVASCRIPT FOR CAROUSEL:
function scrollCarousel(carouselId, direction) {
  const carousel = document.querySelector('#' + carouselId + ' .carousel-track');
  const scrollAmount = 320;
  carousel.scrollBy({ left: direction * scrollAmount, behavior: 'smooth' });
}

EXAMPLE HOTEL CAROUSEL (MANDATORY):
<div id="hotel-carousel" class="hotel-carousel">
  <div class="hotel-slide">
    <div class="card">
      <img src="[hotel-image-url]" alt="Hotel des Grands Boulevards">
      <div class="card-content">
        <h3>Hotel des Grands Boulevards</h3>
        <p>Boutique hotel in the heart of Paris</p>
        <div class="price">€180/night</div>
      </div>
    </div>
  </div>
</div>

EXAMPLE POI CAROUSEL (MANDATORY):
<div id="poi-carousel" class="poi-carousel">
  <div class="poi-slide">
    <div class="card">
      <img src="[poi-image-url]" alt="Eiffel Tower">
      <div class="card-content">
        <h3>Eiffel Tower</h3>
        <p>Iconic iron lattice tower and symbol of Paris</p>
        <div class="type">Monument</div>
      </div>
    </div>
  </div>
</div>

⚠️ CRITICAL - FINAL WARNING ⚠️: Always use this carousel structure.
NEVER EVER use simple lists, paragraphs, or plain text for restaurants, hotels, or POI sections.
IF YOU USE SIMPLE LISTS INSTEAD OF CAROUSELS, THE OUTPUT WILL BE REJECTED.

LOCATION SIDEBAR: When a user clicks on a location name, a sidebar should open with images of that location.
- Make all location names clickable with an onclick attribute that calls openLocationDetail(this)
- Format the location data as a valid JSON string in the data-location attribute
- Include imageUrl or imageUrls in the location data to display images in the sidebar
- Make sure the JSON is properly escaped to avoid conflicts with HTML attributes
- Example: <span class="location-name" onclick="openLocationDetail(this)" data-location='{"name":"Eiffel Tower","type":"attraction","imageUrl":"https://example.com/image.jpg"}'>Eiffel Tower</span>

IMPORTANT: For each location mentioned in the itinerary:
1. Make the location name a clickable span with class="location-name"
2. Add onclick="openLocationDetail(this)" to the span
3. Add a data-location attribute with a JSON object containing:
   - name: The name of the location
   - type: The type of location (attraction, restaurant, hotel, etc.)
   - description: A description of the location
   - imageUrl: A direct URL to an image of the location (if available)
   - OR imageUrls: An array of image URLs for the location (if available)
4. If you don't have specific image URLs, the system will automatically fetch images from web APIs or use SVG placeholders based on the location name

The JavaScript should initialize the Leaflet map and add markers for all points of interest, with different marker styles based on the point type.

MANDATORY JAVASCRIPT FOR CAROUSELS:
The system will automatically inject the complete carousel JavaScript classes.
You do NOT need to include carousel JavaScript in your jsContent - it will be added automatically.
Focus on your map initialization and other interactive features.

Use tabs or accordion sections to organize the content in a user-friendly way.`,
      schema: z.object({
        htmlContent: z.string().describe('HTML structure of the page'),
        cssContent: z.string().describe('CSS styling for the page'),
        jsContent: z.string().describe('JavaScript code for interactivity'),
      }),
      temperature: 0.7, // Add some creativity but not too much
    });

    // Validate that we have all required properties
    const typedObject = object as AdvancedTripPlanningOutput;
    if (
      !typedObject.htmlContent ||
      !typedObject.cssContent ||
      !typedObject.jsContent
    ) {
      throw new Error('Generated HTML is missing required properties');
    }

    // Intelligently convert content to carousels using LLM
    console.log('Applying intelligent carousel conversion...');
    typedObject.htmlContent = await intelligentCarouselConversion(
      typedObject.htmlContent,
    );

    // Force carousel creation if sections are missing or contain [object Promise]
    typedObject.htmlContent = await forceCarouselCreation(
      typedObject.htmlContent,
      tripPlan,
    );

    // Include simple layout CSS and JavaScript
    typedObject.cssContent = `${typedObject.cssContent}\n\n${SIMPLE_LAYOUT_CSS}`;
    typedObject.jsContent = `${typedObject.jsContent}\n\n${SIMPLE_LAYOUT_JS}`;

    return typedObject;
  } catch (error) {
    console.error(
      'Error generating HTML from advanced trip plan with AI:',
      error,
    );

    // Fallback to the main web-based generation method
    console.log('Falling back to web-based HTML generation...');
    return await generateAdvancedTripPlanHtml(tripPlan);
  }
}

// enhanceAgentDataWithImages function removed - using simple layouts

/**
 * Main function that orchestrates the advanced trip planning workflow
 */
export async function generateAdvancedTripPlan(
  input: AdvancedTripPlanningInput,
): Promise<AdvancedTripPlanningOutput> {
  const { query, dataStream } = input;

  try {
    // Protection contre les exécutions multiples
    if (isWorkflowRunning) {
      console.log('Workflow already running, skipping duplicate execution');
      return await generateErrorHtml(
        query,
        new Error('Workflow already in progress'),
      );
    }

    isWorkflowRunning = true;
    console.log('Starting advanced trip planning workflow for query:', query);

    // Send initial update to the client
    sendProgressUpdate(dataStream, 'Analyzing your travel request...', 5, {
      query,
    });

    // 0. Validate the travel request first
    const validationAgent = new ValidationAgent(
      myProvider.languageModel('artifact-model'),
    );

    sendProgressUpdate(dataStream, 'Validating your travel request...', 8, {
      query,
    });

    const validationResult: ValidationResult =
      await validationAgent.validateTravelRequest(query);

    console.log(
      'Validation result:',
      JSON.stringify(validationResult, null, 2),
    );

    // If validation fails, ask for clarification
    if (!validationResult.shouldProceed || validationResult.confidence < 0.7) {
      console.log('Validation failed, generating clarifying questions...');

      const clarifyingQuestion =
        await validationAgent.generateClarifyingQuestions(
          query,
          validationResult.missingCritical,
        );

      // Return early with clarifying question
      throw new Error(`Validation failed: ${clarifyingQuestion}`);
    }

    const destinationAgent = new DestinationAgent(
      myProvider.languageModel('artifact-model'),
    );
    const destinationInfo =
      await destinationAgent.extractDestinationInfo(query);

    // Send update about destination research
    sendProgressUpdate(
      dataStream,
      `Researching ${destinationInfo.destination}, ${destinationInfo.country}...`,
      15,
      destinationInfo,
    );

    // 2. Classify the trip request to identify specific activities
    const classifierAgent = new ClassifierAgent(
      myProvider.languageModel('artifact-model'),
    );
    const tripClassification = await classifierAgent.classifyTripRequest(query);

    // Log the classification for debugging
    console.log(
      'Trip classification:',
      JSON.stringify(tripClassification, null, 2),
    );

    // Send update about trip classification
    sendProgressUpdate(
      dataStream,
      `Analyzing your travel preferences and activities...`,
      20,
      {
        destination: destinationInfo,
        tripType: tripClassification.primaryType,
        activities: tripClassification.keywords,
      },
    );

    // 3. Orchestrate the trip planning process
    const orchestratorAgent = new OrchestratorAgent(
      myProvider.languageModel('artifact-model'),
    );

    // Create a progress update function for the orchestrator
    const orchestratorProgressUpdate = (
      message: string,
      progress: number,
      data: any,
    ) => {
      // Scale the progress to be between 25 and 85
      const scaledProgress = 25 + (progress / 100) * 60;
      sendProgressUpdate(dataStream, message, scaledProgress, data);
    };

    // 🚨 CRITICAL: Only use intelligent default if user did NOT specify a duration
    console.log(
      `🔍 DURATION CHECK: destinationInfo.duration = ${destinationInfo.duration}`,
    );
    console.log(`🔍 DURATION TYPE: ${typeof destinationInfo.duration}`);

    if (
      destinationInfo.duration === null ||
      destinationInfo.duration === undefined
    ) {
      console.log(
        `⚠️ No duration specified for ${destinationInfo.destination}, using intelligent default`,
      );

      // Use AI to determine optimal duration based on destination characteristics
      const { object: durationInfo } = await generateObject({
        model: myProvider.languageModel('artifact-model'),
        system: `You are a travel expert with deep knowledge of destinations worldwide.
        
        Analyze the destination and determine the optimal trip duration based on:
        - Destination type (major city, small city, region, country)
        - Tourist attractions and activities available
        - Typical visitor patterns
        - Cultural and geographical characteristics
        
        GUIDELINES:
        - Major capitals (Paris, London, Rome, Tokyo): 3-5 days
        - Small capitals/cities (Skopje, Ljubljana, Tallinn): 2-3 days
        - Regions (Tuscany, Provence): 5-7 days
        - Island destinations: 4-6 days
        - Cultural/historical cities: 3-4 days
        - Nature/outdoor destinations: 4-7 days
        
        Provide a reasonable duration that allows visitors to see main attractions without rushing.`,
        prompt: `Determine optimal trip duration for: ${destinationInfo.destination}, ${destinationInfo.country}`,
        schema: z.object({
          duration: z
            .number()
            .min(1)
            .max(14)
            .describe('Optimal trip duration in days'),
          reasoning: z
            .string()
            .describe('Explanation for this duration choice'),
        }),
        temperature: 0.1,
      });

      // Update the destination info with the intelligent default
      destinationInfo.duration = durationInfo.duration;
      console.log(
        `✅ INTELLIGENT DURATION SET: ${durationInfo.duration} days for ${destinationInfo.destination}`,
      );
      console.log(`📝 REASONING: ${durationInfo.reasoning}`);
    } else {
      console.log(
        `✅ USER SPECIFIED DURATION: ${destinationInfo.duration} days - RESPECTING USER CHOICE`,
      );
    }

    // Log the extracted duration for verification
    console.log(
      `✅ DURATION CONFIRMED: ${destinationInfo.duration} days for ${destinationInfo.destination}`,
    );

    // Validate that the duration is reasonable (1-365 days)
    if (destinationInfo.duration < 1 || destinationInfo.duration > 365) {
      throw new Error(
        `Invalid duration: ${destinationInfo.duration} days. Please specify between 1 and 365 days.`,
      );
    }

    // Orchestrate the trip planning process with classification information
    const comprehensiveTripPlan =
      await orchestratorAgent.orchestrateTripPlanning(
        query,
        destinationInfo,
        orchestratorProgressUpdate,
        tripClassification, // Pass the classification to the orchestrator
      );

    // Get a hero image for the destination
    sendProgressUpdate(
      dataStream,
      'Finding beautiful images of your destination...',
      85,
      destinationInfo,
    );

    // Pre-load destination image in cache for faster template rendering
    await getDestinationImageAsync(
      destinationInfo.destination,
      destinationInfo.country,
    );

    const heroImage = await getDestinationHeroImage(
      destinationInfo.destination,
      destinationInfo.country,
    );

    // Add the hero image to the trip plan
    comprehensiveTripPlan.heroImage = heroImage || undefined;

    // Generate the final HTML output
    sendProgressUpdate(
      dataStream,
      'Creating your interactive travel guide...',
      90,
      {
        status: 'generating_html',
      },
    );

    const finalHtml = await generateAdvancedTripPlanHtml(comprehensiveTripPlan);

    // Send final progress update
    sendProgressUpdate(dataStream, 'Finalizing your travel handbook...', 95, {
      status: 'almost_complete',
    });

    // CRITICAL: Send the final HTML content to the dataStream
    console.log('Sending final HTML content to dataStream...');
    const finalContent = JSON.stringify({
      htmlContent: finalHtml.htmlContent,
      cssContent: finalHtml.cssContent,
      jsContent: finalHtml.jsContent,
    });

    dataStream.write({
      type: 'data-htmlDelta',
      data: finalContent,
      transient: false,
    });

    // Send completion update immediately (no setTimeout to avoid async issues)
    sendProgressUpdate(dataStream, 'Your travel adventure is ready!', 100, {
      status: 'complete',
      destination: destinationInfo.destination,
      country: destinationInfo.country,
    });

    // Send explicit completion signal to stop any further processing
    dataStream.write({ type: 'data-finish', data: null, transient: true });

    console.log('Advanced trip planning workflow completed successfully');

    // 🎯 Mark that an itinerary has been created for this destination
    ValidationAgent.markItineraryCreated(destinationInfo.destination);

    // Réinitialiser la protection contre les exécutions multiples
    isWorkflowRunning = false;

    return finalHtml;
  } catch (error) {
    console.error('Error in advanced trip planning workflow:', error);

    // Réinitialiser la protection contre les exécutions multiples en cas d'erreur
    isWorkflowRunning = false;

    // Return a fallback HTML in case of error
    return await generateErrorHtml(query, error);
  }
}

// Variables globales pour la protection contre les boucles infinies et les exécutions multiples
let lastProgressUpdateTime = 0;
let progressUpdateCallCount = 0;
let isWorkflowRunning = false;

/**
 * Helper function to send progress updates to the client
 */
function sendProgressUpdate(
  dataStream: any,
  message: string,
  progress: number,
  data: any,
) {
  if (!dataStream) return;

  // Protection contre les appels trop fréquents
  const now = Date.now();

  // Limiter les mises à jour à une par seconde maximum
  if (now - lastProgressUpdateTime < 1000) {
    console.log('Throttling progress update, too many calls');
    return;
  }

  // Mettre à jour le temps de la dernière mise à jour
  lastProgressUpdateTime = now;

  // Protection contre les boucles infinies
  // Limiter le nombre total d'appels
  progressUpdateCallCount++;
  if (progressUpdateCallCount > 100) {
    console.error(
      'Too many progress updates (>100), possible infinite loop detected',
    );
    return;
  }

  // Determine the current stage based on progress
  const getStageIcon = (currentProgress: number) => {
    if (currentProgress < 20) return '✈️'; // Planning stage
    if (currentProgress < 40) return '🗺️'; // Destination research
    if (currentProgress < 60) return '📍'; // Points of interest
    if (currentProgress < 80) return '📝'; // Itinerary creation
    return '📸'; // Finalizing
  };

  // Background SVG image for clarification page
  const backgroundImageUrl =
    "data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='1600' height='900' viewBox='0 0 1600 900'%3E%3Cdefs%3E%3ClinearGradient id='bg' x1='0%25' y1='0%25' x2='100%25' y2='100%25'%3E%3Cstop offset='0%25' style='stop-color:%234f46e5;stop-opacity:1' /%3E%3Cstop offset='100%25' style='stop-color:%237c3aed;stop-opacity:1' /%3E%3C/linearGradient%3E%3C/defs%3E%3Crect width='1600' height='900' fill='url(%23bg)'/%3E%3Ctext x='800' y='450' font-family='Arial' font-size='48' fill='white' text-anchor='middle' dominant-baseline='middle'%3ETravel Planning%3C/text%3E%3C/svg%3E";

  const htmlContent = `
    <div id="app" class="min-h-screen flex flex-col items-center justify-center relative overflow-hidden">
      <!-- Background image with parallax effect -->
      <div class="absolute inset-0 z-0">
        <img src="${backgroundImageUrl}" class="w-full h-full object-cover" style="filter: blur(8px); transform: scale(1.1);" />
        <div class="absolute inset-0 bg-black bg-opacity-50"></div>
      </div>

      <!-- Content container -->
      <div class="relative z-10 max-w-3xl w-full mx-auto p-8 rounded-xl bg-white bg-opacity-90 shadow-2xl">
        <div class="text-center">
          <div class="flex justify-center mb-6">
            <div class="text-5xl animate-bounce">${getStageIcon(progress)}</div>
          </div>

          <h1 class="text-3xl font-bold mb-4 text-gray-800">Creating Your Travel Adventure</h1>
          <p class="text-xl mb-8 text-gray-700">${message}</p>

          <!-- Animated progress bar -->
          <div class="relative pt-1 mb-6">
            <div class="flex mb-2 items-center justify-between">
              <div>
                <span class="text-xs font-semibold inline-block py-1 px-2 uppercase rounded-full text-blue-600 bg-blue-200">
                  Progress
                </span>
              </div>
              <div class="text-right">
                <span class="text-xs font-semibold inline-block text-blue-600">
                  ${progress}%
                </span>
              </div>
            </div>
            <div class="overflow-hidden h-2 mb-4 text-xs flex rounded bg-blue-200">
              <div style="width:${progress}%" class="shadow-none flex flex-col text-center whitespace-nowrap text-white justify-center bg-gradient-to-r from-blue-500 to-purple-600 transition-all duration-500 ease-in-out"></div>
            </div>
          </div>

          <!-- Travel preparation steps -->
          <div class="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
            <div class="p-4 rounded-lg ${progress >= 30 ? 'bg-green-100 text-green-800' : 'bg-gray-100 text-gray-500'}">
              <div class="text-2xl mb-2">${progress >= 30 ? '✓' : '🔍'}</div>
              <h3 class="font-bold">Destination Research</h3>
            </div>
            <div class="p-4 rounded-lg ${progress >= 60 ? 'bg-green-100 text-green-800' : 'bg-gray-100 text-gray-500'}">
              <div class="text-2xl mb-2">${progress >= 60 ? '✓' : '📋'}</div>
              <h3 class="font-bold">Itinerary Planning</h3>
            </div>
            <div class="p-4 rounded-lg ${progress >= 90 ? 'bg-green-100 text-green-800' : 'bg-gray-100 text-gray-500'}">
              <div class="text-2xl mb-2">${progress >= 90 ? '✓' : '🎨'}</div>
              <h3 class="font-bold">Final Touches</h3>
            </div>
          </div>

          <!-- Fun travel fact -->
          <div class="mt-6 p-4 bg-blue-50 rounded-lg text-left">
            <h4 class="font-bold text-blue-800">Did you know?</h4>
            <p class="text-blue-700" id="travel-fact">Loading interesting travel fact...</p>
          </div>
        </div>
      </div>
    </div>
  `;

  // Add CSS for animations and styling
  const cssContent = `
    @keyframes pulse {
      0%, 100% { opacity: 1; }
      50% { opacity: 0.5; }
    }

    @keyframes float {
      0%, 100% { transform: translateY(0); }
      50% { transform: translateY(-10px); }
    }

    body {
      font-family: system-ui, -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
      margin: 0;
      padding: 0;
      background-color: #f8fafc;
      color: #334155;
      min-height: 100vh;
    }

    .animate-bounce {
      animation: float 2s ease-in-out infinite;
    }

    .transition-all {
      transition-property: all;
      transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
      transition-duration: 300ms;
    }

    .duration-500 {
      transition-duration: 500ms;
    }

    .ease-in-out {
      transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
    }
  `;

  // Add JavaScript for dynamic content and animations
  const jsContent = `
    console.log("Progress update: ${progress}%");

    // Array of interesting travel facts
    const travelFacts = [
      "The world's largest hotel is in Saudi Arabia with 10,000 rooms.",
      "France is the most visited country in the world with over 89 million annual tourists.",
      "The shortest international flight lasts only 8 minutes between Switzerland and Germany.",
      "Japan has more than 6,800 islands.",
      "There are 195 countries in the world recognized by the United Nations.",
      "The Great Wall of China is not visible from space with the naked eye.",
      "Venice, Italy is built on 118 small islands connected by about 400 bridges.",
      "The world's oldest hotel has been operating since 705 AD in Japan.",
      "Singapore's Changi Airport has a butterfly garden with over 1,000 butterflies.",
      "The Maldives is the lowest country on Earth with an average ground level of 1.5 meters above sea level."
    ];

    // Display a random travel fact
    document.getElementById('travel-fact').textContent = travelFacts[Math.floor(Math.random() * travelFacts.length)];

    // Add subtle animation to the background
    const bgImage = document.querySelector('#app > div:first-child img');
    if (bgImage) {
      let scale = 1.1;
      let direction = 0.0001;

      setInterval(() => {
        scale += direction;
        if (scale > 1.15) direction = -0.0001;
        if (scale < 1.1) direction = 0.0001;
        bgImage.style.transform = \`scale(\${scale})\`;
      }, 50);
    }
  `;

  const content = JSON.stringify({
    htmlContent,
    cssContent,
    jsContent,
  });

  dataStream.write({ type: 'data-htmlDelta', data: content, transient: true });
}

/**
 * Generate horizontal itinerary layout with week grouping
 */
async function generateHorizontalItinerary(
  days: any[],
  destination: string,
  country: string,
): Promise<string> {
  // Group days by weeks if more than 7 days
  const groupDaysByWeeks = (days: any[]) => {
    if (days.length <= 7) {
      return [{ type: 'days', days }];
    }

    const weeks = [];
    let weekNumber = 1;

    for (let i = 0; i < days.length; i += 7) {
      const weekDays = days.slice(i, i + 7);
      if (weekDays.length === 7) {
        weeks.push({
          type: 'week',
          weekNumber,
          title:
            weekNumber === 1
              ? 'Première semaine'
              : weekNumber === 2
                ? 'Deuxième semaine'
                : weekNumber === 3
                  ? 'Troisième semaine'
                  : weekNumber === 4
                    ? 'Quatrième semaine'
                    : `${weekNumber}ème semaine`,
          days: weekDays,
        });
        weekNumber++;
      } else {
        // Remaining days
        weeks.push({
          type: 'remaining',
          title:
            weekDays.length === 1
              ? 'Jour supplémentaire'
              : 'Jours supplémentaires',
          days: weekDays,
        });
      }
    }

    return weeks;
  };

  const groupedData = groupDaysByWeeks(days);
  let html = '';

  for (const group of groupedData) {
    if (group.type === 'days') {
      // Défilement horizontal même pour <7 jours
      html += `
        <div class="overflow-x-auto pb-2">
          <div class="flex gap-4 min-w-max">
            ${await Promise.all(
              group.days.map(async (day: any) => {
                // Get specific image for this day based on its activities
                let dayImage = await getDaySpecificImage(
                  day.day,
                  day.activities,
                  destination,
                  country,
                );

                // Try to get more specific location images if available
                const firstActivity = day.activities[0];
                if (firstActivity && dayImage) {
                  try {
                    const images = await getLocationImagesWithTavily(
                      firstActivity.location,
                      destination,
                      country,
                    );
                    if (images.length > 0) {
                      // Use different image from the array based on day number to ensure variety
                      const imageIndex = (day.day - 1) % images.length;
                      dayImage = images[imageIndex];
                    }
                  } catch (error) {
                    console.log('Error getting location image for day:', error);
                    // Keep the day-specific image we already found
                  }
                }

                return `
                  <div class="group cursor-pointer hover:shadow-xl transition-all duration-300 border border-gray-300 shadow-md bg-white rounded-2xl overflow-hidden flex-shrink-0 w-64"
                       onclick="openDayModal(${day.day})">
                    <div class="relative h-32">
                      <div class="absolute inset-0 bg-gradient-to-t from-black/80 to-transparent z-10"></div>
                      <img src="${dayImage}" alt="Day ${day.day}" class="w-full h-full object-cover"
                           onerror="this.onerror=null; this.src='${getDestinationImageSync(destination, country)}'"/>
                      <div class="absolute top-2 left-2 z-20">
                        <div class="w-8 h-8 bg-blue-800 rounded-full flex items-center justify-center">
                          <span class="text-xs font-medium text-white">${day.day}</span>
                        </div>
                      </div>
                      <div class="absolute bottom-2 left-2 z-20">
                        <h3 class="text-sm font-light text-white">Day ${day.day}</h3>
                        <p class="text-xs text-gray-300 font-light">${day.activities.length} activités</p>
                      </div>
                    </div>
                  </div>
                `;
              }),
            ).then((results) => results.join(''))}
          </div>
        </div>
      `;
    } else {
      // Semaine + jours restants
      const weekId =
        group.type === 'week' && 'weekNumber' in group
          ? `week-${group.weekNumber}`
          : 'remaining';

      html += `
        <div class="relative">
          <div class="flex items-center justify-between gap-4 mb-6 p-5 bg-purple-50 rounded-2xl border border-purple-200 cursor-pointer hover:bg-purple-100 transition-all duration-300"
               onclick="toggleWeek('${weekId}')">
            <div class="flex items-center gap-4">
              <div class="flex-shrink-0">
                <div class="w-14 h-14 bg-purple-600 rounded-2xl flex items-center justify-center shadow-lg">
                  <svg class="w-6 h-6 text-white" fill="currentColor" viewBox="0 0 20 20">
                    <path fill-rule="evenodd" d="M6 2a1 1 0 00-1 1v1H4a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V6a2 2 0 00-2-2h-1V3a1 1 0 10-2 0v1H7V3a1 1 0 00-1-1zm0 5a1 1 0 000 2h8a1 1 0 100-2H6z" clip-rule="evenodd"/>
                  </svg>
                </div>
              </div>
              <div>
                <h2 class="text-xl font-light text-gray-800 mb-1">${'title' in group ? group.title : 'Semaine'}</h2>
                <p class="text-purple-600 text-sm font-light">
                  ${group.days.length} jour${group.days.length > 1 ? 's' : ''} • Jours ${group.days[0].day} à ${group.days[group.days.length - 1].day}
                </p>
              </div>
            </div>
            <div class="flex items-center gap-2">
              <div class="text-sm text-purple-600 font-light week-toggle-text" data-week="${weekId}">Développer</div>
              <svg class="w-6 h-6 text-purple-600 week-toggle-icon" data-week="${weekId}" fill="currentColor" viewBox="0 0 20 20">
                <path fill-rule="evenodd" d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z" clip-rule="evenodd"/>
              </svg>
            </div>
          </div>
          <div class="week-content overflow-hidden transition-all duration-500 ease-in-out max-h-0 opacity-0" data-week="${weekId}">
            <div class="ml-8 pb-6">
              <div class="overflow-x-auto pb-2">
                <div class="flex gap-4 min-w-max">
                  ${await Promise.all(
                    group.days.map(async (day: any) => {
                      const firstActivity = day.activities[0];
                      let dayImage = getFallbackImage(
                        'attraction',
                        destination,
                      );

                      if (firstActivity) {
                        try {
                          const images = await getLocationImagesWithTavily(
                            firstActivity.location,
                            destination,
                            country,
                          );
                          if (images.length > 0) {
                            dayImage = images[0];
                          }
                        } catch (error) {
                          console.log('Error getting image for day:', error);
                        }
                      }

                      return `
                        <div class="group cursor-pointer hover:shadow-xl transition-all duration-300 border border-gray-300 shadow-md bg-white rounded-2xl overflow-hidden flex-shrink-0 w-64"
                             onclick="openDayModal(${day.day})">
                          <div class="relative h-32">
                            <div class="absolute inset-0 bg-gradient-to-t from-black/80 to-transparent z-10"></div>
                            <img src="${dayImage}" alt="Day ${day.day}" class="w-full h-full object-cover"
                                 onerror="this.onerror=null; this.src='${getDestinationImageSync(destination, country)}'"/>
                            <div class="absolute top-2 left-2 z-20">
                              <div class="w-8 h-8 bg-blue-800 rounded-full flex items-center justify-center">
                                <span class="text-xs font-medium text-white">${day.day}</span>
                              </div>
                            </div>
                            <div class="absolute bottom-2 left-2 z-20">
                              <h3 class="text-sm font-light text-white">Day ${day.day}</h3>
                              <p class="text-xs text-gray-300 font-light">${day.activities.length} activités</p>
                            </div>
                          </div>
                        </div>
                      `;
                    }),
                  ).then((results) => results.join(''))}
                </div>
              </div>
            </div>
          </div>
        </div>
      `;
    }
  }

  return html;
}

/**
 * Generate the HTML output from the comprehensive trip plan with real web-retrieved data (main method)
 */
async function generateAdvancedTripPlanHtml(
  tripPlan: ComprehensiveTripPlan,
): Promise<AdvancedTripPlanningOutput> {
  try {
    console.log(
      'Generating HTML with real web-retrieved data for:',
      tripPlan.destination.destination,
    );

    const { destination, country, duration } = tripPlan.destination;

    // Enrich activities with Serper data before generating HTML
    console.log('🔄 Enriching trip plan activities with Serper data...');
    const enrichedTripPlan = await enrichTripPlanActivities(
      tripPlan,
      destination,
    );
    console.log('✅ Trip plan activities enriched successfully');

    // Create a simple but functional travel itinerary
    const htmlContent = `
    <!DOCTYPE html>
    <html lang="en">
    <head>
      <meta charset="UTF-8">
      <meta name="viewport" content="width=device-width, initial-scale=1.0">
      <title>Trip to ${destination}, ${country}</title>
      <!-- Leaflet CSS -->
      <link rel="stylesheet" href="https://unpkg.com/leaflet@1.9.4/dist/leaflet.css" integrity="sha256-p4NxAoJBhIIN+hmNHrzRCf9tD/miZyoHS5obTRR9BMY=" crossorigin=""/>
      <!-- Leaflet JavaScript -->
      <script src="https://unpkg.com/leaflet@1.9.4/dist/leaflet.js" integrity="sha256-20nQCchB9co0qIjJZRGuk2/Z9VM+kNiyxNV1lvTlZBo=" crossorigin=""></script>
    </head>
    <body>
    <script>
      // Function to open location detail panel - defined globally so it's available immediately
      function openLocationDetail(element) {
        console.log('openLocationDetail called with element:', element);

        try {
          // Show the sheet and overlay immediately
          const locationSheet = document.getElementById('location-sheet');
          const locationSheetOverlay = document.getElementById('location-sheet-overlay');

          if (!locationSheet || !locationSheetOverlay) {
            console.error('Location sheet elements not found');
            return;
          }

          // Show the sheet and overlay immediately
          locationSheet.classList.add('open');
          locationSheetOverlay.classList.add('open');

          // Get location data from the element
          let locationData;
          try {
            locationData = JSON.parse(element.getAttribute('data-location'));
            console.log('Location data:', locationData);
          } catch (e) {
            console.error('Error parsing location data:', e);
            locationData = {
              name: element.textContent || 'Unknown location',
              type: 'attraction'
            };
          }

          // Get DOM elements
          const locationMainImage = document.getElementById('location-main-image');
          const locationThumbnails = document.getElementById('location-thumbnails');
          const locationDescription = document.getElementById('location-description');
          const locationInfo = document.getElementById('location-info');
          const locationTitle = document.querySelector('.location-sheet-title');

          // Update the title
          if (locationTitle) {
            locationTitle.textContent = locationData.name || 'Location Details';
          }

          // Update the description
          if (locationDescription) {
            locationDescription.textContent = locationData.description || 'No description available.';
          }

          // Update the info
          if (locationInfo) {
            let infoHTML = '';
            if (locationData.type) {
              infoHTML += '<div><strong>Type:</strong> ' + (locationData.type.charAt(0).toUpperCase() + locationData.type.slice(1)) + '</div>';
            }
            if (locationData.day) {
              infoHTML += '<div><strong>Day:</strong> ' + locationData.day + '</div>';
            }
            if (locationData.time) {
              infoHTML += '<div><strong>Time:</strong> ' + locationData.time + '</div>';
            }
            if (locationData.address) {
              infoHTML += '<div><strong>Address:</strong> ' + locationData.address + '</div>';
            }
            locationInfo.innerHTML = infoHTML || 'No additional information available.';
          }

          // Set main image with fallbacks
          if (locationMainImage) {
            // Create a function to handle image loading with fallbacks
            const setupImageWithFallbacks = (initialSrc) => {
              // Reset opacity for transition effect
              locationMainImage.style.opacity = '0';

              // Set a timeout to show the image even if events don't fire
              const showImageTimeout = setTimeout(() => {
                locationMainImage.style.opacity = '1';
              }, 1000);

              // Setup onload event
              locationMainImage.onload = function() {
                clearTimeout(showImageTimeout);
                console.log('Image loaded successfully:', locationMainImage.src);
                locationMainImage.style.opacity = '1';

                // Hide loading indicator
                const container = locationMainImage.parentElement;
                if (container) {
                  container.classList.add('image-loaded');
                }
              };

              // Setup onerror event with multiple fallback levels
              locationMainImage.onerror = function() {
                clearTimeout(showImageTimeout);
                console.error('Failed to load image:', locationMainImage.src);

                // Use SVG placeholder directly
                locationMainImage.src = "data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='800' height='600' viewBox='0 0 800 600'%3E%3Crect width='800' height='600' fill='%23f0f0f0'/%3E%3Ctext x='50%25' y='50%25' font-family='Arial' font-size='24' fill='%23666' text-anchor='middle' dominant-baseline='middle'%3E" + encodeURIComponent(locationData.name || 'Location') + "%3C/text%3E%3C/svg%3E";
                locationMainImage.onerror = null; // Disable future attempts
                locationMainImage.style.opacity = '1';
              };

              // Set initial source
              locationMainImage.src = initialSrc;
            };

            // Determine initial image source
            let initialSrc;

            // First try to use provided imageUrl
            if (locationData.imageUrl) {
              initialSrc = locationData.imageUrl;
            }
            // Then try first image from imageUrls array if available
            else if (locationData.imageUrls && Array.isArray(locationData.imageUrls) && locationData.imageUrls.length > 0) {
              initialSrc = locationData.imageUrls[0];
            }
            // Finally, use SVG placeholder
            else {
              initialSrc = "data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='800' height='600' viewBox='0 0 800 600'%3E%3Crect width='800' height='600' fill='%23f0f0f0'/%3E%3Ctext x='50%25' y='50%25' font-family='Arial' font-size='24' fill='%23666' text-anchor='middle' dominant-baseline='middle'%3E" + encodeURIComponent(locationData.name || 'Location') + "%3C/text%3E%3C/svg%3E";
            }

            // Setup image with initial source and fallbacks
            setupImageWithFallbacks(initialSrc);
          }

          // Set thumbnails with fallbacks
          if (locationThumbnails) {
            // Clear existing thumbnails
            locationThumbnails.innerHTML = '';

            // Create thumbnails
            const thumbnailCount = 3;
            const thumbnailTerms = [
              locationData.name + ' ' + (locationData.type || 'attraction'),
              locationData.name + ' landmark',
              locationData.name + ' tourism'
            ];

            // Use provided imageUrls if available
            const imageUrls = locationData.imageUrls || [];
            const totalImages = Math.max(imageUrls.length, thumbnailCount);

            for (let i = 0; i < totalImages; i++) {
              const thumbnail = document.createElement('div');
              thumbnail.className = 'location-sheet-thumbnail' + (i === 0 ? ' active' : '');

              const img = document.createElement('img');

              // Function to setup image with fallbacks
              const setupThumbnailWithFallbacks = (initialSrc) => {
                // Setup onload event
                img.onload = function() {
                  console.log('Thumbnail loaded successfully:', img.src);
                  // Add class to parent (thumbnail) to indicate image is loaded
                  thumbnail.classList.add('thumb-loaded');
                };

                // Setup onerror event with multiple fallback levels
                img.onerror = function() {
                  console.error('Failed to load thumbnail:', img.src);

                  // Use SVG placeholder directly
                  img.src = "data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='60' height='60' viewBox='0 0 60 60'%3E%3Crect width='60' height='60' fill='%23f0f0f0'/%3E%3Ctext x='50%25' y='50%25' font-family='Arial' font-size='10' fill='%23666' text-anchor='middle' dominant-baseline='middle'%3ENo image%3C/text%3E%3C/svg%3E";
                  img.onerror = null; // Disable future attempts
                };

                // Set initial source
                img.src = initialSrc;
              };

              // Determine initial image source
              let initialSrc;

              // Use imageUrls if available
              if (i < imageUrls.length) {
                initialSrc = imageUrls[i];
              } else {
                // Otherwise use SVG placeholder
                initialSrc = "data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='100' height='100' viewBox='0 0 100 100'%3E%3Crect width='100' height='100' fill='%23f0f0f0'/%3E%3Ctext x='50%25' y='50%25' font-family='Arial' font-size='12' fill='%23666' text-anchor='middle' dominant-baseline='middle'%3EImage%3C/text%3E%3C/svg%3E";
              }

              img.alt = locationData.name + ' image ' + (i+1);

              // Setup image with initial source and fallbacks
              setupThumbnailWithFallbacks(initialSrc);

              // Add click event to change main image
              thumbnail.addEventListener('click', function() {
                // Update active state
                document.querySelectorAll('.location-sheet-thumbnail').forEach(thumb => {
                  thumb.classList.remove('active');
                });
                thumbnail.classList.add('active');

                // Update main image with same fallback approach
                if (locationMainImage) {
                  // Reset opacity for transition effect
                  locationMainImage.style.opacity = '0';

                  // Set a timeout to show the image even if events don't fire
                  const showImageTimeout = setTimeout(() => {
                    locationMainImage.style.opacity = '1';
                  }, 1000);

                  // Setup onload event
                  locationMainImage.onload = function() {
                    clearTimeout(showImageTimeout);
                    console.log('Main image loaded successfully from thumbnail:', locationMainImage.src);
                    locationMainImage.style.opacity = '1';
                  };

                  // Setup onerror event
                  locationMainImage.onerror = function() {
                    clearTimeout(showImageTimeout);
                    console.error('Failed to load main image from thumbnail:', locationMainImage.src);

                    // Use SVG placeholder directly
                    locationMainImage.src = "data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='800' height='600' viewBox='0 0 800 600'%3E%3Crect width='800' height='600' fill='%23f0f0f0'/%3E%3Ctext x='50%25' y='50%25' font-family='Arial' font-size='24' fill='%23666' text-anchor='middle' dominant-baseline='middle'%3E" + encodeURIComponent(locationData.name || 'Location') + "%3C/text%3E%3C/svg%3E";
                    locationMainImage.onerror = null; // Disable future attempts
                    locationMainImage.style.opacity = '1';
                  };

                  // Set source
                  locationMainImage.src = img.src;
                }
              });

              thumbnail.appendChild(img);
              locationThumbnails.appendChild(thumbnail);
            }
          }
        } catch (error) {
          console.error('Error in openLocationDetail:', error);
        }
      }

      // Function to close the location sheet
      function closeLocationSheet() {
        const locationSheet = document.getElementById('location-sheet');
        const locationSheetOverlay = document.getElementById('location-sheet-overlay');

        if (locationSheet && locationSheetOverlay) {
          locationSheet.classList.remove('open');
          locationSheetOverlay.classList.remove('open');
        }
      }

      // Itinerary functions
      let expandedWeeks = new Set(['week-1']); // First week open by default
      let tripDays = ${JSON.stringify(enrichedTripPlan.days)};

      // Function to toggle week expansion
      function toggleWeek(weekId) {
        const weekContent = document.querySelector('.week-content[data-week="' + weekId + '"]');
        const toggleText = document.querySelector('.week-toggle-text[data-week="' + weekId + '"]');
        const toggleIcon = document.querySelector('.week-toggle-icon[data-week="' + weekId + '"]');

        if (!weekContent) return;

        const isExpanded = expandedWeeks.has(weekId);

        if (isExpanded) {
          // Collapse
          expandedWeeks.delete(weekId);
          weekContent.style.maxHeight = '0';
          weekContent.style.opacity = '0';
          if (toggleText) toggleText.textContent = 'Développer';
          if (toggleIcon) {
            toggleIcon.innerHTML = '<path fill-rule="evenodd" d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z" clip-rule="evenodd"/>';
          }
        } else {
          // Expand
          expandedWeeks.add(weekId);
          weekContent.style.maxHeight = '500px';
          weekContent.style.opacity = '1';
          if (toggleText) toggleText.textContent = 'Réduire';
          if (toggleIcon) {
            toggleIcon.innerHTML = '<path fill-rule="evenodd" d="M14.707 12.707a1 1 0 01-1.414 0L10 9.414l-3.293 3.293a1 1 0 01-1.414-1.414l4-4a1 1 0 011.414 0l4 4a1 1 0 010 1.414z" clip-rule="evenodd"/>';
          }
        }
      }

      // Function to expand all weeks
      function expandAllWeeks() {
        const weekContents = document.querySelectorAll('.week-content');
        const toggleTexts = document.querySelectorAll('.week-toggle-text');
        const toggleIcons = document.querySelectorAll('.week-toggle-icon');

        weekContents.forEach((content, index) => {
          const weekId = content.getAttribute('data-week');
          if (weekId) {
            expandedWeeks.add(weekId);
            content.style.maxHeight = '500px';
            content.style.opacity = '1';
          }
        });

        toggleTexts.forEach(text => {
          text.textContent = 'Réduire';
        });

        toggleIcons.forEach(icon => {
          icon.innerHTML = '<path fill-rule="evenodd" d="M14.707 12.707a1 1 0 01-1.414 0L10 9.414l-3.293 3.293a1 1 0 01-1.414-1.414l4-4a1 1 0 011.414 0l4 4a1 1 0 010 1.414z" clip-rule="evenodd"/>';
        });
      }

      // Function to collapse all weeks
      function collapseAllWeeks() {
        const weekContents = document.querySelectorAll('.week-content');
        const toggleTexts = document.querySelectorAll('.week-toggle-text');
        const toggleIcons = document.querySelectorAll('.week-toggle-icon');

        expandedWeeks.clear();

        weekContents.forEach(content => {
          content.style.maxHeight = '0';
          content.style.opacity = '0';
        });

        toggleTexts.forEach(text => {
          text.textContent = 'Développer';
        });

        toggleIcons.forEach(icon => {
          icon.innerHTML = '<path fill-rule="evenodd" d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z" clip-rule="evenodd"/>';
        });
      }

      // Function to open day modal
      function openDayModal(dayNumber) {
        const modal = document.getElementById('day-modal');
        const modalDayNumber = document.getElementById('modal-day-number');
        const modalDayTitle = document.getElementById('modal-day-title');
        const modalActivitiesCount = document.getElementById('modal-activities-count');
        const modalContent = document.getElementById('modal-content');

        if (!modal || !tripDays) return;

        const dayData = tripDays.find(day => day.day === dayNumber);
        if (!dayData) return;

        // Debug: Log the day data to see what we're working with
        console.log('🔍 Day Data for Day', dayNumber, ':', dayData);
        console.log('🔍 Activities:', dayData.activities);

        // Update modal header
        if (modalDayNumber) {
          modalDayNumber.querySelector('span').textContent = dayNumber;
        }
        if (modalDayTitle) {
          modalDayTitle.textContent = 'Day ' + dayNumber;
        }
        // Use only the original activities from the day
        const allActivities = dayData.activities;

        if (modalActivitiesCount) {
          modalActivitiesCount.textContent = allActivities.length;
        }

        // Generate activities HTML with expanded content
        if (modalContent) {
          let activitiesHtml = '';

          allActivities.forEach((activity, index) => {
            // Determine activity type and icon
            const activityType = activity.activity.toLowerCase().includes('temple') || activity.activity.toLowerCase().includes('shrine') || activity.activity.toLowerCase().includes('cérémonie') || activity.activity.toLowerCase().includes('calligraphie') || activity.activity.toLowerCase().includes('kabuki') ? 'Culture' :
                               activity.activity.toLowerCase().includes('lunch') || activity.activity.toLowerCase().includes('dinner') || activity.activity.toLowerCase().includes('restaurant') || activity.activity.toLowerCase().includes('marché') || activity.activity.toLowerCase().includes('sushi') ? 'Gastronomie' :
                               activity.activity.toLowerCase().includes('walk') || activity.activity.toLowerCase().includes('explore') || activity.activity.toLowerCase().includes('exploration') ? 'Exploration' : 'Culture';


                               
            const typeColor = activityType === 'Culture' ? '#F59E0B' :
                            activityType === 'Gastronomie' ? '#EF4444' : '#10B981';

            const typeIcon = activityType === 'Culture' ? '⛩️' :
                           activityType === 'Gastronomie' ? '🍽️' : '🚶';

            // Calculate duration (default 2 hours)
            const duration = '2 heures';

            activitiesHtml += \`
              <div class="bg-gray-800 rounded-xl p-6 mb-6 border border-gray-700">
                <div class="flex gap-4">
                  <!-- Image Section -->
                  <div class="relative w-40 h-32 flex-shrink-0 bg-gray-700 rounded-lg overflow-hidden">
                    <img src="\${activity.enrichedData.imageUrl}"
                         alt="\${activity.activity}"
                         class="w-full h-full object-cover"
                         onerror="this.style.display='none'; this.nextElementSibling.style.display='flex'"/>
                    <div class="absolute inset-0 bg-gray-600 flex items-center justify-center text-gray-400 text-xs" style="display: none;">
                      📍
                    </div>
                    <!-- Rating Badge -->
                    <div class="absolute bottom-2 left-2 bg-black bg-opacity-80 text-white text-xs px-2 py-1 rounded flex items-center gap-1">
                      <span class="text-yellow-400">⭐</span>
                      <span>\${activity.enrichedData.rating || '4.7/5'}</span>
                      <span class="text-gray-300">(avis)</span>
                    </div>
                  </div>

                  <!-- Content Section -->
                  <div class="flex-1 min-w-0">
                    <!-- Header with time and type -->
                    <div class="flex items-start justify-between mb-3">
                      <div class="flex items-center gap-2">
                        <span class="inline-flex items-center text-xs font-light rounded-full px-2 py-0.5 text-white" style="background-color: \${typeColor};">
                          \${typeIcon} \${activityType}
                        </span>
                      </div>
                      <div class="text-right text-sm">
                        <div class="text-blue-400 font-semibold text-base">\${activity.time}</div>
                        <div class="text-gray-400 text-sm flex items-center gap-1 mt-1">
                          <span>⏱️</span>
                          <span>\${duration}</span>
                        </div>
                      </div>
                    </div>

                    <!-- Title -->
                    <h3 class="text-white font-semibold text-lg mb-3 leading-tight">
                      \${activity.enrichedData?.name || activity.activity}
                    </h3>

                    <!-- Description -->
                    <p class="text-gray-300 text-sm mb-4 leading-relaxed">
                      \${activity.enrichedData?.description || activity.description}
                    </p>

                    <!-- Informations détaillées -->
                    <div class="bg-gray-900 rounded-lg p-4 mb-4">
                      <h4 class="text-white font-medium text-sm mb-3">Informations pratiques</h4>
                      <div class="space-y-2">
                        <!-- Location -->
                        <div class="flex items-start gap-3 text-gray-400 text-sm">
                          <span class="text-blue-400 mt-0.5">📍</span>
                          <span>\${activity.enrichedData?.address || activity.location}</span>
                        </div>
                        \${activity.enrichedData.phoneNumber ? \`
                        <!-- Phone -->
                        <div class="flex items-center gap-3 text-gray-400 text-sm">
                          <span class="text-green-400">📞</span>
                          <span>\${activity.enrichedData.phoneNumber}</span>
                        </div>
                        \` : ''}
                        \${activity.enrichedData.openingHours ? \`
                        <!-- Hours -->
                        <div class="flex items-center gap-3 text-gray-400 text-sm">
                          <span class="text-yellow-400">🕒</span>
                          <span>\${activity.enrichedData.openingHours}</span>
                        </div>
                        \` : ''}
                        \${activity.enrichedData.priceRange ? \`
                        <!-- Price Range -->
                        <div class="flex items-center gap-3 text-gray-400 text-sm">
                          <span class="text-green-400">💰</span>
                          <span>\${activity.enrichedData.priceRange}</span>
                        </div>
                        \` : ''}
                        \${activity.enrichedData.website ? \`
                        <!-- Website -->
                        <div class="flex items-center gap-3 text-gray-400 text-sm">
                          <span class="text-purple-400">🌐</span>
                          <a href="\${activity.enrichedData.website}" target="_blank" class="text-blue-400 hover:text-blue-300 underline">Site web</a>
                        </div>
                        \` : ''}
                      </div>
                    </div>





                    <!-- Action Buttons -->
                    <div class="flex gap-2">
                      <button class="flex-1 bg-blue-600 hover:bg-blue-700 text-white text-sm font-medium py-3 rounded-lg transition-colors duration-200 flex items-center justify-center gap-2">
                        <span>🗺️</span>
                        Voir sur la carte
                      </button>
                      <button class="flex-1 bg-gray-600 hover:bg-gray-700 text-white text-sm font-medium py-3 rounded-lg transition-colors duration-200 flex items-center justify-center gap-2">
                        <span>ℹ️</span>
                        Plus d'infos
                      </button>
                    </div>
                  </div>
                </div>
              </div>
            \`;
          });

          // Add minimal extra content to ensure scrollbar appears
          const extraContent = '<div style="height: 200px; opacity: 0.05; pointer-events: none;"><div class="text-center text-gray-500 text-xs mt-20">Fin du programme de la journée</div></div>';

          modalContent.innerHTML = activitiesHtml + extraContent;
        }

        // Show modal
        modal.style.display = 'flex';
      }

      // Function to close day modal
      function closeDayModal() {
        const modal = document.getElementById('day-modal');
        if (modal) {
          modal.style.display = 'none';
        }
      }

      // Initialize on page load
      document.addEventListener('DOMContentLoaded', function() {
        // Show week controls if there are weeks
        const weekContents = document.querySelectorAll('.week-content');
        const weekControls = document.getElementById('week-controls');

        if (weekContents.length > 0 && weekControls) {
          weekControls.style.display = 'flex';

          // Expand first week by default
          const firstWeekContent = document.querySelector('.week-content[data-week="week-1"]');
          if (firstWeekContent) {
            firstWeekContent.style.maxHeight = '500px';
            firstWeekContent.style.opacity = '1';

            const firstToggleText = document.querySelector('.week-toggle-text[data-week="week-1"]');
            const firstToggleIcon = document.querySelector('.week-toggle-icon[data-week="week-1"]');

            if (firstToggleText) firstToggleText.textContent = 'Réduire';
            if (firstToggleIcon) {
              firstToggleIcon.innerHTML = '<path fill-rule="evenodd" d="M14.707 12.707a1 1 0 01-1.414 0L10 9.414l-3.293 3.293a1 1 0 01-1.414-1.414l4-4a1 1 0 011.414 0l4 4a1 1 0 010 1.414z" clip-rule="evenodd"/>';
            }
          }
        }
      });
    </script>
    <div id="app">
      <!-- Location Sheet Panel -->
      <div id="location-sheet" class="location-sheet">
        <div class="location-sheet-header">
          <h2 class="location-sheet-title">Location Details</h2>
          <button class="location-sheet-close" aria-label="Close panel" onclick="closeLocationSheet()">&times;</button>
        </div>
        <div class="location-sheet-content">
          <div class="location-sheet-images">
            <div class="location-sheet-image-container">
              <img src="data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='200' height='200' viewBox='0 0 200 200'%3E%3Crect width='200' height='200' fill='%23f0f0f0'/%3E%3Ctext x='50%25' y='50%25' font-family='Arial' font-size='14' fill='%23666' text-anchor='middle' dominant-baseline='middle'%3ELoading...%3C/text%3E%3C/svg%3E"
                   alt="Loading location image"
                   class="location-sheet-image"
                   id="location-main-image">
            </div>
            <div class="location-sheet-thumbnails" id="location-thumbnails">
              <div class="location-sheet-thumbnail active">
                <img src="data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='60' height='60' viewBox='0 0 60 60'%3E%3Crect width='60' height='60' fill='%23f0f0f0'/%3E%3C/svg%3E" alt="Loading thumbnail">
              </div>
              <div class="location-sheet-thumbnail">
                <img src="data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='60' height='60' viewBox='0 0 60 60'%3E%3Crect width='60' height='60' fill='%23f0f0f0'/%3E%3C/svg%3E" alt="Loading thumbnail">
              </div>
              <div class="location-sheet-thumbnail">
                <img src="data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='60' height='60' viewBox='0 0 60 60'%3E%3Crect width='60' height='60' fill='%23f0f0f0'/%3E%3C/svg%3E" alt="Loading thumbnail">
              </div>
            </div>
          </div>
          <div class="location-sheet-details">
            <div class="location-sheet-description" id="location-description">
              Loading description...
            </div>
            <div class="location-sheet-info" id="location-info">
              Loading information...
            </div>
          </div>
        </div>
      </div>

      <!-- Overlay for the location sheet -->
      <div id="location-sheet-overlay" class="location-sheet-overlay" onclick="closeLocationSheet()"></div>

      <!-- Hero Image Section -->
      <div class="hero-image-container">
        <img src="${enrichedTripPlan.heroImage || getDestinationImageSync(destination, country)}"
             alt="${destination}, ${country}"
             onerror="this.onerror=null; this.src='${getDestinationImageSync(destination, country)}'" />
        <div class="absolute">
          <h1>Your Trip to ${destination}, ${country}</h1>
          <p>${duration} Day Itinerary</p>
        </div>
      </div>

      <div class="p-8">
        <div class="mb-8">
          <h2 class="text-2xl font-bold mb-4">Overview</h2>
          <p>Here's your ${duration}-day travel plan for ${destination}, ${country}. We've included the main attractions, recommended restaurants, and useful travel tips.</p>
        </div>

        <div class="mb-8">
          <h2 class="text-2xl font-bold mb-4">Interactive Map</h2>
          <div id="main-map" data-destination="${destination}, ${country}" class="h-[400px] w-full rounded-lg mb-4 border border-gray-200 shadow-lg"></div>
          <p class="text-sm text-gray-600">The map shows the main points of interest for your trip.</p>
        </div>

        <!-- Day-by-Day Itinerary Section with Horizontal Layout -->
        <div class="mb-8 itinerary-section">
          <div class="text-center mb-10">
            <div class="flex items-center justify-center gap-3 mb-2">
              <div class="w-8 h-8 bg-blue-800 rounded-full flex items-center justify-center">
                <svg class="w-4 h-4 text-white" fill="currentColor" viewBox="0 0 20 20">
                  <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"/>
                </svg>
              </div>
              <h2 class="text-xl font-light text-gray-800 tracking-wide">Day-by-Day Itinerary</h2>
            </div>
            <p class="text-blue-600 text-sm font-light mb-6">Discover exceptional experiences • Comfort & Luxury Combined</p>

            <!-- Expand/Collapse Controls -->
            <div class="flex flex-wrap justify-center gap-2" id="week-controls" style="display: none;">
              <button onclick="expandAllWeeks()" class="bg-purple-100 border border-purple-300 text-purple-700 hover:bg-purple-200 rounded-full text-xs font-light px-4 py-2 transition-colors">
                <svg class="w-3 h-3 mr-1 inline" fill="currentColor" viewBox="0 0 20 20">
                  <path fill-rule="evenodd" d="M6 2a1 1 0 00-1 1v1H4a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V6a2 2 0 00-2-2h-1V3a1 1 0 10-2 0v1H7V3a1 1 0 00-1-1zm0 5a1 1 0 000 2h8a1 1 0 100-2H6z" clip-rule="evenodd"/>
                </svg>
                Toutes les semaines
              </button>
              <button onclick="collapseAllWeeks()" class="bg-purple-100 border border-purple-300 text-purple-700 hover:bg-purple-200 rounded-full text-xs font-light px-4 py-2 transition-colors">
                <svg class="w-3 h-3 mr-1 inline" fill="currentColor" viewBox="0 0 20 20">
                  <path fill-rule="evenodd" d="M3 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm0 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm0 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1z" clip-rule="evenodd"/>
                </svg>
                Réduire semaines
              </button>
            </div>
          </div>

          <!-- Itinerary Content -->
          <div id="itinerary-content" class="space-y-6">
            ${await generateHorizontalItinerary(enrichedTripPlan.days, destination, country)}
          </div>
        </div>

        <!-- Day Detail Modal -->
        <div id="day-modal" class="fixed inset-0 bg-black bg-opacity-90 z-50 flex items-center justify-center p-2" style="display: none;">
          <div class="bg-gray-900 border border-gray-700 rounded-2xl w-[95vw] h-[90vh] max-w-6xl overflow-hidden flex flex-col">
            <!-- Modal Header -->
            <div class="flex items-center justify-between p-6 border-b border-gray-700 bg-gray-800 flex-shrink-0">
              <div class="flex items-center gap-4">
                <div id="modal-day-number" class="w-14 h-14 bg-blue-600 rounded-full flex items-center justify-center">
                  <span class="text-lg font-medium text-white">1</span>
                </div>
                <div>
                  <h2 id="modal-day-title" class="text-2xl font-light text-white">Premier Jour</h2>
                  <p class="text-gray-400 text-base font-light">
                    <span id="modal-activities-count">0</span> expériences soigneusement sélectionnées
                  </p>
                </div>
              </div>
              <button onclick="closeDayModal()" class="text-gray-400 hover:text-white p-3 rounded-full hover:bg-gray-700 transition-colors">
                <svg class="w-6 h-6" fill="currentColor" viewBox="0 0 20 20">
                  <path fill-rule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clip-rule="evenodd"/>
                </svg>
              </button>
            </div>

            <!-- Modal Content with Scrollbar -->
            <div id="modal-content" class="p-6" style="height: calc(90vh - 120px); max-height: calc(90vh - 120px); overflow-y: scroll !important; overflow-x: hidden !important; scrollbar-width: auto !important; scrollbar-color: #D1D5DB #374151 !important;">
              <!-- Activities will be populated here -->
            </div>
          </div>
        </div>

        <div class="mb-8">
          ${await createSimpleLayout(
            await getWebAccommodations(destination, country, 12),
            'hotel',
            destination,
          )}
        </div>

        <div class="mb-8">
          ${await createSimpleLayout(
            await getWebRestaurants(destination, country, 15),
            'restaurant',
            destination,
          )}
        </div>

        <div class="mb-8">
          ${await createSimpleLayout(
            (() => {
              console.log('🔍 Local Recommendations Debug:');
              console.log(
                'tripPlan.localRecommendations:',
                tripPlan.localRecommendations,
              );

              const items = [
                // Hidden Gems
                ...(enrichedTripPlan.localRecommendations?.hiddenGems?.map(
                  (gem: any) => {
                    console.log('Processing hidden gem:', gem.name);
                    return {
                      name: gem.name,
                      description: gem.description,
                      extra: `Best time: ${gem.bestTimeToVisit}`,
                      category: 'Hidden Gem',
                      type: 'poi',
                    };
                  },
                ) || []),
                // Local Events
                ...(enrichedTripPlan.localRecommendations?.localEvents
                  ?.slice(0, 5)
                  .map((event: any) => {
                    console.log('Processing local event:', event.name);
                    return {
                      name: event.name,
                      description: event.description,
                      extra: `Date: ${event.date}`,
                      category: event.category,
                      type: 'poi',
                    };
                  }) || []),
                // Seasonal Activities
                ...(enrichedTripPlan.localRecommendations?.seasonalActivities
                  ?.slice(0, 4)
                  .map((activity: any) => {
                    console.log(
                      'Processing seasonal activity:',
                      activity.activity,
                    );
                    return {
                      name: activity.activity,
                      description: activity.description,
                      extra: `Best season: ${activity.bestSeason} | Cost: ${activity.cost}`,
                      category: 'Seasonal Activity',
                      type: 'poi',
                    };
                  }) || []),
                // Nightlife Options
                ...(enrichedTripPlan.localRecommendations?.nightlifeOptions
                  ?.slice(0, 3)
                  .map((nightlife: any) => {
                    console.log('Processing nightlife option:', nightlife.name);
                    return {
                      name: nightlife.name,
                      description: nightlife.description,
                      extra: `Type: ${nightlife.type} | Hours: ${nightlife.openingHours}`,
                      category: 'Nightlife',
                      type: 'poi',
                    };
                  }) || []),
                // Annual Events
                ...(enrichedTripPlan.localRecommendations?.annualEvents
                  ?.slice(0, 3)
                  .map((event: any) => {
                    console.log('Processing annual event:', event.name);
                    return {
                      name: event.name,
                      description: event.description,
                      extra: `Dates: ${event.dates}`,
                      category: 'Annual Event',
                      type: 'poi',
                    };
                  }) || []),
              ];

              console.log(
                `📊 Total local recommendation items: ${items.length}`,
              );
              console.log('Items:', items);

              // If no items found, add fallback recommendations
              if (items.length === 0) {
                console.log(
                  '⚠️ No local recommendations found, adding fallback items',
                );
                const fallbackItems = [
                  {
                    name: `Historic Center of ${enrichedTripPlan.destination.destination}`,
                    description:
                      'Explore the charming historic district with centuries-old architecture and cobblestone streets',
                    extra: 'Historic Site',
                    category: 'Historical',
                    type: 'poi',
                  },
                  {
                    name: `Local Market in ${enrichedTripPlan.destination.destination}`,
                    description:
                      'Experience authentic local culture and cuisine at this bustling traditional market',
                    extra: 'Market',
                    category: 'Shopping',
                    type: 'poi',
                  },
                  {
                    name: `Scenic Viewpoint`,
                    description:
                      'Breathtaking panoramic views of the city and surrounding landscapes',
                    extra: 'Viewpoint',
                    category: 'Nature',
                    type: 'poi',
                  },
                  {
                    name: `Cultural Museum`,
                    description:
                      'Discover the rich history and cultural heritage of the region',
                    extra: 'Museum',
                    category: 'Culture',
                    type: 'poi',
                  },
                  {
                    name: `Local Restaurant District`,
                    description:
                      'Authentic dining experiences featuring traditional local cuisine',
                    extra: 'Culinary Experience',
                    category: 'Food & Drink',
                    type: 'poi',
                  },
                ];
                items.push(...fallbackItems);
                console.log(`✅ Added ${fallbackItems.length} fallback items`);
              }

              return items;
            })(),
            'poi',
            tripPlan.destination.destination,
          )}
        </div>

        <!-- Practical Information section has been moved to Money Guide tab -->
          </div>
        </div>

        <div class="mb-8">
          <h2 class="text-2xl font-bold mb-4">Travel Tips</h2>
          ${generateInteractiveTravelTips(tripPlan.travelTips, tripPlan.currencyInfo, { destination: tripPlan.destination.destination, coordinates: tripPlan.destination.coordinates })}
        </div>

        <div class="mb-8">
          <h2 class="text-2xl font-bold mb-4">Budget Information</h2>
          <div class="overflow-x-auto">
            <table class="min-w-full border-collapse">
              <thead>
                <tr class="bg-gray-100">
                  <th class="p-2 text-left border border-gray-200">Category</th>
                  <th class="p-2 text-left border border-gray-200">Estimated Cost</th>
                  <th class="p-2 text-left border border-gray-200">Notes</th>
                </tr>
              </thead>
              <tbody>
                ${tripPlan.budget
                  .map(
                    (item) => `
                  <tr>
                    <td class="p-2 border border-gray-200">${item.category}</td>
                    <td class="p-2 border border-gray-200">${item.estimatedCost}</td>
                    <td class="p-2 border border-gray-200">${item.notes}</td>
                  </tr>
                `,
                  )
                  .join('')}
              </tbody>
            </table>
          </div>
        </div>
      </div>
    </div>
    </body>
    </html>
  `;

    // Basic CSS styling
    const cssContent = `
    body {
      font-family: system-ui, -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
      line-height: 1.5;
      color: #333;
      margin: 0;
      padding: 0;
    }

    .container {
      max-width: 1200px;
      margin: 0 auto;
      padding: 0 1rem;
    }

    h1, h2, h3, h4, h5, h6 {
      margin-top: 0;
    }

    table {
      border-collapse: collapse;
      width: 100%;
    }

    th, td {
      text-align: left;
      padding: 8px;
      border: 1px solid #ddd;
    }

    tr:nth-child(even) {
      background-color: #f2f2f2;
    }

    /* Map styles */
    #main-map {
      height: 400px;
      width: 100%;
      z-index: 1;
    }

    /* Ensure Leaflet styles work properly */
    .leaflet-container {
      height: 100%;
      width: 100%;
    }

    /* Hero image styles */
    .hero-image-container {
      position: relative;
      width: 100%;
      height: 50vh;
      overflow: hidden;
    }

    .hero-image-container img {
      width: 100%;
      height: 100%;
      object-fit: cover;
      transition: transform 0.3s ease;
    }

    .hero-image-container:hover img {
      transform: scale(1.05);
    }

    .hero-image-container .absolute {
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      background-color: rgba(0, 0, 0, 0.4);
      color: white;
      padding: 2rem;
      text-align: center;
    }

    /* Location Sheet Panel Styles */
    .location-sheet {
      position: fixed;
      top: 0;
      right: -100%;
      width: 90%;
      max-width: 450px;
      height: 100%;
      background-color: white;
      box-shadow: -2px 0 10px rgba(0, 0, 0, 0.15);
      z-index: 1000;
      overflow-y: auto;
      transition: right 0.3s ease-in-out;
    }

    .location-sheet.open {
      right: 0;
    }

    .location-sheet-overlay {
      position: fixed;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background-color: rgba(0, 0, 0, 0.5);
      z-index: 999;
      opacity: 0;
      visibility: hidden;
      transition: opacity 0.3s ease-in-out, visibility 0.3s ease-in-out;
    }

    .location-sheet-overlay.open {
      opacity: 1;
      visibility: visible;
    }

    .location-sheet-header {
      position: relative;
      padding: 1.5rem;
      border-bottom: 1px solid #eee;
    }

    .location-sheet-close {
      position: absolute;
      top: 1rem;
      right: 1rem;
      width: 32px;
      height: 32px;
      background-color: rgba(255, 255, 255, 0.8);
      border: none;
      border-radius: 50%;
      display: flex;
      align-items: center;
      justify-content: center;
      cursor: pointer;
      font-size: 1.25rem;
      color: #333;
      box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
      transition: background-color 0.2s;
    }

    .location-sheet-close:hover {
      background-color: #f0f0f0;
    }

    .location-sheet-title {
      font-size: 1.5rem;
      font-weight: 700;
      margin: 0;
      padding-right: 2rem;
    }

    .location-sheet-content {
      padding: 1.5rem;
    }

    .location-sheet-images {
      margin-bottom: 1.5rem;
    }

    .location-sheet-image-container {
      position: relative;
      width: 100%;
      height: 250px;
      overflow: hidden;
      border-radius: 8px;
      margin-bottom: 0.5rem;
      background-color: #f0f0f0;
    }

    /* Indicateur de chargement pour les images */
    .location-sheet-image-container::after {
      content: "";
      position: absolute;
      top: 50%;
      left: 50%;
      width: 30px;
      height: 30px;
      margin: -15px 0 0 -15px;
      border: 3px solid #f3f3f3;
      border-top: 3px solid #3f51b5;
      border-radius: 50%;
      animation: spin 1s linear infinite;
      z-index: 1;
    }

    @keyframes spin {
      0% { transform: rotate(0deg); }
      100% { transform: rotate(360deg); }
    }

    /* Masquer l'indicateur de chargement quand l'image est chargée */
    .location-sheet-image-container.image-loaded::after {
      display: none;
    }

    .location-sheet-image {
      width: 100%;
      height: 100%;
      object-fit: cover;
      transition: opacity 0.5s ease;
      background-color: #f0f0f0;
      opacity: 0; /* Commencer avec une opacité de 0 pour l'effet de transition */
    }

    .location-sheet-thumbnails {
      display: flex;
      gap: 0.5rem;
      overflow-x: auto;
      padding-bottom: 0.5rem;
      min-height: 60px;
    }

    .location-sheet-thumbnail {
      width: 60px;
      height: 60px;
      border-radius: 4px;
      overflow: hidden;
      cursor: pointer;
      border: 2px solid transparent;
      transition: border-color 0.2s ease;
      flex-shrink: 0;
      background-color: #f0f0f0;
    }

    .location-sheet-thumbnail.active {
      border-color: #3f51b5;
    }

    .location-sheet-thumbnail img {
      width: 100%;
      height: 100%;
      object-fit: cover;
      transition: opacity 0.5s ease;
      opacity: 0.7; /* Légèrement transparent par défaut */
      position: relative;
    }

    /* Effet de chargement pour les miniatures */
    .location-sheet-thumbnail::before {
      content: "";
      position: absolute;
      top: 50%;
      left: 50%;
      width: 15px;
      height: 15px;
      margin: -7.5px 0 0 -7.5px;
      border: 2px solid #f3f3f3;
      border-top: 2px solid #3f51b5;
      border-radius: 50%;
      animation: spin 1s linear infinite;
      z-index: 1;
    }

    /* La classe loaded sera ajoutée au thumbnail lui-même */
    .location-sheet-thumbnail.thumb-loaded::before {
      display: none;
    }

    .location-sheet-thumbnail.active img {
      opacity: 1; /* Complètement opaque pour la miniature active */
      border: 2px solid #fff;
      box-shadow: 0 0 0 1px #3f51b5;
    }

    .location-sheet-details {
      margin-top: 1.5rem;
    }

    .location-sheet-description {
      margin-bottom: 1rem;
      line-height: 1.6;
    }

    .location-sheet-info {
      display: grid;
      gap: 0.5rem;
      padding: 1rem;
      background-color: #f9f9f9;
      border-radius: 8px;
      font-size: 0.9rem;
    }

    /* Make location names clickable */
    .location-name {
      color: #3f51b5;
      cursor: pointer;
      font-weight: 600;
      text-decoration: none;
      border-bottom: 1px dashed #3f51b5;
      display: inline-block;
    }

    .location-name:hover {
      color: #1a237e;
      border-bottom-style: solid;
    }

    /* Horizontal Itinerary Styles */
    .itinerary-section {
      background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
      border-radius: 1rem;
      padding: 2rem;
      margin: 2rem 0;
    }

    /* Week grouping styles */
    .week-content {
      transition: max-height 0.5s ease-in-out, opacity 0.5s ease-in-out;
    }

    .week-toggle-icon {
      transition: transform 0.3s ease;
    }

    /* Day card styles */
    .group {
      transition: all 0.3s ease;
    }

    .group:hover {
      transform: translateY(-2px);
      box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15);
    }

    /* Grid responsive styles */
    @media (max-width: 640px) {
      .grid {
        grid-template-columns: repeat(1, minmax(0, 1fr));
      }
    }

    @media (min-width: 640px) and (max-width: 1024px) {
      .grid {
        grid-template-columns: repeat(2, minmax(0, 1fr));
      }
    }

    @media (min-width: 1024px) and (max-width: 1280px) {
      .grid {
        grid-template-columns: repeat(3, minmax(0, 1fr));
      }
    }

    @media (min-width: 1280px) {
      .grid {
        grid-template-columns: repeat(4, minmax(0, 1fr));
      }
    }

    /* Modal styles */
    #day-modal {
      backdrop-filter: blur(4px);
    }

    /* Utility classes for spacing */
    .space-y-6 > * + * {
      margin-top: 1.5rem;
    }

    .space-y-2 > * + * {
      margin-top: 0.5rem;
    }

    .gap-4 {
      gap: 1rem;
    }

    .gap-2 {
      gap: 0.5rem;
    }

    .gap-3 {
      gap: 0.75rem;
    }

    /* Flexbox utilities */
    .flex {
      display: flex;
    }

    .flex-col {
      flex-direction: column;
    }

    .flex-wrap {
      flex-wrap: wrap;
    }

    .items-center {
      align-items: center;
    }

    .items-start {
      align-items: flex-start;
    }

    .justify-center {
      justify-content: center;
    }

    .justify-between {
      justify-content: space-between;
    }

    /* Text utilities */
    .text-center {
      text-align: center;
    }

    .text-right {
      text-align: right;
    }

    .font-light {
      font-weight: 300;
    }

    .font-medium {
      font-weight: 500;
    }

    .tracking-wide {
      letter-spacing: 0.025em;
    }

    /* Color utilities */
    .text-white {
      color: white;
    }

    .text-gray-300 {
      color: #d1d5db;
    }

    .text-gray-400 {
      color: #9ca3af;
    }

    .text-gray-500 {
      color: #6b7280;
    }

    .text-gray-600 {
      color: #4b5563;
    }

    .text-gray-800 {
      color: #1f2937;
    }

    .text-blue-400 {
      color: #60a5fa;
    }

    .text-blue-600 {
      color: #2563eb;
    }

    .text-blue-700 {
      color: #1d4ed8;
    }

    .text-purple-600 {
      color: #9333ea;
    }

    .text-purple-700 {
      color: #7c3aed;
    }

    .text-yellow-400 {
      color: #fbbf24;
    }

    /* Background utilities */
    .bg-white {
      background-color: white;
    }

    .bg-black {
      background-color: black;
    }

    .bg-blue-800 {
      background-color: #1e40af;
    }

    .bg-blue-600 {
      background-color: #2563eb;
    }

    .bg-blue-700 {
      background-color: #1d4ed8;
    }

    .bg-blue-100 {
      background-color: #dbeafe;
    }

    .bg-purple-50 {
      background-color: #faf5ff;
    }

    .bg-purple-100 {
      background-color: #f3e8ff;
    }

    .bg-purple-200 {
      background-color: #e9d5ff;
    }

    .bg-purple-600 {
      background-color: #9333ea;
    }

    .bg-gray-200 {
      background-color: #e5e7eb;
    }

    .bg-opacity-80 {
      background-color: rgba(0, 0, 0, 0.8);
    }

    /* Border utilities */
    .border {
      border-width: 1px;
    }

    .border-gray-300 {
      border-color: #d1d5db;
    }

    .border-purple-200 {
      border-color: #e9d5ff;
    }

    .border-purple-300 {
      border-color: #d8b4fe;
    }

    .border-b {
      border-bottom-width: 1px;
    }

    /* Rounded utilities */
    .rounded-full {
      border-radius: 9999px;
    }

    .rounded-2xl {
      border-radius: 1rem;
    }

    /* Size utilities */
    .w-8 {
      width: 2rem;
    }

    .w-12 {
      width: 3rem;
    }

    .w-14 {
      width: 3.5rem;
    }

    .w-full {
      width: 100%;
    }

    .h-8 {
      height: 2rem;
    }

    .h-12 {
      height: 3rem;
    }

    .h-14 {
      height: 3.5rem;
    }

    .h-32 {
      height: 8rem;
    }

    .h-40 {
      height: 10rem;
    }

    .max-w-4xl {
      max-width: 56rem;
    }

    .max-h-0 {
      max-height: 0;
    }

    /* Position utilities */
    .relative {
      position: relative;
    }

    .absolute {
      position: absolute;
    }

    .fixed {
      position: fixed;
    }

    .inset-0 {
      top: 0;
      right: 0;
      bottom: 0;
      left: 0;
    }

    .top-2 {
      top: 0.5rem;
    }

    .left-2 {
      left: 0.5rem;
    }

    .bottom-2 {
      bottom: 0.5rem;
    }

    .z-10 {
      z-index: 10;
    }

    .z-20 {
      z-index: 20;
    }

    .z-50 {
      z-index: 50;
    }

    /* Opacity utilities */
    .opacity-0 {
      opacity: 0;
    }

    .opacity-100 {
      opacity: 1;
    }

    /* Overflow utilities */
    .overflow-hidden {
      overflow: hidden;
    }

    .overflow-y-auto {
      overflow-y: auto;
    }

    /* Shadow utilities */
    .shadow-md {
      box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
    }

    .shadow-lg {
      box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
    }

    .shadow-xl {
      box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
    }

    /* Padding utilities */
    .p-2 {
      padding: 0.5rem;
    }

    .p-4 {
      padding: 1rem;
    }

    .p-5 {
      padding: 1.25rem;
    }

    .p-6 {
      padding: 1.5rem;
    }

    .px-2 {
      padding-left: 0.5rem;
      padding-right: 0.5rem;
    }

    .px-4 {
      padding-left: 1rem;
      padding-right: 1rem;
    }

    .py-2 {
      padding-top: 0.5rem;
      padding-bottom: 0.5rem;
    }

    .py-0\.5 {
      padding-top: 0.125rem;
      padding-bottom: 0.125rem;
    }

    .pb-6 {
      padding-bottom: 1.5rem;
    }

    .ml-8 {
      margin-left: 2rem;
    }

    .mb-1 {
      margin-bottom: 0.25rem;
    }

    .mb-2 {
      margin-bottom: 0.5rem;
    }

    .mb-3 {
      margin-bottom: 0.75rem;
    }

    .mb-6 {
      margin-bottom: 1.5rem;
    }

    .mb-10 {
      margin-bottom: 2.5rem;
    }

    .mr-1 {
      margin-right: 0.25rem;
    }

    .mt-0\.5 {
      margin-top: 0.125rem;
    }

    /* Cursor utilities */
    .cursor-pointer {
      cursor: pointer;
    }

    /* Transition utilities */
    .transition-all {
      transition-property: all;
    }

    .transition-colors {
      transition-property: color, background-color, border-color, text-decoration-color, fill, stroke;
    }

    .duration-300 {
      transition-duration: 300ms;
    }

    .duration-500 {
      transition-duration: 500ms;
    }

    .ease-in-out {
      transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
    }

    /* Hover utilities */
    .hover\\:bg-purple-100:hover {
      background-color: #f3e8ff;
    }

    .hover\\:bg-purple-200:hover {
      background-color: #e9d5ff;
    }

    .hover\\:bg-blue-700:hover {
      background-color: #1d4ed8;
    }

    .hover\\:text-blue-600:hover {
      color: #2563eb;
    }

    .hover\\:text-gray-600:hover {
      color: #4b5563;
    }

    .hover\\:shadow-xl:hover {
      box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
    }

    /* Text size utilities */
    .text-xs {
      font-size: 0.75rem;
      line-height: 1rem;
    }

    .text-sm {
      font-size: 0.875rem;
      line-height: 1.25rem;
    }

    .text-base {
      font-size: 1rem;
      line-height: 1.5rem;
    }

    .text-xl {
      font-size: 1.25rem;
      line-height: 1.75rem;
    }

    /* Leading utilities */
    .leading-snug {
      line-height: 1.375;
    }

    .leading-relaxed {
      line-height: 1.625;
    }

    /* Flex utilities */
    .flex-1 {
      flex: 1 1 0%;
    }

    .flex-shrink-0 {
      flex-shrink: 0;
    }

    /* Grid utilities */
    .grid {
      display: grid;
    }

    .grid-cols-1 {
      grid-template-columns: repeat(1, minmax(0, 1fr));
    }

    /* Backdrop utilities */
    .backdrop-blur-sm {
      backdrop-filter: blur(4px);
    }

    /* Gradient utilities */
    .bg-gradient-to-t {
      background-image: linear-gradient(to top, var(--tw-gradient-stops));
    }

    .from-black\/80 {
      --tw-gradient-from: rgba(0, 0, 0, 0.8);
      --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to, rgba(0, 0, 0, 0));
    }

    .to-transparent {
      --tw-gradient-to: transparent;
    }

    .from-black\/60 {
      --tw-gradient-from: rgba(0, 0, 0, 0.6);
      --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to, rgba(0, 0, 0, 0));
    }

    /* Object fit utilities */
    .object-cover {
      object-fit: cover;
    }

    /* Inline utilities */
    .inline {
      display: inline;
    }

    /* Width utilities for SVG */
    .w-3 {
      width: 0.75rem;
    }

    .w-4 {
      width: 1rem;
    }

    .w-5 {
      width: 1.25rem;
    }

    .w-6 {
      width: 1.5rem;
    }

    .h-3 {
      height: 0.75rem;
    }

    .h-4 {
      height: 1rem;
    }

    .h-5 {
      height: 1.25rem;
    }

    .h-6 {
      height: 1.5rem;
    }
  `;

    // JavaScript for the map
    const jsContent = `
    // Variables globales pour éviter les boucles infinies
    let isClosingLocationSheet = false;
    let isOpeningLocationDetail = false;

    // Fonction pour fermer le panneau de détails de localisation
    function closeLocationSheet() {
      // Éviter les appels récursifs
      if (isClosingLocationSheet) {
        console.warn('closeLocationSheet est déjà en cours d\'exécution, ignorant l\'appel récursif');
        return;
      }

      isClosingLocationSheet = true;

      try {
        const locationSheet = document.getElementById('location-sheet');
        const locationSheetOverlay = document.getElementById('location-sheet-overlay');

        if (locationSheet) {
          locationSheet.classList.remove('open');
        }

        if (locationSheetOverlay) {
          locationSheetOverlay.classList.remove('open');
        }
      } finally {
        // Réinitialiser le drapeau après un court délai
        setTimeout(() => {
          isClosingLocationSheet = false;
        }, 100);
      }
    }

    // Fonction pour ouvrir le panneau de détails de localisation
    function openLocationDetail(element) {
      console.log('openLocationDetail called with element:', element);

      try {
        // Show the sheet and overlay immediately
        const locationSheet = document.getElementById('location-sheet');
        const locationSheetOverlay = document.getElementById('location-sheet-overlay');

        if (!locationSheet || !locationSheetOverlay) {
          console.error('Location sheet elements not found');
          return;
        }

        // Show the sheet and overlay immediately
        locationSheet.classList.add('open');
        locationSheetOverlay.classList.add('open');

        // Get location data from the element
        let locationData;
        try {
          locationData = JSON.parse(element.getAttribute('data-location'));
          console.log('Location data:', locationData);
        } catch (e) {
          console.error('Error parsing location data:', e);
          locationData = {
            name: element.textContent || 'Unknown location',
            type: 'attraction'
          };
        }

        // Get DOM elements
        const locationMainImage = document.getElementById('location-main-image');
        const locationThumbnails = document.getElementById('location-thumbnails');
        const locationDescription = document.getElementById('location-description');
        const locationInfo = document.getElementById('location-info');
        const locationTitle = document.querySelector('.location-sheet-title');

        // Update the title
        if (locationTitle) {
          locationTitle.textContent = locationData.name || 'Location Details';
        }

        // Update the description
        if (locationDescription) {
          locationDescription.textContent = locationData.description || 'No description available.';
        }

        // Update the info
        if (locationInfo) {
          let infoHTML = '';
          if (locationData.type) {
            infoHTML += '<div><strong>Type:</strong> ' + (locationData.type.charAt(0).toUpperCase() + locationData.type.slice(1)) + '</div>';
          }
          if (locationData.day) {
            infoHTML += '<div><strong>Day:</strong> ' + locationData.day + '</div>';
          }
          if (locationData.time) {
            infoHTML += '<div><strong>Time:</strong> ' + locationData.time + '</div>';
          }
          if (locationData.address) {
            infoHTML += '<div><strong>Address:</strong> ' + locationData.address + '</div>';
          }
          locationInfo.innerHTML = infoHTML || 'No additional information available.';
        }

        // Set main image with fallbacks
        if (locationMainImage) {
          // Create a function to handle image loading with fallbacks
          const setupImageWithFallbacks = (initialSrc) => {
            // Reset opacity for transition effect
            locationMainImage.style.opacity = '0';

            // Set a timeout to show the image even if events don't fire
            const showImageTimeout = setTimeout(() => {
              locationMainImage.style.opacity = '1';
            }, 1000);

            // Setup onload event
            locationMainImage.onload = function() {
              clearTimeout(showImageTimeout);
              console.log('Image loaded successfully:', locationMainImage.src);
              locationMainImage.style.opacity = '1';

              // Hide loading indicator
              const container = locationMainImage.parentElement;
              if (container) {
                container.classList.add('image-loaded');
              }
            };

            // Setup onerror event with multiple fallback levels
            locationMainImage.onerror = function() {
              clearTimeout(showImageTimeout);
              console.error('Failed to load image:', locationMainImage.src);

              // Use SVG placeholder directly
              locationMainImage.src = "data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='800' height='600' viewBox='0 0 800 600'%3E%3Crect width='800' height='600' fill='%23f0f0f0'/%3E%3Ctext x='50%25' y='50%25' font-family='Arial' font-size='24' fill='%23666' text-anchor='middle' dominant-baseline='middle'%3E" + encodeURIComponent(locationData.name || 'Location') + "%3C/text%3E%3C/svg%3E";
              locationMainImage.onerror = null; // Disable future attempts
              locationMainImage.style.opacity = '1';
            };

            // Set initial source
            locationMainImage.src = initialSrc;
          };

          // Determine initial image source
          let initialSrc;

          // First try to use provided imageUrl
          if (locationData.imageUrl) {
            initialSrc = locationData.imageUrl;
          }
          // Then try first image from imageUrls array if available
          else if (locationData.imageUrls && Array.isArray(locationData.imageUrls) && locationData.imageUrls.length > 0) {
            initialSrc = locationData.imageUrls[0];
          }
          // Finally, use SVG placeholder
          else {
            initialSrc = "data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='800' height='600' viewBox='0 0 800 600'%3E%3Crect width='800' height='600' fill='%23f0f0f0'/%3E%3Ctext x='50%25' y='50%25' font-family='Arial' font-size='24' fill='%23666' text-anchor='middle' dominant-baseline='middle'%3E" + encodeURIComponent(locationData.name || 'Location') + "%3C/text%3E%3C/svg%3E";
          }

          // Setup image with initial source and fallbacks
          setupImageWithFallbacks(initialSrc);
        }

        // Set thumbnails with fallbacks
        if (locationThumbnails) {
          // Clear existing thumbnails
          locationThumbnails.innerHTML = '';

          // Create thumbnails
          const thumbnailCount = 3;
          const thumbnailTerms = [
            locationData.name + ' ' + (locationData.type || 'attraction'),
            locationData.name + ' landmark',
            locationData.name + ' tourism'
          ];

          // Use provided imageUrls if available
          const imageUrls = locationData.imageUrls || [];
          const totalImages = Math.max(imageUrls.length, thumbnailCount);

          for (let i = 0; i < totalImages; i++) {
            const thumbnail = document.createElement('div');
            thumbnail.className = 'location-sheet-thumbnail' + (i === 0 ? ' active' : '');

            const img = document.createElement('img');

            // Function to setup image with fallbacks
            const setupThumbnailWithFallbacks = (initialSrc) => {
              // Setup onload event
              img.onload = function() {
                console.log('Thumbnail loaded successfully:', img.src);
                // Add class to parent (thumbnail) to indicate image is loaded
                thumbnail.classList.add('thumb-loaded');
              };

              // Setup onerror event with multiple fallback levels
              img.onerror = function() {
                console.error('Failed to load thumbnail:', img.src);

                // Use SVG placeholder directly
                img.src = "data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='60' height='60' viewBox='0 0 60 60'%3E%3Crect width='60' height='60' fill='%23f0f0f0'/%3E%3Ctext x='50%25' y='50%25' font-family='Arial' font-size='10' fill='%23666' text-anchor='middle' dominant-baseline='middle'%3ENo image%3C/text%3E%3C/svg%3E";
                img.onerror = null; // Disable future attempts
              };

              // Set initial source
              img.src = initialSrc;
            };

            // Determine initial image source
            let initialSrc;

            // Use imageUrls if available
            if (i < imageUrls.length) {
              initialSrc = imageUrls[i];
            } else {
              // Otherwise use SVG placeholder
              initialSrc = "data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='100' height='100' viewBox='0 0 100 100'%3E%3Crect width='100' height='100' fill='%23f0f0f0'/%3E%3Ctext x='50%25' y='50%25' font-family='Arial' font-size='12' fill='%23666' text-anchor='middle' dominant-baseline='middle'%3EImage%3C/text%3E%3C/svg%3E";
            }

            img.alt = locationData.name + ' image ' + (i+1);

            // Setup image with initial source and fallbacks
            setupThumbnailWithFallbacks(initialSrc);

            // Add click event to change main image
            thumbnail.addEventListener('click', function() {
              // Update active state
              document.querySelectorAll('.location-sheet-thumbnail').forEach(thumb => {
                thumb.classList.remove('active');
              });
              thumbnail.classList.add('active');

              // Update main image with same fallback approach
              if (locationMainImage) {
                // Reset opacity for transition effect
                locationMainImage.style.opacity = '0';

                // Set a timeout to show the image even if events don't fire
                const showImageTimeout = setTimeout(() => {
                  locationMainImage.style.opacity = '1';
                }, 1000);

                // Setup onload event
                locationMainImage.onload = function() {
                  clearTimeout(showImageTimeout);
                  console.log('Main image loaded successfully from thumbnail:', locationMainImage.src);
                  locationMainImage.style.opacity = '1';
                };

                // Setup onerror event
                locationMainImage.onerror = function() {
                  clearTimeout(showImageTimeout);
                  console.error('Failed to load main image from thumbnail:', locationMainImage.src);

                  // Use SVG placeholder directly
                  locationMainImage.src = "data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='800' height='600' viewBox='0 0 800 600'%3E%3Crect width='800' height='600' fill='%23f0f0f0'/%3E%3Ctext x='50%25' y='50%25' font-family='Arial' font-size='24' fill='%23666' text-anchor='middle' dominant-baseline='middle'%3E" + encodeURIComponent(locationData.name || 'Location') + "%3C/text%3E%3C/svg%3E";
                  locationMainImage.onerror = null; // Disable future attempts
                  locationMainImage.style.opacity = '1';
                };

                // Set source
                locationMainImage.src = img.src;
              }
            });

            thumbnail.appendChild(img);
            locationThumbnails.appendChild(thumbnail);
          }
        }
      } catch (error) {
        console.error('Error in openLocationDetail:', error);
      }
    }

    // Wait for the page to fully load
    window.addEventListener('load', function() {
      console.log('Initializing map...');

      // Make sure Leaflet is loaded
      if (typeof L === 'undefined') {
        console.error('Leaflet library not loaded. Loading it now...');

        // Dynamically load Leaflet if it's not already loaded
        const leafletCSS = document.createElement('link');
        leafletCSS.rel = 'stylesheet';
        leafletCSS.href = 'https://unpkg.com/leaflet@1.9.4/dist/leaflet.css';
        leafletCSS.integrity = 'sha256-p4NxAoJBhIIN+hmNHrzRCf9tD/miZyoHS5obTRR9BMY=';
        leafletCSS.crossOrigin = '';
        document.head.appendChild(leafletCSS);

        const leafletScript = document.createElement('script');
        leafletScript.src = 'https://unpkg.com/leaflet@1.9.4/dist/leaflet.js';
        leafletScript.integrity = 'sha256-20nQCchB9co0qIjJZRGuk2/Z9VM+kNiyxNV1lvTlZBo=';
        leafletScript.crossOrigin = '';
        leafletScript.onload = initMap;
        document.head.appendChild(leafletScript);
      } else {
        // Leaflet is already loaded, initialize the map
        initMap();
      }
    });

    function initMap() {
      console.log('Leaflet loaded, initializing map...');

      // Initialize the map
      const mapContainer = document.getElementById('main-map');
      if (!mapContainer) {
        console.error('Map container not found');
        return;
      }

      const destination = mapContainer.getAttribute('data-destination');
      console.log('Destination:', destination);

      try {
        // Create the map with a default view using the coordinates from the trip plan
        const map = L.map('main-map').setView([${tripPlan.destination.coordinates.lat}, ${tripPlan.destination.coordinates.lng}], 13);

        // Add the OpenStreetMap tiles
        L.tileLayer('https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png', {
          attribution: '&copy; <a href="https://www.openstreetmap.org/copyright">OpenStreetMap</a> contributors'
        }).addTo(map);

        console.log('Map created');

        // Add markers for all POIs
        const markers = [];
        const pois = ${JSON.stringify(tripPlan.pois)};

        pois.forEach((poi, index) => {
          try {
            const { name, lat, lng, day, type, description } = poi;

            if (lat && lng) {
              // Create marker
              const marker = L.marker([parseFloat(lat), parseFloat(lng)]).addTo(map);

              // Add popup
              marker.bindPopup(
                '<strong>' + name + '</strong><br>' +
                day + '<br>' +
                description
              );

              markers.push(marker);

              console.log('Added marker for:', name);
            }
          } catch (e) {
            console.error('Error adding POI marker:', e);
          }
        });

        // If we have markers, fit the map to show all of them
        if (markers.length > 0) {
          console.log('Fitting map to markers:', markers.length);
          const group = new L.featureGroup(markers);
          map.fitBounds(group.getBounds().pad(0.1));
        }
      } catch (error) {
        console.error('Error initializing map:', error);
      }
    }
  `;

    return {
      htmlContent,
      cssContent,
      jsContent,
    };
  } catch (error) {
    console.error('Error generating HTML with web-retrieved data:', error);

    // Fallback to AI-based generation method
    console.log('Falling back to AI-based HTML generation...');
    return await generateAdvancedTripPlanHtmlWithAI(tripPlan);
  }
}

/**
 * Generate clarification HTML when validation fails
 */
async function generateClarificationHtml(
  query: string,
  clarifyingQuestion: any,
  validationResult: ValidationResult,
): Promise<AdvancedTripPlanningOutput> {
  const htmlContent = `
    <div id="app" class="min-h-screen flex flex-col items-center justify-center relative overflow-hidden">
      <!-- Background image -->
      <div class="absolute inset-0 z-0">
        <img src="data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='1600' height='900' viewBox='0 0 1600 900'%3E%3Cdefs%3E%3ClinearGradient id='questionBg' x1='0%25' y1='0%25' x2='100%25' y2='100%25'%3E%3Cstop offset='0%25' style='stop-color:%233b82f6;stop-opacity:1' /%3E%3Cstop offset='100%25' style='stop-color:%231d4ed8;stop-opacity:1' /%3E%3C/linearGradient%3E%3C/defs%3E%3Crect width='1600' height='900' fill='url(%23questionBg)'/%3E%3Ctext x='800' y='450' font-family='Arial' font-size='48' fill='white' text-anchor='middle' dominant-baseline='middle'%3ETravel Planning%3C/text%3E%3C/svg%3E" class="w-full h-full object-cover" style="filter: blur(5px) brightness(0.8);" />
        <div class="absolute inset-0 bg-blue-900 bg-opacity-20"></div>
      </div>

      <!-- Content container -->
      <div class="relative z-10 max-w-3xl w-full mx-auto p-8 rounded-xl bg-white bg-opacity-95 shadow-2xl">
        <div class="text-center">
          <div class="flex justify-center mb-6">
            <div class="text-6xl animate-bounce">🤔</div>
          </div>

          <h1 class="text-3xl font-bold mb-4 text-blue-600">Need More Information</h1>
          <p class="text-xl mb-6 text-gray-700">To create the perfect travel guide for you, I need a bit more detail.</p>

          <div class="p-6 bg-blue-50 border border-blue-200 rounded-lg mb-6 text-left">
            <div class="flex items-start">
              <div class="flex-shrink-0 mr-3">
                <svg class="h-5 w-5 text-blue-400 mt-1" viewBox="0 0 20 20" fill="currentColor">
                  <path fill-rule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clip-rule="evenodd" />
                </svg>
              </div>
              <div>
                <p class="text-sm text-blue-700">
                  <strong>Your request:</strong> "${query}"
                </p>
              </div>
            </div>
          </div>

          <div class="mb-6 text-left">
            <h2 class="text-xl font-semibold text-gray-800 mb-3">
              ${clarifyingQuestion.question}
            </h2>

            ${
              clarifyingQuestion.suggestions &&
              clarifyingQuestion.suggestions.length > 0
                ? `
              <div class="bg-gray-50 rounded-lg p-4">
                <h3 class="text-sm font-medium text-gray-700 mb-2">💡 Suggestions:</h3>
                <ul class="text-sm text-gray-600 space-y-1">
                  ${clarifyingQuestion.suggestions.map((suggestion: string) => `<li>• ${suggestion}</li>`).join('')}
                </ul>
              </div>
            `
                : ''
            }
          </div>

          <div class="text-center">
            <p class="text-sm text-gray-500 mb-4">
              Confidence: ${Math.round(validationResult.confidence * 100)}% |
              ${validationResult.reasoning}
            </p>
            <button
              onclick="window.location.reload()"
              class="bg-blue-600 hover:bg-blue-700 text-white font-bold py-3 px-6 rounded-lg transition duration-200 transform hover:scale-105"
            >
              ✨ Try Again
            </button>
          </div>
        </div>
      </div>
    </div>
  `;

  const cssContent = `
    body {
      font-family: system-ui, -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
      margin: 0;
      padding: 0;
      background-color: #f8fafc;
      color: #334155;
      min-height: 100vh;
    }

    @keyframes bounce {
      0%, 100% { transform: translateY(0); }
      50% { transform: translateY(-10px); }
    }

    .animate-bounce {
      animation: bounce 2s ease-in-out infinite;
    }

    .transition {
      transition-property: all;
      transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
      transition-duration: 200ms;
    }

    .transform {
      transform: translateX(0) translateY(0) rotate(0) skewX(0) skewY(0) scaleX(1) scaleY(1);
    }

    .hover\\:scale-105:hover {
      transform: scale(1.05);
    }
  `;

  const jsContent = `
    console.log("Validation failed for query: ${query.replace(/"/g, '\\"')}");
    console.log("Confidence: ${validationResult.confidence}");
    console.log("Reasoning: ${validationResult.reasoning.replace(/"/g, '\\"')}");

    // Add subtle animation to the question emoji
    const emojiElement = document.querySelector('#app .animate-bounce');
    if (emojiElement) {
      console.log('Question emoji animation initialized');
    }
  `;

  return {
    htmlContent,
    cssContent,
    jsContent,
  };
}

/**
 * Generate error HTML in case the workflow fails
 */
async function generateErrorHtml(
  query: string,
  error: any,
): Promise<AdvancedTripPlanningOutput> {
  const errorMessage = error instanceof Error ? error.message : 'Unknown error';

  const htmlContent = `
    <div id="app" class="min-h-screen flex flex-col items-center justify-center relative overflow-hidden">
      <!-- Background image -->
      <div class="absolute inset-0 z-0">
        <img src="data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='1600' height='900' viewBox='0 0 1600 900'%3E%3Cdefs%3E%3ClinearGradient id='errorBg' x1='0%25' y1='0%25' x2='100%25' y2='100%25'%3E%3Cstop offset='0%25' style='stop-color:%23dc2626;stop-opacity:1' /%3E%3Cstop offset='100%25' style='stop-color:%23991b1b;stop-opacity:1' /%3E%3C/linearGradient%3E%3C/defs%3E%3Crect width='1600' height='900' fill='url(%23errorBg)'/%3E%3Ctext x='800' y='450' font-family='Arial' font-size='48' fill='white' text-anchor='middle' dominant-baseline='middle'%3EError%3C/text%3E%3C/svg%3E" class="w-full h-full object-cover" style="filter: blur(5px) brightness(0.7);" />
        <div class="absolute inset-0 bg-red-900 bg-opacity-30"></div>
      </div>

      <!-- Content container -->
      <div class="relative z-10 max-w-3xl w-full mx-auto p-8 rounded-xl bg-white bg-opacity-95 shadow-2xl">
        <div class="text-center">
          <div class="flex justify-center mb-6">
            <div class="text-6xl">🧳❌</div>
          </div>

          <h1 class="text-3xl font-bold mb-4 text-red-600">Oops! Travel Plans Interrupted</h1>
          <p class="text-xl mb-6">We couldn't generate your travel itinerary. Please try again with a different query.</p>

          <div class="p-6 bg-red-50 border border-red-200 rounded-lg mb-6 text-left">
            <h3 class="font-bold text-red-800 mb-2">What Happened:</h3>
            <p class="mb-4">We encountered an error while creating your travel plan for "${query}". This might be due to:</p>
            <ul class="list-disc pl-5 mb-4">
              <li>Temporary service unavailability</li>
              <li>Limited information about this destination</li>
              <li>Connection issues with our travel data providers</li>
            </ul>
          </div>

          <div class="mt-6">
            <h3 class="font-bold text-lg mb-3">Suggestions:</h3>
            <ul class="text-left list-disc pl-6 space-y-2">
              <li>Try specifying a clear destination (e.g., "Trip to Paris for 5 days")</li>
              <li>Make sure your destination is a real place</li>
              <li>Specify the number of days for your trip</li>
              <li>Try a different destination or duration</li>
            </ul>
          </div>

          <details class="mt-6 text-left">
            <summary class="cursor-pointer text-sm text-gray-600">Technical details</summary>
            <pre class="mt-2 p-4 bg-gray-100 rounded text-xs overflow-auto font-mono">${errorMessage}</pre>
          </details>
        </div>
      </div>
    </div>
  `;

  const cssContent = `
    body {
      font-family: system-ui, -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
      margin: 0;
      padding: 0;
      background-color: #f8fafc;
      color: #334155;
      min-height: 100vh;
    }

    .list-disc {
      list-style-type: disc;
    }

    @keyframes shake {
      0%, 100% { transform: rotate(0deg); }
      25% { transform: rotate(-5deg); }
      75% { transform: rotate(5deg); }
    }

    @keyframes iconPulse {
      0%, 100% {
        transform: scale(1);
        box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);
      }
      50% {
        transform: scale(1.05);
        box-shadow: 0 6px 20px rgba(102, 126, 234, 0.5);
      }
    }

    @keyframes sparkle {
      0%, 100% {
        opacity: 0;
        transform: scale(0.8) rotate(0deg);
      }
      50% {
        opacity: 1;
        transform: scale(1) rotate(180deg);
      }
    }

    /* Sophisticated Logo Hover Effects */
    .dining-logo-container:hover .dining-logo-glow {
      opacity: 1;
    }

    .dining-logo-container:hover .dining-logo-main {
      transform: scale(1.05);
    }

    .dining-logo-container:hover .dining-sparkles {
      opacity: 1;
    }

    .accommodation-logo-container:hover .accommodation-logo-glow {
      opacity: 1;
    }

    .accommodation-logo-container:hover .accommodation-logo-main {
      transform: scale(1.05);
    }

    .accommodation-logo-container:hover .accommodation-sparkles {
      opacity: 1;
    }

    .local-recommendations-logo-container:hover .local-recommendations-logo-glow {
      opacity: 1;
    }

    .local-recommendations-logo-container:hover .local-recommendations-logo-main {
      transform: scale(1.05);
    }

    .local-recommendations-logo-container:hover .local-recommendations-sparkles {
      opacity: 1;
    }
  `;

  const jsContent = `
    console.error("Error generating travel itinerary: ${errorMessage.replace(/"/g, '\\"')}");

    // Add a subtle animation to the error emoji
    const emojiElement = document.querySelector('#app .text-6xl');
    if (emojiElement) {
      emojiElement.style.animation = 'shake 2s ease-in-out infinite';
    }
  `;

  return {
    htmlContent,
    cssContent,
    jsContent,
  };
}

// generateInitialCard function removed - using simple layouts

/**
 * Generate interactive travel tips with modern UI inspired by financial guides
 */
function generateInteractiveTravelTips(
  travelTips: Array<{ category: string; tips: string[] }>,
  currencyInfo?: {
    code: string;
    symbol: string;
    name: string;
    country: string;
  },
  destinationInfo?: {
    destination: string;
    coordinates: { lat: string; lng: string };
  },
): string {
  // Find the Money category for special treatment
  const moneyTips = travelTips.find((tip) =>
    tip.category.toLowerCase().includes('money'),
  );
  const otherTips = travelTips.filter(
    (tip) => !tip.category.toLowerCase().includes('money'),
  );

  // Generate tab navigation with inline JavaScript
  const tabNavigation = `
    <!-- Inline JavaScript for Travel Tips -->
    <script>
      // Define the function immediately and globally
      window.showTravelTipTab = function(tabId) {
        console.log('🎯 Switching to travel tip tab:', tabId);

        // Hide all tab panels
        const panels = document.querySelectorAll('.tab-panel');
        console.log('📋 Found', panels.length, 'tab panels');
        panels.forEach(panel => {
          panel.style.display = 'none';
        });

        // Remove active class from all buttons
        const buttons = document.querySelectorAll('.tab-btn');
        console.log('🔘 Found', buttons.length, 'tab buttons');
        buttons.forEach(btn => {
          btn.classList.remove('active');
          btn.style.background = '#f8f9fa';
          btn.style.color = '#666';
          btn.style.borderBottomColor = 'transparent';
        });

        // Show selected panel
        const selectedPanel = document.getElementById(tabId);
        if (selectedPanel) {
          selectedPanel.style.display = 'block';
          console.log('✅ Showed panel:', tabId);
        } else {
          console.warn('❌ Panel not found:', tabId);
        }

        // Activate selected button
        const selectedButton = document.querySelector('[data-tab="' + tabId + '"]');
        if (selectedButton) {
          selectedButton.classList.add('active');
          selectedButton.style.background = '#667eea';
          selectedButton.style.color = 'white';
          selectedButton.style.borderBottomColor = '#5a67d8';
          console.log('✅ Activated button for:', tabId);
        } else {
          console.warn('❌ Button not found for:', tabId);
        }
      };

      // Also make it available as a global function (for compatibility)
      function showTravelTipTab(tabId) {
        window.showTravelTipTab(tabId);
      }

      // Initialize TradingView Currency Exchange Widget
      function initTradingViewWidget() {
        const container = document.getElementById('tradingview-widget-container-currency');
        if (container && !container.querySelector('script')) {
          const script = document.createElement('script');
          script.src = 'https://s3.tradingview.com/external-embedding/embed-widget-forex-cross-rates.js';
          script.async = true;
          script.innerHTML = JSON.stringify({
            width: "100%",
            height: 400,
            currencies: [
              "EUR", "USD", "JPY", "GBP", "CHF", "AUD", "CAD", "NZD", "CNY",
              "SEK", "NOK", "DKK", "PLN", "CZK", "HUF", "RON", "BGN", "HRK",
              "RUB", "TRY", "ZAR", "BRL", "MXN", "ARS", "CLP", "COP", "PEN",
              "KRW", "THB", "SGD", "MYR", "IDR", "PHP", "VND", "INR", "PKR",
              "ILS", "SAR", "AED", "QAR", "KWD", "BHD", "OMR", "JOD", "EGP"
            ],
            isTransparent: false,
            colorTheme: "light",
            locale: "fr",
            backgroundColor: "#ffffff"
          });
          container.appendChild(script);
          console.log('📊 TradingView widget initialized');
        }
      }

      // Initialize widget when DOM is ready and when Money Guide tab is shown
      document.addEventListener('DOMContentLoaded', function() {
        setTimeout(initTradingViewWidget, 1000);
      });

      // Override showTravelTipTab to initialize widget when money tab is shown
      const originalShowTravelTipTab = window.showTravelTipTab;
      window.showTravelTipTab = function(tabId) {
        originalShowTravelTipTab(tabId);
        if (tabId === 'money') {
          setTimeout(initTradingViewWidget, 500);
        }
      };

      console.log('🎯 Travel tips function defined globally');
      console.log('📊 TradingView widget function defined');
    </script>

    <style>
      .image-modal {
        display: none;
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background-color: rgba(0, 0, 0, 0.8);
        z-index: 1000;
        justify-content: center;
        align-items: center;
        opacity: 0;
        transition: opacity 0.3s ease;
      }
      
      .image-modal.show {
        display: flex;
        opacity: 1;
      }
      
      .image-modal-content {
        max-width: 90%;
        max-height: 90%;
        position: relative;
      }
      
      .image-modal img {
        max-width: 100%;
        max-height: 80vh;
        border: 3px solid white;
        border-radius: 8px;
        box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
      }
      
      .close-modal {
        position: absolute;
        top: -40px;
        right: -10px;
        color: white;
        font-size: 30px;
        font-weight: bold;
        cursor: pointer;
        background: rgba(0, 0, 0, 0.5);
        width: 36px;
        height: 36px;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        line-height: 1;
      }
      
      .currency-bill, .currency-coin {
        cursor: pointer;
        transition: transform 0.2s ease, box-shadow 0.2s ease;
      }
      
      .currency-bill:hover, .currency-coin:hover {
        transform: translateY(-3px);
        box-shadow: 0 4px 12px rgba(0,0,0,0.15);
      }
    </style>
    
    <!-- Image Modal -->
    <div id="currencyImageModal" class="image-modal">
      <div class="image-modal-content">
        <span class="close-modal">&times;</span>
        <img id="modalImage" src="" alt="Enlarged view">
      </div>
    </div>
    
    <script>
      document.addEventListener('DOMContentLoaded', function() {
        const modal = document.getElementById('currencyImageModal');
        const modalImg = document.getElementById('modalImage');
        const closeBtn = document.querySelector('.close-modal');
        
        // Open modal when clicking on currency images
        document.addEventListener('click', function(e) {
          const target = e.target.closest('.currency-bill img, .currency-coin img');
          if (target) {
            e.preventDefault();
            modalImg.src = target.src;
            modal.classList.add('show');
            document.body.style.overflow = 'hidden';
          }
        });
        
        // Close modal
        function closeModal() {
          modal.classList.remove('show');
          document.body.style.overflow = 'auto';
        }
        
        closeBtn.addEventListener('click', closeModal);
        modal.addEventListener('click', function(e) {
          if (e.target === modal) closeModal();
        });
        
        // Close with ESC key
        document.addEventListener('keydown', function(e) {
          if (e.key === 'Escape') closeModal();
        });
      });
    </script>

    <div class="travel-tips-container" style="background: #f8f9fa; border-radius: 12px; overflow: hidden; box-shadow: 0 4px 12px rgba(0,0,0,0.1);">
      <!-- Header -->
      <div style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; padding: 20px; text-align: center;">
        <h3 style="margin: 0; font-size: 1.5em; font-weight: 600;">🎯 Essential Travel Information</h3>
        <p style="margin: 8px 0 0 0; opacity: 0.9;">Your complete guide for a perfect trip</p>
      </div>

      <!-- Tab Navigation -->
      <div class="tab-navigation" style="display: flex; background: white; border-bottom: 1px solid #e9ecef; overflow-x: auto;">
        ${
          moneyTips
            ? `
        <button class="tab-btn active" data-tab="money" onclick="showTravelTipTab('money')" style="
          flex: 1; padding: 15px 20px; border: none; background: #667eea; color: white;
          font-weight: 600; cursor: pointer; transition: all 0.3s ease; min-width: 120px;
          border-bottom: 3px solid #5a67d8;
        ">
          💰 Money Guide
        </button>`
            : ''
        }
        ${otherTips
          .map(
            (tip, index) => `
        <button class="tab-btn ${!moneyTips && index === 0 ? 'active' : ''}" data-tab="${tip.category.toLowerCase().replace(/\s+/g, '-')}" onclick="showTravelTipTab('${tip.category.toLowerCase().replace(/\s+/g, '-')}')" style="
          flex: 1; padding: 15px 20px; border: none; background: ${!moneyTips && index === 0 ? '#667eea' : '#f8f9fa'};
          color: ${!moneyTips && index === 0 ? 'white' : '#666'}; font-weight: 600; cursor: pointer;
          transition: all 0.3s ease; min-width: 120px;
          border-bottom: 3px solid ${!moneyTips && index === 0 ? '#5a67d8' : 'transparent'};
        ">
          ${getCategoryIcon(tip.category)} ${tip.category}
        </button>`,
          )
          .join('')}
      </div>

      <!-- Tab Content -->
      <div class="tab-content" style="padding: 0;">
        ${moneyTips ? generateMoneyGuideContent(moneyTips, currencyInfo, destinationInfo) : ''}
        ${otherTips.map((tip, index) => generateStandardTipContent(tip, !moneyTips && index === 0)).join('')}
      </div>
    </div>
  `;

  return tabNavigation;
}

/**
 * Generate interactive currency map with filters and location data
 */
function generateInteractiveCurrencyMap(
  currencyInfo: any,
  destination: string,
  coordinates: { lat: string; lng: string },
): string {
  const mapId = `currency-map-${Date.now()}`;

  return `
    <div id="interactive-currency-map" style="margin: 25px 0; background: white; border-radius: 12px; box-shadow: 0 4px 8px rgba(0,0,0,0.1); overflow: hidden;">
      <!-- Map Header -->
      <div style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; padding: 20px;">
        <h4 style="margin: 0 0 10px 0; font-size: 1.3em; font-weight: 600; display: flex; align-items: center;">
          <span style="margin-right: 10px; font-size: 1.4em;">🗺️</span> Interactive Financial Map
        </h4>
        <p style="margin: 0; opacity: 0.9; font-size: 0.9em;">Find ATMs, payment options, and safe zones near your location</p>
      </div>

      <!-- Map Filters -->
      <div style="padding: 15px; background: #f8f9fa; border-bottom: 1px solid #e9ecef;">
        <div style="display: flex; flex-wrap: wrap; gap: 10px; align-items: center;">
          <span style="font-weight: 600; color: #495057; margin-right: 10px;">Filters:</span>

          <button class="map-filter" data-filter="fee-free" style="
            padding: 6px 12px; border: 1px solid #28a745; background: white; color: #28a745;
            border-radius: 20px; font-size: 0.8em; cursor: pointer; transition: all 0.2s;
          ">💰 Fee-Free ATMs</button>

          <button class="map-filter" data-filter="open-now" style="
            padding: 6px 12px; border: 1px solid #007bff; background: white; color: #007bff;
            border-radius: 20px; font-size: 0.8em; cursor: pointer; transition: all 0.2s;
          ">🕒 Open Now</button>

          <button class="map-filter" data-filter="alipay" style="
            padding: 6px 12px; border: 1px solid #1890ff; background: white; color: #1890ff;
            border-radius: 20px; font-size: 0.8em; cursor: pointer; transition: all 0.2s;
          ">📱 Alipay Accepted</button>

          <button class="map-filter" data-filter="cash-only" style="
            padding: 6px 12px; border: 1px solid #ffc107; background: white; color: #ffc107;
            border-radius: 20px; font-size: 0.8em; cursor: pointer; transition: all 0.2s;
          ">💵 Cash Only</button>

          <button class="map-filter" data-filter="safe-zones" style="
            padding: 6px 12px; border: 1px solid #28a745; background: white; color: #28a745;
            border-radius: 20px; font-size: 0.8em; cursor: pointer; transition: all 0.2s;
          ">🛡️ Safe Zones</button>
        </div>
      </div>

      <!-- Map Container -->
      <div style="position: relative; height: 400px; background: #e9ecef;">
        <div id="${mapId}" style="width: 100%; height: 100%; position: relative; background: linear-gradient(45deg, #f8f9fa 25%, transparent 25%), linear-gradient(-45deg, #f8f9fa 25%, transparent 25%); background-size: 20px 20px;">

          <!-- Map Placeholder -->
          <div style="position: absolute; top: 50%; left: 50%; transform: translate(-50%, -50%); text-align: center; background: white; padding: 20px; border-radius: 8px; box-shadow: 0 2px 8px rgba(0,0,0,0.1);">
            <div style="font-size: 2em; margin-bottom: 10px;">📍</div>
            <h5 style="margin: 0 0 10px 0; color: #495057;">${destination}</h5>
            <p style="margin: 0; color: #6c757d; font-size: 0.9em;">Interactive map will load here</p>
            <p style="margin: 5px 0 0 0; color: #6c757d; font-size: 0.8em;">Coordinates: ${coordinates.lat}, ${coordinates.lng}</p>
          </div>

          <!-- Sample Location Markers -->
          ${
            currencyInfo?.exchangeInfo?.atmInfo
              ? `
          <div class="location-marker marker-atm" data-filter-type="fee-free" style="position: absolute; top: 30%; left: 25%; width: 30px; height: 30px; border-radius: 50%; background: #28a745; color: white; display: flex; align-items: center; justify-content: center; cursor: pointer;">🏧</div>
          <div class="location-marker marker-atm" data-filter-type="open-now" style="position: absolute; top: 60%; left: 70%; width: 30px; height: 30px; border-radius: 50%; background: #28a745; color: white; display: flex; align-items: center; justify-content: center; cursor: pointer;">🏧</div>
          `
              : ''
          }

          ${
            currencyInfo?.paymentMethods?.digitalPayments
              ? `
          <div class="location-marker marker-restaurant" data-filter-type="alipay" style="position: absolute; top: 45%; left: 50%; width: 30px; height: 30px; border-radius: 50%; background: #ffc107; color: #212529; display: flex; align-items: center; justify-content: center; cursor: pointer;">🍽️</div>
          <div class="location-marker marker-restaurant" data-filter-type="cash-only" style="position: absolute; top: 70%; left: 30%; width: 30px; height: 30px; border-radius: 50%; background: #ffc107; color: #212529; display: flex; align-items: center; justify-content: center; cursor: pointer;">🍜</div>
          `
              : ''
          }

          ${
            currencyInfo?.safetyAndPrecautions?.localSafety
              ? `
          <div class="location-marker marker-safe" data-filter-type="safe-zones" style="position: absolute; top: 25%; left: 60%; width: 30px; height: 30px; border-radius: 50%; background: #17a2b8; color: white; display: flex; align-items: center; justify-content: center; cursor: pointer;">🛡️</div>
          <div class="location-marker marker-risk" style="position: absolute; top: 80%; left: 80%; width: 30px; height: 30px; border-radius: 50%; background: #dc3545; color: white; display: flex; align-items: center; justify-content: center; cursor: pointer;">⚠️</div>
          `
              : ''
          }
        </div>
      </div>

      <!-- Location Details Panel -->
      <div style="padding: 20px; background: white;">
        <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: 15px;">

          ${
            currencyInfo?.exchangeInfo?.atmInfo
              ? `
          <div style="background: #f8f9fa; padding: 15px; border-radius: 8px;">
            <h6 style="margin: 0 0 10px 0; color: #495057; display: flex; align-items: center;">
              <span style="margin-right: 8px;">🏧</span> Nearby ATMs
            </h6>
            ${
              currencyInfo.exchangeInfo.atmInfo.nearTouristSites
                ? `
            <div style="margin-bottom: 10px; padding: 10px; background: white; border-radius: 4px; border-left: 3px solid #28a745;">
              <p style="margin: 0; font-size: 0.85em; font-weight: 600;">Near Tourist Sites:</p>
              <p style="margin: 2px 0 0 0; font-size: 0.8em;">${currencyInfo.exchangeInfo.atmInfo.nearTouristSites}</p>
            </div>
            `
                : ''
            }
            ${
              currencyInfo.exchangeInfo.atmInfo.freeAtmLocations
                ? `
            <div style="margin-bottom: 10px; padding: 10px; background: white; border-radius: 4px; border-left: 3px solid #007bff;">
              <p style="margin: 0; font-size: 0.85em; font-weight: 600;">💰 Fee-Free Options:</p>
              <p style="margin: 2px 0 0 0; font-size: 0.8em;">${currencyInfo.exchangeInfo.atmInfo.freeAtmLocations}</p>
            </div>
            `
                : ''
            }
          </div>
          `
              : ''
          }

          ${
            currencyInfo?.interactiveLocations
              ? `
          <div style="background: #f8f9fa; padding: 15px; border-radius: 8px;">
            <h6 style="margin: 0 0 10px 0; color: #495057; display: flex; align-items: center;">
              <span style="margin-right: 8px;">💳</span> Payment Acceptance
            </h6>
            <div style="margin-bottom: 10px; padding: 10px; background: white; border-radius: 4px;">
              <p style="margin: 0; font-size: 0.8em;">${currencyInfo.interactiveLocations.paymentAcceptance}</p>
            </div>
          </div>
          `
              : ''
          }

          ${
            currencyInfo?.safetyAndPrecautions?.localSafety
              ? `
          <div style="background: #f8f9fa; padding: 15px; border-radius: 8px;">
            <h6 style="margin: 0 0 10px 0; color: #495057; display: flex; align-items: center;">
              <span style="margin-right: 8px;">🛡️</span> Safety Zones
            </h6>
            ${
              currencyInfo.safetyAndPrecautions.localSafety.safeZones
                ? `
            <div style="margin-bottom: 8px; padding: 8px; background: #d4edda; border-radius: 4px; border-left: 3px solid #28a745;">
              <p style="margin: 0; font-size: 0.8em; font-weight: 600;">✅ Safe Areas:</p>
              <p style="margin: 2px 0 0 0; font-size: 0.75em;">${currencyInfo.safetyAndPrecautions.localSafety.safeZones}</p>
            </div>
            `
                : ''
            }
            ${
              currencyInfo.safetyAndPrecautions.localSafety.riskZones
                ? `
            <div style="padding: 8px; background: #f8d7da; border-radius: 4px; border-left: 3px solid #dc3545;">
              <p style="margin: 0; font-size: 0.8em; font-weight: 600;">⚠️ Risk Areas:</p>
              <p style="margin: 2px 0 0 0; font-size: 0.75em;">${currencyInfo.safetyAndPrecautions.localSafety.riskZones}</p>
            </div>
            `
                : ''
            }
          </div>
          `
              : ''
          }
        </div>
      </div>

      <!-- Optimized Routes -->
      ${
        currencyInfo?.interactiveLocations?.optimizedRoutes
          ? `
      <div style="margin-top: 20px; padding: 20px; background: linear-gradient(135deg, #ffecd2 0%, #fcb69f 100%); border-radius: 8px;">
        <h5 style="margin: 0 0 15px 0; color: #495057; display: flex; align-items: center;">
          <span style="margin-right: 10px; font-size: 1.2em;">🗺️</span> Optimized Routes
        </h5>
        <div style="background: rgba(255,255,255,0.8); padding: 15px; border-radius: 6px;">
          <p style="margin: 0; font-size: 0.9em; line-height: 1.4;">${currencyInfo.interactiveLocations.optimizedRoutes}</p>
        </div>
      </div>
      `
          : ''
      }
    </div>

    <style>
      .map-filter:hover {
        background: var(--filter-color) !important;
        color: white !important;
      }

      .map-filter.active {
        background: var(--filter-color) !important;
        color: white !important;
      }

      .location-marker:hover {
        transform: scale(1.2);
        z-index: 10;
      }
    </style>

    <script>
      // Initialize map filters
      document.querySelectorAll('.map-filter').forEach(button => {
        button.addEventListener('click', function() {
          this.classList.toggle('active');
          const filter = this.dataset.filter;
          toggleMapFilter(filter);
        });
      });

      function toggleMapFilter(filterType) {
        const markers = document.querySelectorAll(\`[data-filter-type="\${filterType}"]\`);
        markers.forEach(marker => {
          marker.style.display = marker.style.display === 'none' ? 'flex' : 'none';
        });
      }

      // Location marker click handlers
      document.querySelectorAll('.location-marker').forEach(marker => {
        marker.addEventListener('click', function() {
          const info = this.dataset.info || 'Location details';
          alert('Location Details: ' + info);
        });
      });
    </script>
  `;
}

/**
 * Generate enhanced Money Guide content with financial information
 */
function generateMoneyGuideContent(
  moneyTips: {
    category: string;
    tips: string[];
  },
  currencyInfo?: {
    code: string;
    symbol: string;
    name: string;
    country: string;
    exchangeInfo?: {
      currentRate: {
        usd: number;
        eur: number;
        lastUpdated: string;
      };
      bestExchangeLocations: Array<{
        type: string;
        name: string;
        fees: string;
        rate: string;
        locations: string;
        openingHours: string;
        notes?: string;
      }>;
      atmInfo: {
        networks: string[];
        fees: string;
        dailyLimits: string;
        locations: string;
        languageSupport: string;
        concreteExample: string;
        freeAtmLocations: string;
        nearTouristSites: string;
      };
    };
    paymentMethods?: {
      creditCards: {
        acceptance: string;
        preferredCards: string[];
        fees: string;
      };
      cash: {
        importance: string;
        recommendations: string;
      };
      digitalPayments: {
        localApps: string[];
        internationalAccess: string;
        accessibilityDetails: string;
        modernSolutions: string[];
        qrCodePayments: string;
      };
      transportCards: {
        available: string[];
        touristFriendly: string;
        depositRefund: string;
        usageAreas: string;
        purchaseLocations: string;
        cardImages: string;
      };
    };
    tipping?: {
      culture: string;
      restaurants: string;
      taxis: string;
      hotels: string;
      other: string;
    };
    typicalCosts?: {
      meals: {
        budget: string;
        midRange: string;
        upscale: string;
      };
      transport: {
        publicTransport: string;
        taxi: string;
        rideshare: string;
      };
      accommodation: {
        budget: string;
        midRange: string;
        luxury: string;
      };
    };
    safetyAndPrecautions?: {
      avoidFees: string;
      cardActivation: string;
      safetyTips: string;
      backupPayments: string;
      localSafety: {
        atmSafety: string;
        cashLimits: string;
        cardSkimming: string;
        emergencyAccess: string;
        riskZones: string;
        safeZones: string;
      };
    };
    practicalTips?: {
      bestStrategy: string;
      avoidTouristTraps: string;
      specialConsiderations: string;
      costMinimization: string;
    };
    concreteExamples?: {
      atmWithdrawal: string;
      restaurantPayment: string;
      transportPayment: string;
      shoppingPayment: string;
      hotelPayment: string;
    };
    interactiveLocations?: {
      atmLocations: string;
      paymentAcceptance: string;
      optimizedRoutes: string;
    };
    denominations?: {
      bills: Array<{
        value: number;
        type: 'bill';
        description?: string;
        color?: string;
        material?: string;
        size?: string;
        imageUrl?: string;
      }>;
      coins: Array<{
        value: number;
        type: 'coin';
        description?: string;
        color?: string;
        material?: string;
        size?: string;
        imageUrl?: string;
      }>;
    };
  },
  destinationInfo?: {
    destination: string;
    coordinates: { lat: string; lng: string };
  },
): string {
  return `
    <div id="money" class="tab-panel active" style="display: block; padding: 0;">
      <!-- Money Guide Header -->
      <div style="background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%); color: white; padding: 25px; text-align: center;">
        <h4 style="margin: 0; font-size: 1.4em; font-weight: 600;">💰 Complete Financial Guide</h4>
        <p style="margin: 8px 0 0 0; opacity: 0.9;">Everything you need to know about money and payments</p>
      </div>

      <!-- Financial Information Grid -->
      <div style="padding: 25px; background: white;">
        <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 20px; margin-bottom: 25px;">

          <!-- Currency Information -->
          <div style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; padding: 20px; border-radius: 12px; box-shadow: 0 4px 8px rgba(0,0,0,0.1);">
            <h5 style="margin: 0 0 15px 0; font-size: 1.2em; display: flex; align-items: center;">
              <span style="margin-right: 10px; font-size: 1.5em;">💱</span> Currency Information
            </h5>
            ${
              currencyInfo
                ? `
              <!-- Basic Currency Info -->
              <div style="background: rgba(255,255,255,0.1); padding: 15px; border-radius: 8px; margin-bottom: 15px;">
                <p style="margin: 5px 0; font-size: 0.95em;"><strong>Currency:</strong> ${currencyInfo.name} (${currencyInfo.code})</p>
                <p style="margin: 5px 0; font-size: 0.95em;"><strong>Symbol:</strong> ${currencyInfo.symbol}</p>
                <p style="margin: 5px 0; font-size: 0.95em;"><strong>Country:</strong> ${currencyInfo.country}</p>
                ${
                  currencyInfo.exchangeInfo?.currentRate
                    ? `
                <div style="margin-top: 10px; padding: 10px; background: rgba(255,255,255,0.15); border-radius: 6px;">
                  <p style="margin: 0; font-size: 0.9em; font-weight: 600;">📈 Current Rates:</p>
                  <p style="margin: 2px 0; font-size: 0.85em;">1 USD = ${currencyInfo.exchangeInfo.currentRate.usd} ${currencyInfo.code}</p>
                  <p style="margin: 2px 0; font-size: 0.85em;">1 EUR = ${currencyInfo.exchangeInfo.currentRate.eur} ${currencyInfo.code}</p>
                  <p style="margin: 2px 0; font-size: 0.8em; opacity: 0.8;">Updated: ${currencyInfo.exchangeInfo.currentRate.lastUpdated}</p>
                </div>
                `
                    : `
                <p style="margin: 5px 0; font-size: 0.95em;">Check current exchange rates before traveling</p>
                <p style="margin: 5px 0; font-size: 0.95em;">Notify your bank of travel plans</p>
                `
                }
              </div>

              ${
                currencyInfo.denominations
                  ? `
              <!-- Bills and Coins Visual Guide -->
              <div style="background: rgba(255,255,255,0.95); padding: 18px; border-radius: 8px; margin-bottom: 15px; color: #333;">
                <h6 style="margin: 0 0 15px 0; font-size: 1.1em; font-weight: 600; text-align: center; color: #2c3e50;">
                  💵 Bills & Coins Currently in Circulation
                </h6>
                
                ${
                  currencyInfo.denominations.bills &&
                  currencyInfo.denominations.bills.length > 0
                    ? `
                <!-- Banknotes Section -->
                <div style="margin-bottom: 20px;">
                  <h7 style="display: block; margin: 0 0 12px 0; font-size: 1em; font-weight: 600; color: #4a5568; border-bottom: 2px solid #e2e8f0; padding-bottom: 5px;">
                    🏦 Banknotes
                  </h7>
                  <div class="currency-denomination-grid" style="display: grid; grid-template-columns: repeat(auto-fit, minmax(140px, 1fr)); gap: 12px;">
                    ${currencyInfo.denominations.bills
                      .sort((a, b) => b.value - a.value)
                      .map(
                        (bill) => `
                        <div class="currency-bill" style="
                          background: ${bill.imageUrl ? 'white' : `linear-gradient(135deg, ${bill.color || '#4a90e2'} 0%, ${bill.color ? `${bill.color}dd` : '#357abd'} 100%)`};
                          color: ${bill.imageUrl ? '#333' : 'white'};
                          padding: ${bill.imageUrl ? '8px' : '12px 8px'};
                          border-radius: 8px;
                          text-align: center;
                          box-shadow: 0 2px 8px rgba(0,0,0,0.15);
                          border: 2px solid ${bill.imageUrl ? '#e2e8f0' : 'rgba(255,255,255,0.2)'};
                          position: relative;
                          overflow: hidden;
                          cursor: pointer;
                        ">
                          ${
                            bill.imageUrl
                              ? `
                            <div style="margin-bottom: 8px;">
                              <img src="${bill.imageUrl}" alt="${currencyInfo.symbol}${bill.value} bill" style="
                                width: 100%;
                                height: 60px;
                                object-fit: cover;
                                border-radius: 4px;
                                border: 1px solid #e2e8f0;
                              " onerror="console.log('Failed to load bill image for ${currencyInfo.symbol}${bill.value}:', this.src); this.style.display='none'; this.nextElementSibling.style.display='block';" onload="console.log('Successfully loaded bill image for ${currencyInfo.symbol}${bill.value}');">
                              <div style="display: none; background: linear-gradient(135deg, ${bill.color || '#4a90e2'} 0%, ${bill.color ? `${bill.color}dd` : '#357abd'} 100%); color: white; padding: 20px; border-radius: 4px;">
                                <div style="font-size: 1.2em; font-weight: bold;">${currencyInfo.symbol}${bill.value}</div>
                                <div style="font-size: 0.8em; opacity: 0.9; margin-top: 4px;">Image not available</div>
                              </div>
                            </div>
                          `
                              : `
                            <div style="position: absolute; top: -10px; right: -10px; width: 30px; height: 30px; background: rgba(255,255,255,0.1); border-radius: 50%; opacity: 0.3;"></div>
                          `
                          }
                          <div style="font-size: 1.1em; font-weight: bold; margin-bottom: 4px;">
                            ${currencyInfo.symbol}${bill.value}
                          </div>
                          <div style="font-size: 0.75em; opacity: 0.9; line-height: 1.2;">
                            ${bill.description || `${bill.color || 'Standard'} bill`}
                          </div>
                          ${bill.material ? `<div style="font-size: 0.7em; opacity: 0.8; margin-top: 2px;">${bill.material}</div>` : ''}
                        </div>
                      `,
                      )
                      .join('')}
                  </div>
                </div>
                `
                    : ''
                }

                ${
                  currencyInfo.denominations.coins &&
                  currencyInfo.denominations.coins.length > 0
                    ? `
                <!-- Coins Section -->
                <div>
                  <h7 style="display: block; margin: 0 0 12px 0; font-size: 1em; font-weight: 600; color: #4a5568; border-bottom: 2px solid #e2e8f0; padding-bottom: 5px;">
                    🪙 Coins
                  </h7>
                  <div class="currency-denomination-grid" style="display: grid; grid-template-columns: repeat(auto-fit, minmax(100px, 1fr)); gap: 10px;">
                    ${currencyInfo.denominations.coins
                      .sort((a, b) => b.value - a.value)
                      .map(
                        (coin) => `
                        <div class="currency-coin" style="
                          background: ${coin.imageUrl ? 'white' : `radial-gradient(circle, ${coin.color || '#c0c0c0'} 0%, ${coin.color ? `${coin.color}aa` : '#a0a0a0'} 100%)`};
                          color: ${coin.imageUrl ? '#333' : coin.color && (coin.color.toLowerCase().includes('gold') || coin.color.toLowerCase().includes('yellow')) ? '#333' : 'white'};
                          padding: ${coin.imageUrl ? '6px' : '10px 6px'};
                          border-radius: 50%;
                          text-align: center;
                          box-shadow: 0 2px 6px rgba(0,0,0,0.2);
                          border: 3px solid ${coin.imageUrl ? '#e2e8f0' : 'rgba(255,255,255,0.3)'};
                          aspect-ratio: 1;
                          display: flex;
                          flex-direction: column;
                          justify-content: center;
                          align-items: center;
                          position: relative;
                          cursor: pointer;
                        ">
                          ${
                            coin.imageUrl
                              ? `
                            <div style="margin-bottom: 4px; width: 100%; height: 50px; display: flex; align-items: center; justify-content: center;">
                              <img src="${coin.imageUrl}" alt="${coin.value < 1 ? `${Math.round(coin.value * 100)}¢` : `${currencyInfo.symbol}${coin.value}`} coin" style="
                                max-width: 100%;
                                max-height: 100%;
                                object-fit: contain;
                                border-radius: 50%;
                                border: 1px solid #e2e8f0;
                              " onerror="console.log('Failed to load coin image for ${coin.value < 1 ? `${Math.round(coin.value * 100)}¢` : `${currencyInfo.symbol}${coin.value}`}:', this.src); this.style.display='none'; this.nextElementSibling.style.display='flex';" onload="console.log('Successfully loaded coin image for ${coin.value < 1 ? `${Math.round(coin.value * 100)}¢` : `${currencyInfo.symbol}${coin.value}`}');">
                              <div style="display: none; background: radial-gradient(circle, ${coin.color || '#c0c0c0'} 0%, ${coin.color ? `${coin.color}aa` : '#a0a0a0'} 100%); color: white; width: 40px; height: 40px; border-radius: 50%; align-items: center; justify-content: center; font-weight: bold; flex-direction: column;">
                                <div>${coin.value < 1 ? `${Math.round(coin.value * 100)}¢` : `${currencyInfo.symbol}${coin.value}`}</div>
                                <div style="font-size: 0.6em; opacity: 0.8;">No image</div>
                              </div>
                            </div>
                          `
                              : `
                            <div style="position: absolute; top: 15%; left: 20%; width: 8px; height: 8px; background: rgba(255,255,255,0.4); border-radius: 50%; opacity: 0.6;"></div>
                          `
                          }
                          <div style="font-size: 0.9em; font-weight: bold; margin-bottom: 2px;">
                            ${coin.value < 1 ? `${Math.round(coin.value * 100)}¢` : `${currencyInfo.symbol}${coin.value}`}
                          </div>
                          <div style="font-size: 0.65em; opacity: 0.9; line-height: 1.1; text-align: center;">
                            ${coin.material || coin.color || 'Metal'}
                          </div>
                        </div>
                      `,
                      )
                      .join('')}
                  </div>
                </div>
                `
                    : ''
                }

                <div style="margin-top: 15px; padding: 10px; background: #f7fafc; border-radius: 6px; border-left: 4px solid #4299e1;">
                  <p style="margin: 0; font-size: 0.85em; color: #2d3748; font-style: italic;">
                    💡 <strong>Tip:</strong> Familiarize yourself with these denominations before traveling to avoid confusion during transactions.
                  </p>
                </div>
              </div>
              `
                  : ''
              }
            `
                : `
              <div style="background: rgba(255,255,255,0.1); padding: 15px; border-radius: 8px; margin-bottom: 15px;">
                <p style="margin: 5px 0;">Check current exchange rates before traveling</p>
                <p style="margin: 5px 0;">Notify your bank of travel plans</p>
              </div>
            `
            }
            
            <!-- Tableau des Taux de Change TradingView -->
            <div style="background: rgba(255,255,255,0.95); padding: 16px; border-radius: 8px; color: #333;">
              <h6 style="margin: 0 0 12px 0; font-size: 1.1em; font-weight: 600; text-align: center; color: #2c3e50;">
                📊 Tableau des Taux de Change en Temps Réel
              </h6>
              <div class="tradingview-widget-container" style="width: 100%;" id="tradingview-widget-container-currency">
                <div class="tradingview-widget-container__widget"></div>
                <div style="margin-top: 8px; text-align: center;">
                  <a href="https://fr.tradingview.com/" rel="noopener nofollow" target="_blank" style="font-size: 0.8em; color: #718096; text-decoration: none;">
                    Tableau des taux de change par TradingView
                  </a>
                </div>
              </div>
            </div>
          </div>

          <!-- Exchange & Banking -->
          <div style="background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%); color: white; padding: 20px; border-radius: 12px; box-shadow: 0 4px 8px rgba(0,0,0,0.1);">
            <h5 style="margin: 0 0 15px 0; font-size: 1.2em; display: flex; align-items: center;">
              <span style="margin-right: 10px; font-size: 1.5em;">🏦</span> Banking & Exchange
            </h5>
            ${
              currencyInfo?.exchangeInfo?.bestExchangeLocations?.length
                ? `
              <div style="background: rgba(255,255,255,0.1); padding: 15px; border-radius: 8px; margin-bottom: 15px;">
                <p style="margin: 0 0 10px 0; font-size: 0.9em; font-weight: 600;">🏦 Best Exchange Locations:</p>
                ${currencyInfo.exchangeInfo.bestExchangeLocations
                  .map(
                    (location) => `
                  <div style="margin-bottom: 12px; padding: 8px; background: rgba(255,255,255,0.1); border-radius: 4px;">
                    <p style="margin: 0; font-size: 0.85em; font-weight: 600;">📍 ${location.name} (${location.type})</p>
                    <p style="margin: 2px 0; font-size: 0.8em;">💰 Rate: ${location.rate}, Fees: ${location.fees}</p>
                    <p style="margin: 2px 0; font-size: 0.8em;">📍 ${location.locations}</p>
                    <p style="margin: 2px 0; font-size: 0.8em;">🕒 Hours: ${location.openingHours}</p>
                    ${location.notes ? `<p style="margin: 2px 0; font-size: 0.75em; opacity: 0.9;">ℹ️ ${location.notes}</p>` : ''}
                  </div>
                `,
                  )
                  .join('')}
              </div>
              ${
                currencyInfo.exchangeInfo.atmInfo
                  ? `
              <div style="background: rgba(255,255,255,0.1); padding: 15px; border-radius: 8px;">
                <p style="margin: 0 0 8px 0; font-size: 0.9em; font-weight: 600;">🏧 ATM Information:</p>
                <p style="margin: 2px 0; font-size: 0.8em;">• Networks: ${currencyInfo.exchangeInfo.atmInfo.networks.join(', ')}</p>
                <p style="margin: 2px 0; font-size: 0.8em;">• Fees: ${currencyInfo.exchangeInfo.atmInfo.fees}</p>
                <p style="margin: 2px 0; font-size: 0.8em;">• Daily Limits: ${currencyInfo.exchangeInfo.atmInfo.dailyLimits}</p>
                <p style="margin: 2px 0; font-size: 0.8em;">• Locations: ${currencyInfo.exchangeInfo.atmInfo.locations}</p>
                <p style="margin: 2px 0; font-size: 0.8em;">• Language: ${currencyInfo.exchangeInfo.atmInfo.languageSupport}</p>
                ${
                  currencyInfo.exchangeInfo.atmInfo.freeAtmLocations
                    ? `
                <p style="margin: 2px 0; font-size: 0.8em;">• 💰 Fee-Free: ${currencyInfo.exchangeInfo.atmInfo.freeAtmLocations}</p>
                `
                    : ''
                }
                ${
                  currencyInfo.exchangeInfo.atmInfo.nearTouristSites
                    ? `
                <p style="margin: 2px 0; font-size: 0.8em;">• 📍 Near Attractions: ${currencyInfo.exchangeInfo.atmInfo.nearTouristSites}</p>
                `
                    : ''
                }
                <div style="margin-top: 8px; padding: 8px; background: rgba(255,255,255,0.15); border-radius: 4px;">
                  <p style="margin: 0; font-size: 0.75em; font-weight: 600;">💡 Example:</p>
                  <p style="margin: 2px 0; font-size: 0.75em;">${currencyInfo.exchangeInfo.atmInfo.concreteExample}</p>
                </div>
              </div>
              `
                  : ''
              }
            `
                : `
              <div style="background: rgba(255,255,255,0.1); padding: 15px; border-radius: 8px;">
                <p style="margin: 5px 0;">Use ATMs at major banks for best rates</p>
                <p style="margin: 5px 0;">Keep receipts for currency exchange</p>
              </div>
            `
            }
          </div>

          <!-- Payment Methods -->
          <div style="background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%); color: white; padding: 20px; border-radius: 12px; box-shadow: 0 4px 8px rgba(0,0,0,0.1);">
            <h5 style="margin: 0 0 15px 0; font-size: 1.2em; display: flex; align-items: center;">
              <span style="margin-right: 10px; font-size: 1.5em;">💳</span> Payment Methods
            </h5>
            ${
              currencyInfo?.paymentMethods
                ? `
              ${
                currencyInfo.paymentMethods.creditCards
                  ? `
              <div style="background: rgba(255,255,255,0.1); padding: 15px; border-radius: 8px; margin-bottom: 15px;">
                <p style="margin: 0 0 8px 0; font-size: 0.9em; font-weight: 600;">💳 Credit Cards:</p>
                <p style="margin: 2px 0; font-size: 0.8em;">• Acceptance: ${currencyInfo.paymentMethods.creditCards.acceptance}</p>
                <p style="margin: 2px 0; font-size: 0.8em;">• Preferred: ${currencyInfo.paymentMethods.creditCards.preferredCards.join(', ')}</p>
                <p style="margin: 2px 0; font-size: 0.8em;">• Fees: ${currencyInfo.paymentMethods.creditCards.fees}</p>
              </div>
              `
                  : ''
              }
              ${
                currencyInfo.paymentMethods.cash
                  ? `
              <div style="background: rgba(255,255,255,0.1); padding: 15px; border-radius: 8px; margin-bottom: 15px;">
                <p style="margin: 0 0 8px 0; font-size: 0.9em; font-weight: 600;">💵 Cash:</p>
                <p style="margin: 2px 0; font-size: 0.8em;">• Importance: ${currencyInfo.paymentMethods.cash.importance}</p>
                <p style="margin: 2px 0; font-size: 0.8em;">• Recommendations: ${currencyInfo.paymentMethods.cash.recommendations}</p>
              </div>
              `
                  : ''
              }
              ${
                currencyInfo.paymentMethods.digitalPayments
                  ? `
              <div style="background: rgba(255,255,255,0.1); padding: 15px; border-radius: 8px;">
                <p style="margin: 0 0 8px 0; font-size: 0.9em; font-weight: 600;">📱 Digital Payments:</p>
                ${
                  currencyInfo.paymentMethods.digitalPayments.localApps?.length
                    ? `
                <p style="margin: 2px 0; font-size: 0.8em;">• Local Apps: ${currencyInfo.paymentMethods.digitalPayments.localApps.join(', ')}</p>
                `
                    : ''
                }
                <p style="margin: 2px 0; font-size: 0.8em;">• International Access: ${currencyInfo.paymentMethods.digitalPayments.internationalAccess}</p>
                <p style="margin: 2px 0; font-size: 0.8em;">• Setup Requirements: ${currencyInfo.paymentMethods.digitalPayments.accessibilityDetails}</p>
                ${
                  currencyInfo.paymentMethods.digitalPayments.modernSolutions
                    ?.length
                    ? `
                <p style="margin: 2px 0; font-size: 0.8em;">• Modern Solutions: ${currencyInfo.paymentMethods.digitalPayments.modernSolutions.join(', ')}</p>
                `
                    : ''
                }
                <p style="margin: 2px 0; font-size: 0.8em;">• QR Payments: ${currencyInfo.paymentMethods.digitalPayments.qrCodePayments}</p>
              </div>
              `
                  : ''
              }
              ${
                currencyInfo.paymentMethods.transportCards
                  ? `
              <div style="background: rgba(255,255,255,0.1); padding: 15px; border-radius: 8px;">
                <p style="margin: 0 0 8px 0; font-size: 0.9em; font-weight: 600;">🚌 Transport Cards:</p>
                <p style="margin: 2px 0; font-size: 0.8em;">• Available: ${currencyInfo.paymentMethods.transportCards.available.join(', ')}</p>
                <p style="margin: 2px 0; font-size: 0.8em;">• Tourist Access: ${currencyInfo.paymentMethods.transportCards.touristFriendly}</p>
                <p style="margin: 2px 0; font-size: 0.8em;">• Deposit/Refund: ${currencyInfo.paymentMethods.transportCards.depositRefund}</p>
                <p style="margin: 2px 0; font-size: 0.8em;">• Usage Areas: ${currencyInfo.paymentMethods.transportCards.usageAreas}</p>
                <p style="margin: 2px 0; font-size: 0.8em;">• 📍 Where to Buy: ${currencyInfo.paymentMethods.transportCards.purchaseLocations}</p>
                ${
                  currencyInfo.paymentMethods.transportCards.cardImages
                    ? `
                <div style="margin-top: 8px; padding: 8px; background: rgba(255,255,255,0.15); border-radius: 4px;">
                  <p style="margin: 0; font-size: 0.75em; font-weight: 600;">🎴 Card Images:</p>
                  <p style="margin: 2px 0; font-size: 0.75em;">${currencyInfo.paymentMethods.transportCards.cardImages}</p>
                </div>
                `
                    : ''
                }
              </div>
              `
                  : ''
              }
            `
                : `
              <div style="background: rgba(255,255,255,0.1); padding: 15px; border-radius: 8px;">
                <p style="margin: 5px 0;">Credit cards widely accepted</p>
                <p style="margin: 5px 0;">Carry some local cash for small purchases</p>
              </div>
            `
            }
          </div>

          <!-- Typical Costs & Tipping -->
          <div style="background: linear-gradient(135deg, #fa709a 0%, #fee140 100%); color: white; padding: 20px; border-radius: 12px; box-shadow: 0 4px 8px rgba(0,0,0,0.1);">
            <h5 style="margin: 0 0 15px 0; font-size: 1.2em; display: flex; align-items: center;">
              <span style="margin-right: 10px; font-size: 1.5em;">💰</span> Costs & Tipping
            </h5>
            ${
              currencyInfo?.typicalCosts || currencyInfo?.tipping
                ? `
              ${
                currencyInfo.typicalCosts
                  ? `
              <div style="background: rgba(255,255,255,0.1); padding: 15px; border-radius: 8px; margin-bottom: 15px;">
                <p style="margin: 0 0 10px 0; font-size: 0.9em; font-weight: 600;">💰 Typical Costs:</p>
                ${
                  currencyInfo.typicalCosts.meals
                    ? `
                <div style="margin-bottom: 8px;">
                  <p style="margin: 0; font-size: 0.8em; font-weight: 600;">🍽️ Meals:</p>
                  <p style="margin: 2px 0; font-size: 0.75em;">• Budget: ${currencyInfo.typicalCosts.meals.budget}</p>
                  <p style="margin: 2px 0; font-size: 0.75em;">• Mid-range: ${currencyInfo.typicalCosts.meals.midRange}</p>
                  <p style="margin: 2px 0; font-size: 0.75em;">• Upscale: ${currencyInfo.typicalCosts.meals.upscale}</p>
                </div>
                `
                    : ''
                }
                ${
                  currencyInfo.typicalCosts.transport
                    ? `
                <div style="margin-bottom: 8px;">
                  <p style="margin: 0; font-size: 0.8em; font-weight: 600;">🚌 Transport:</p>
                  <p style="margin: 2px 0; font-size: 0.75em;">• Public: ${currencyInfo.typicalCosts.transport.publicTransport}</p>
                  <p style="margin: 2px 0; font-size: 0.75em;">• Taxi: ${currencyInfo.typicalCosts.transport.taxi}</p>
                  <p style="margin: 2px 0; font-size: 0.75em;">• Rideshare: ${currencyInfo.typicalCosts.transport.rideshare}</p>
                </div>
                `
                    : ''
                }
                ${
                  currencyInfo.typicalCosts.accommodation
                    ? `
                <div>
                  <p style="margin: 0; font-size: 0.8em; font-weight: 600;">🏨 Accommodation:</p>
                  <p style="margin: 2px 0; font-size: 0.75em;">• Budget: ${currencyInfo.typicalCosts.accommodation.budget}</p>
                  <p style="margin: 2px 0; font-size: 0.75em;">• Mid-range: ${currencyInfo.typicalCosts.accommodation.midRange}</p>
                  <p style="margin: 2px 0; font-size: 0.75em;">• Luxury: ${currencyInfo.typicalCosts.accommodation.luxury}</p>
                </div>
                `
                    : ''
                }
              </div>
              `
                  : ''
              }
              ${
                currencyInfo.tipping
                  ? `
              <div style="background: rgba(255,255,255,0.1); padding: 15px; border-radius: 8px;">
                <p style="margin: 0 0 10px 0; font-size: 0.9em; font-weight: 600;">💡 Tipping Guide:</p>
                <p style="margin: 2px 0; font-size: 0.8em;">📖 Culture: ${currencyInfo.tipping.culture}</p>
                <p style="margin: 2px 0; font-size: 0.8em;">🍽️ Restaurants: ${currencyInfo.tipping.restaurants}</p>
                <p style="margin: 2px 0; font-size: 0.8em;">🚕 Taxis: ${currencyInfo.tipping.taxis}</p>
                <p style="margin: 2px 0; font-size: 0.8em;">🏨 Hotels: ${currencyInfo.tipping.hotels}</p>
                <p style="margin: 2px 0; font-size: 0.8em;">📋 Other: ${currencyInfo.tipping.other}</p>
              </div>
              `
                  : ''
              }
            `
                : `
              <div style="background: rgba(255,255,255,0.1); padding: 15px; border-radius: 8px;">
                <p style="margin: 5px 0;">• Budget for tips and service charges</p>
                <p style="margin: 5px 0;">• Keep emergency cash separate</p>
                <p style="margin: 5px 0;">• Use contactless payments when available</p>
              </div>
            `
            }
          </div>

          <!-- Safety & Precautions -->
          ${
            currencyInfo?.safetyAndPrecautions
              ? `
          <div style="background: linear-gradient(135deg, #ff9a9e 0%, #fecfef 100%); color: white; padding: 20px; border-radius: 12px; box-shadow: 0 4px 8px rgba(0,0,0,0.1);">
            <h5 style="margin: 0 0 15px 0; font-size: 1.2em; display: flex; align-items: center;">
              <span style="margin-right: 10px; font-size: 1.5em;">🔒</span> Safety & Precautions
            </h5>
            <div style="background: rgba(255,255,255,0.1); padding: 15px; border-radius: 8px; margin-bottom: 15px;">
              <p style="margin: 0 0 8px 0; font-size: 0.9em; font-weight: 600;">💡 Avoid Fees:</p>
              <p style="margin: 2px 0; font-size: 0.8em;">${currencyInfo.safetyAndPrecautions.avoidFees}</p>
            </div>
            <div style="background: rgba(255,255,255,0.1); padding: 15px; border-radius: 8px; margin-bottom: 15px;">
              <p style="margin: 0 0 8px 0; font-size: 0.9em; font-weight: 600;">📱 Card Activation:</p>
              <p style="margin: 2px 0; font-size: 0.8em;">${currencyInfo.safetyAndPrecautions.cardActivation}</p>
            </div>
            <div style="background: rgba(255,255,255,0.1); padding: 15px; border-radius: 8px; margin-bottom: 15px;">
              <p style="margin: 0 0 8px 0; font-size: 0.9em; font-weight: 600;">🛡️ Safety Tips:</p>
              <p style="margin: 2px 0; font-size: 0.8em;">${currencyInfo.safetyAndPrecautions.safetyTips}</p>
            </div>
            <div style="background: rgba(255,255,255,0.1); padding: 15px; border-radius: 8px; margin-bottom: 15px;">
              <p style="margin: 0 0 8px 0; font-size: 0.9em; font-weight: 600;">🔄 Backup Payments:</p>
              <p style="margin: 2px 0; font-size: 0.8em;">${currencyInfo.safetyAndPrecautions.backupPayments}</p>
            </div>
            ${
              currencyInfo.safetyAndPrecautions.localSafety
                ? `
            <div style="background: rgba(255,255,255,0.1); padding: 15px; border-radius: 8px;">
              <p style="margin: 0 0 8px 0; font-size: 0.9em; font-weight: 600;">🏛️ Local Safety:</p>
              <p style="margin: 2px 0; font-size: 0.8em;">🏧 ATM Safety: ${currencyInfo.safetyAndPrecautions.localSafety.atmSafety}</p>
              <p style="margin: 2px 0; font-size: 0.8em;">💰 Cash Limits: ${currencyInfo.safetyAndPrecautions.localSafety.cashLimits}</p>
              <p style="margin: 2px 0; font-size: 0.8em;">🛡️ Card Protection: ${currencyInfo.safetyAndPrecautions.localSafety.cardSkimming}</p>
              <p style="margin: 2px 0; font-size: 0.8em;">🆘 Emergency Access: ${currencyInfo.safetyAndPrecautions.localSafety.emergencyAccess}</p>
              ${
                currencyInfo.safetyAndPrecautions.localSafety.safeZones
                  ? `
              <p style="margin: 2px 0; font-size: 0.8em;">✅ Safe Zones: ${currencyInfo.safetyAndPrecautions.localSafety.safeZones}</p>
              `
                  : ''
              }
              ${
                currencyInfo.safetyAndPrecautions.localSafety.riskZones
                  ? `
              <p style="margin: 2px 0; font-size: 0.8em;">⚠️ Risk Areas: ${currencyInfo.safetyAndPrecautions.localSafety.riskZones}</p>
              `
                  : ''
              }
            </div>
            `
                : ''
            }
          </div>
          `
              : ''
          }

          <!-- Practical Tips -->
          ${
            currencyInfo?.practicalTips
              ? `
          <div style="background: linear-gradient(135deg, #a8edea 0%, #fed6e3 100%); color: #2c3e50; padding: 20px; border-radius: 12px; box-shadow: 0 4px 8px rgba(0,0,0,0.1);">
            <h5 style="margin: 0 0 15px 0; font-size: 1.2em; display: flex; align-items: center;">
              <span style="margin-right: 10px; font-size: 1.5em;">💡</span> Expert Tips
            </h5>
            <div style="background: rgba(255,255,255,0.3); padding: 15px; border-radius: 8px; margin-bottom: 15px;">
              <p style="margin: 0 0 8px 0; font-size: 0.9em; font-weight: 600;">🎯 Best Strategy:</p>
              <p style="margin: 2px 0; font-size: 0.8em;">${currencyInfo.practicalTips.bestStrategy}</p>
            </div>
            <div style="background: rgba(255,255,255,0.3); padding: 15px; border-radius: 8px; margin-bottom: 15px;">
              <p style="margin: 0 0 8px 0; font-size: 0.9em; font-weight: 600;">🚫 Avoid Tourist Traps:</p>
              <p style="margin: 2px 0; font-size: 0.8em;">${currencyInfo.practicalTips.avoidTouristTraps}</p>
            </div>
            <div style="background: rgba(255,255,255,0.3); padding: 15px; border-radius: 8px; margin-bottom: 15px;">
              <p style="margin: 0 0 8px 0; font-size: 0.9em; font-weight: 600;">⚠️ Special Considerations:</p>
              <p style="margin: 2px 0; font-size: 0.8em;">${currencyInfo.practicalTips.specialConsiderations}</p>
            </div>
            <div style="background: rgba(255,255,255,0.3); padding: 15px; border-radius: 8px;">
              <p style="margin: 0 0 8px 0; font-size: 0.9em; font-weight: 600;">💰 Cost Minimization:</p>
              <p style="margin: 2px 0; font-size: 0.8em;">${currencyInfo.practicalTips.costMinimization}</p>
            </div>
          </div>
          `
              : ''
          }

          <!-- Concrete Examples -->
          ${
            currencyInfo?.concreteExamples
              ? `
          <div style="background: linear-gradient(135deg, #ffecd2 0%, #fcb69f 100%); color: #2c3e50; padding: 20px; border-radius: 12px; box-shadow: 0 4px 8px rgba(0,0,0,0.1);">
            <h5 style="margin: 0 0 15px 0; font-size: 1.2em; display: flex; align-items: center;">
              <span style="margin-right: 10px; font-size: 1.5em;">🎯</span> Real Examples
            </h5>
            <div style="background: rgba(255,255,255,0.3); padding: 15px; border-radius: 8px; margin-bottom: 15px;">
              <p style="margin: 0 0 8px 0; font-size: 0.9em; font-weight: 600;">🏧 ATM Withdrawal:</p>
              <p style="margin: 2px 0; font-size: 0.8em;">${currencyInfo.concreteExamples.atmWithdrawal}</p>
            </div>
            <div style="background: rgba(255,255,255,0.3); padding: 15px; border-radius: 8px; margin-bottom: 15px;">
              <p style="margin: 0 0 8px 0; font-size: 0.9em; font-weight: 600;">🍽️ Restaurant Payment:</p>
              <p style="margin: 2px 0; font-size: 0.8em;">${currencyInfo.concreteExamples.restaurantPayment}</p>
            </div>
            <div style="background: rgba(255,255,255,0.3); padding: 15px; border-radius: 8px; margin-bottom: 15px;">
              <p style="margin: 0 0 8px 0; font-size: 0.9em; font-weight: 600;">🚌 Transport Payment:</p>
              <p style="margin: 2px 0; font-size: 0.8em;">${currencyInfo.concreteExamples.transportPayment}</p>
            </div>
            <div style="background: rgba(255,255,255,0.3); padding: 15px; border-radius: 8px; margin-bottom: 15px;">
              <p style="margin: 0 0 8px 0; font-size: 0.9em; font-weight: 600;">🛍️ Shopping Payment:</p>
              <p style="margin: 2px 0; font-size: 0.8em;">${currencyInfo.concreteExamples.shoppingPayment}</p>
            </div>
            <div style="background: rgba(255,255,255,0.3); padding: 15px; border-radius: 8px;">
              <p style="margin: 0 0 8px 0; font-size: 0.9em; font-weight: 600;">🏨 Hotel Payment:</p>
              <p style="margin: 2px 0; font-size: 0.8em;">${currencyInfo.concreteExamples.hotelPayment}</p>
            </div>
          </div>
          `
              : ''
          }

          <!-- Interactive Currency Map -->
          ${currencyInfo && destinationInfo ? generateInteractiveCurrencyMap(currencyInfo, destinationInfo.destination, destinationInfo.coordinates) : ''}
        </div>

        <!-- Important Notice -->
        <div style="background: linear-gradient(135deg, #ffecd2 0%, #fcb69f 100%); border: 1px solid #f39c12; border-radius: 8px; padding: 20px; margin-top: 20px;">
          <h5 style="margin: 0 0 10px 0; color: #d35400; display: flex; align-items: center;">
            <span style="margin-right: 10px; font-size: 1.3em;">⚠️</span> Important Financial Notes
          </h5>
          <p style="margin: 5px 0; color: #8b4513;">• Exchange rates fluctuate daily - check current rates before traveling</p>
          <p style="margin: 5px 0; color: #8b4513;">• Inform your bank about travel dates to avoid card blocks</p>
          <p style="margin: 5px 0; color: #8b4513;">• Keep digital and physical copies of important financial documents</p>
        </div>
      </div>
    </div>
  `;
}

/**
 * Generate standard tip content for non-money categories
 */
function generateStandardTipContent(
  tip: { category: string; tips: string[] },
  isActive = false,
): string {
  const categoryId = tip.category.toLowerCase().replace(/\s+/g, '-');

  return `
    <div id="${categoryId}" class="tab-panel" style="display: ${isActive ? 'block' : 'none'}; padding: 25px; background: white;">
      <div style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; padding: 20px; border-radius: 12px; margin-bottom: 20px;">
        <h4 style="margin: 0; font-size: 1.3em; display: flex; align-items: center;">
          <span style="margin-right: 10px; font-size: 1.4em;">${getCategoryIcon(tip.category)}</span> ${tip.category}
        </h4>
      </div>

      <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 15px;">
        ${tip.tips
          .map(
            (tipText, _index) => `
          <div style="background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%); padding: 20px; border-radius: 10px; border-left: 4px solid #667eea; box-shadow: 0 2px 4px rgba(0,0,0,0.1);">
            <p style="margin: 0; color: #2c3e50; line-height: 1.6; font-size: 0.95em;">
              <span style="color: #667eea; font-weight: 600; margin-right: 8px;">•</span>${tipText}
            </p>
          </div>
        `,
          )
          .join('')}
      </div>
    </div>
  `;
}

/**
 * Get appropriate icon for category
 */
function getCategoryIcon(category: string): string {
  const categoryLower = category.toLowerCase();
  if (categoryLower.includes('transport')) return '🚌';
  if (categoryLower.includes('safety')) return '🛡️';
  if (categoryLower.includes('culture')) return '🎭';
  if (categoryLower.includes('connect')) return '📶';
  if (categoryLower.includes('food') || categoryLower.includes('dining'))
    return '🍽️';
  if (categoryLower.includes('weather')) return '🌤️';
  if (categoryLower.includes('health')) return '🏥';
  if (categoryLower.includes('shopping')) return '🛍️';
  return '📋';
}

/**
 * Extract currency information from tips
 */
function extractCurrencyInfo(tips: string[]): string[] {
  return tips.filter(
    (tip) =>
      tip.toLowerCase().includes('currency') ||
      tip.toLowerCase().includes('dollar') ||
      tip.toLowerCase().includes('euro') ||
      tip.toLowerCase().includes('pound') ||
      tip.toLowerCase().includes('yen') ||
      tip.toLowerCase().includes('exchange rate'),
  );
}

/**
 * Extract exchange information from tips
 */
function extractExchangeInfo(tips: string[]): string[] {
  return tips.filter(
    (tip) =>
      tip.toLowerCase().includes('exchange') ||
      tip.toLowerCase().includes('atm') ||
      tip.toLowerCase().includes('bank'),
  );
}

/**
 * Extract payment information from tips
 */
function extractPaymentInfo(tips: string[]): string[] {
  return tips.filter(
    (tip) =>
      tip.toLowerCase().includes('payment') ||
      tip.toLowerCase().includes('card') ||
      tip.toLowerCase().includes('cash') ||
      tip.toLowerCase().includes('credit') ||
      tip.toLowerCase().includes('debit'),
  );
}

/**
 * Extract banking information from tips
 */
function extractBankingInfo(tips: string[]): string[] {
  return tips.filter(
    (tip) =>
      tip.toLowerCase().includes('banking') ||
      tip.toLowerCase().includes('account') ||
      tip.toLowerCase().includes('fee'),
  );
}

/**
 * Get image URL using Serper API for hotels/accommodations
 */
async function getHotelImageUrl(
  hotelName: string,
  destination?: string,
): Promise<string> {
  try {
    const searchQuery = destination
      ? `${hotelName} hotel ${destination}`
      : `${hotelName} hotel accommodation`;

    const apiKey = process.env.SERPER_API as string;
    if (!apiKey) {
      console.warn('Serper API key not found, using fallback image');
      return getFallbackImage('hotel', hotelName);
    }

    const url = `https://google.serper.dev/images?q=${encodeURIComponent(searchQuery)}`;

    const response = await fetch(url, {
      method: 'GET',
      headers: {
        'X-API-KEY': apiKey,
        'Content-Type': 'application/json',
      },
      redirect: 'follow',
    });

    if (!response.ok) {
      throw new Error(`Serper API error: ${response.status}`);
    }

    const data = await response.json();

    if (data.images && data.images.length > 0) {
      // Filter for high-quality hotel images
      const goodImages = data.images.filter(
        (img: any) =>
          img.imageUrl &&
          !img.imageUrl.includes('icon') &&
          !img.imageUrl.includes('thumb') &&
          (img.imageUrl.includes('hotel') ||
            img.imageUrl.includes('accommodation') ||
            img.imageUrl.includes('resort')),
      );

      if (goodImages.length > 0) {
        return goodImages[0].imageUrl;
      } else if (data.images.length > 0) {
        return data.images[0].imageUrl;
      }
    }
  } catch (error) {
    console.error('Error fetching hotel image from Serper:', error);
  }

  // Fallback to SVG placeholder
  return getFallbackImage('hotel', hotelName);
}

/**
 * Get image URL using Serper API for restaurants
 */
async function getRestaurantImageUrl(
  restaurantName: string,
  destination?: string,
): Promise<string> {
  try {
    const searchQuery = destination
      ? `${restaurantName} restaurant ${destination}`
      : `${restaurantName} restaurant`;

    const apiKey = process.env.SERPER_API as string;
    if (!apiKey) {
      console.warn('Serper API key not found, using fallback image');
      return getFallbackImage('restaurant', restaurantName);
    }

    const url = `https://google.serper.dev/images?q=${encodeURIComponent(searchQuery)}`;

    const response = await fetch(url, {
      method: 'GET',
      headers: {
        'X-API-KEY': apiKey,
        'Content-Type': 'application/json',
      },
      redirect: 'follow',
    });

    if (!response.ok) {
      throw new Error(`Serper API error: ${response.status}`);
    }

    const data = await response.json();

    if (data.images && data.images.length > 0) {
      // Filter for high-quality restaurant images
      const goodImages = data.images.filter(
        (img: any) =>
          img.imageUrl &&
          !img.imageUrl.includes('icon') &&
          !img.imageUrl.includes('thumb') &&
          (img.imageUrl.includes('restaurant') ||
            img.imageUrl.includes('food') ||
            img.imageUrl.includes('dining')),
      );

      if (goodImages.length > 0) {
        return goodImages[0].imageUrl;
      } else if (data.images.length > 0) {
        return data.images[0].imageUrl;
      }
    }
  } catch (error) {
    console.error('Error fetching restaurant image from Serper:', error);
  }

  // Fallback to SVG placeholder
  return getFallbackImage('restaurant', restaurantName);
}

/**
 * Get image URL using Serper API for POI/attractions
 */
async function getPoiImageUrl(
  poiName: string,
  destination?: string,
): Promise<string> {
  try {
    const searchQuery = destination
      ? `${poiName} ${destination} attraction tourist`
      : `${poiName} attraction tourist`;

    const apiKey = process.env.SERPER_API as string;
    if (!apiKey) {
      console.warn('Serper API key not found, using fallback image');
      return getFallbackImage('attraction', poiName);
    }

    const url = `https://google.serper.dev/images?q=${encodeURIComponent(searchQuery)}`;

    const response = await fetch(url, {
      method: 'GET',
      headers: {
        'X-API-KEY': apiKey,
        'Content-Type': 'application/json',
      },
      redirect: 'follow',
    });

    if (!response.ok) {
      throw new Error(`Serper API error: ${response.status}`);
    }

    const data = await response.json();

    if (data.images && data.images.length > 0) {
      // Filter for high-quality attraction images
      const goodImages = data.images.filter(
        (img: any) =>
          img.imageUrl &&
          !img.imageUrl.includes('icon') &&
          !img.imageUrl.includes('thumb') &&
          (img.imageUrl.includes('attraction') ||
            img.imageUrl.includes('tourist') ||
            img.imageUrl.includes('landmark')),
      );

      if (goodImages.length > 0) {
        return goodImages[0].imageUrl;
      } else if (data.images.length > 0) {
        return data.images[0].imageUrl;
      }
    }
  } catch (error) {
    console.error('Error fetching POI image from Serper:', error);
  }

  // Fallback to SVG placeholder
  return getFallbackImage('attraction', poiName);
}

/**
 * Interface for enriched activity data
 */
interface EnrichedActivityData {
  imageUrl: string;
  phoneNumber?: string;
  website?: string;
  rating?: string;
  priceRange?: string;
  openingHours?: string;
}

/**
 * Get enriched data for an activity using Serper Maps API
 */
async function getActivityEnrichedData(
  activityName: string,
  location: string,
  destination: string,
): Promise<EnrichedActivityData> {
  console.log(`🔍 Enriching data for activity: ${activityName} at ${location}`);

  const defaultData: EnrichedActivityData = {
    imageUrl: getFallbackImage('attraction', activityName),
  };

  try {
    const apiKey = process.env.SERPER_API as string;
    if (!apiKey) {
      console.warn('Serper API key not found, using default data');
      return defaultData;
    }

    // Use Serper Maps API for more detailed information
    const searchQuery = `${activityName} ${location} ${destination}`;
    const mapsUrl = `https://google.serper.dev/maps?q=${encodeURIComponent(searchQuery)}&apiKey=${apiKey}`;

    console.log(`[Serper Maps] Searching for: "${searchQuery}"`);

    const mapsResponse = await fetch(mapsUrl, {
      method: 'GET',
      redirect: 'follow',
    });

    if (!mapsResponse.ok) {
      throw new Error(`Serper Maps API error: ${mapsResponse.status}`);
    }

    const mapsData = await mapsResponse.json();
    console.log(`[Serper Maps] Found ${mapsData.places?.length || 0} places`);

    // Extract information from the first place result
    let phoneNumber: string | undefined;
    let website: string | undefined;
    let rating: string | undefined;
    let priceRange: string | undefined;
    let openingHours: string | undefined;
    let imageUrl = defaultData.imageUrl;

    if (mapsData.places && mapsData.places.length > 0) {
      const place = mapsData.places[0]; // Take the first (most relevant) result

      console.log(`[Serper Maps] Processing place: "${place.title}"`);

      // Extract structured data from Maps API
      phoneNumber = place.phoneNumber;
      website = place.website;
      priceRange = place.priceLevel;

      // Format rating with stars
      if (place.rating && place.ratingCount) {
        const stars = '⭐'.repeat(Math.floor(place.rating));
        rating = `${stars} ${place.rating}/5 (${place.ratingCount} avis)`;
      } else if (place.rating) {
        const stars = '⭐'.repeat(Math.floor(place.rating));
        rating = `${stars} ${place.rating}/5`;
      }

      // Format opening hours
      if (place.openingHours) {
        const today = new Date().toLocaleDateString('en-US', {
          weekday: 'long',
        });
        const todayHours = place.openingHours[today];
        if (todayHours) {
          openingHours = `Aujourd'hui: ${todayHours}`;
        } else {
          // Get first available day's hours
          const firstDay = Object.keys(place.openingHours)[0];
          if (firstDay && place.openingHours[firstDay]) {
            openingHours = `${firstDay}: ${place.openingHours[firstDay]}`;
          }
        }
      }

      // Use thumbnail from Google Maps if available
      if (place.thumbnailUrl) {
        imageUrl = place.thumbnailUrl;
      } else {
        // Fallback to our image search
        try {
          imageUrl = await getPoiImageUrl(activityName, destination);
        } catch (imageError) {
          console.error('Error fetching activity image:', imageError);
        }
      }

      console.log(`[Serper Maps] Extracted data:`, {
        phoneNumber,
        website,
        rating,
        priceRange,
        openingHours,
        hasImage: !!imageUrl,
      });
    } else {
      console.log(`[Serper Maps] No places found, trying fallback image`);
      // Fallback to image search if no places found
      try {
        imageUrl = await getPoiImageUrl(activityName, destination);
      } catch (imageError) {
        console.error('Error fetching activity image:', imageError);
      }
    }

    const enrichedData: EnrichedActivityData = {
      imageUrl,
      phoneNumber,
      website,
      rating,
      priceRange,
      openingHours,
    };

    console.log(`✅ Enriched data for ${activityName}:`, enrichedData);
    return enrichedData;
  } catch (error) {
    console.error(`Error enriching data for ${activityName}:`, error);
    return defaultData;
  }
}

/**
 * Enrich all activities in a trip plan with Serper data
 */
async function enrichTripPlanActivities(
  tripPlan: any,
  destination: string,
): Promise<any> {
  console.log(`🚀 Starting to enrich activities for ${destination}`);

  const enrichedDays = await Promise.all(
    tripPlan.days.map(async (day: any) => {
      console.log(`📅 Enriching activities for Day ${day.day}`);

      const enrichedActivities = await Promise.all(
        day.activities.map(async (activity: any) => {
          try {
            const enrichedData = await getActivityEnrichedData(
              activity.activity,
              activity.location,
              destination,
            );

            return {
              ...activity,
              enrichedData,
            };
          } catch (error) {
            console.error(
              `Error enriching activity ${activity.activity}:`,
              error,
            );
            return {
              ...activity,
              enrichedData: {
                imageUrl: getFallbackImage('attraction', activity.activity),
              },
            };
          }
        }),
      );

      return {
        ...day,
        activities: enrichedActivities,
      };
    }),
  );

  console.log(`✅ Finished enriching activities for ${destination}`);

  return {
    ...tripPlan,
    days: enrichedDays,
  };
}

/**
 * Create sophisticated local recommendations carousel
 */
async function createLocalRecommendationsCarousel(
  items: Array<{
    name: string;
    description: string;
    extra?: string;
    imageUrl?: string;
    category?: string;
    rating?: string;
    type?: string;
  }>,
  destination?: string,
): Promise<string> {
  console.log(
    `🎨 Creating sophisticated local recommendations carousel with ${items.length} items`,
  );

  if (!items || items.length === 0) {
    return `<div class="text-center p-8 text-gray-500">No local recommendations available</div>`;
  }

  // Transform items to experiences format
  const experiences = await Promise.all(
    items.map(async (item, index) => {
      let imageUrl = item.imageUrl;
      if (!imageUrl) {
        try {
          imageUrl = await getPoiImageUrl(item.name, destination);
        } catch (error) {
          console.error('Error getting POI image:', error);
          imageUrl = getFallbackImage('attraction', item.name);
        }
      }
      return {
        id: index + 1,
        title: item.name,
        subtitle: item.extra || 'Local Experience',
        category: item.category || 'Local Attraction',
        description: item.description,
        optimalVisit: item.extra || 'Year-round • Check local hours',
        image: imageUrl,
        location: destination || 'Local Area',
      };
    }),
  );

  // Helper functions to create HTML
  const createExperienceCardHtml = (experience: any, _featured = false) => `
    <div style="position: relative; overflow: hidden; border-radius: 16px; background: linear-gradient(to bottom, #1f2937, #000000); border: 1px solid rgba(251, 191, 36, 0.2); box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.8); height: 500px; transition: all 0.3s ease;">
      <!-- Image -->
      <div style="position: relative; overflow: hidden; height: 70%;">
        <div
          style="position: absolute; inset: 0; background-image: url('${experience.image}'); background-size: cover; background-position: center; transition: transform 0.7s ease;"
        ></div>
        <div style="position: absolute; inset: 0; background: linear-gradient(to top, rgba(0,0,0,0.8) 0%, rgba(0,0,0,0.2) 50%, transparent 100%);"></div>

        <!-- Category badge -->
        <div style="position: absolute; top: 16px; left: 16px;">
          <span style="display: inline-block; padding: 6px 12px; background: rgba(0,0,0,0.6); backdrop-filter: blur(8px); border: 1px solid rgba(251, 191, 36, 0.5); color: #fbbf24; font-size: 11px; letter-spacing: 2px; text-transform: uppercase; font-weight: 300; border-radius: 20px;">
            ${experience.category}
          </span>
        </div>

        <!-- Location -->
        <div style="position: absolute; top: 16px; right: 16px;">
          <div style="display: flex; align-items: center; gap: 4px; color: rgba(255,255,255,0.8); font-size: 14px;">
            <svg style="width: 16px; height: 16px;" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z"></path>
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 11a3 3 0 11-6 0 3 3 0 016 0z"></path>
            </svg>
            <span>${experience.location}</span>
          </div>
        </div>
      </div>

      <!-- Content -->
      <div style="padding: 24px; height: 30%; display: flex; flex-direction: column; justify-content: space-between;">
        <div>
          <h3 style="font-size: 28px; font-weight: 300; color: white; margin: 0 0 8px 0; letter-spacing: -0.5px; line-height: 1.2;">
            ${experience.title}
          </h3>
          <p style="font-size: 16px; color: #fbbf24; font-weight: 300; font-style: italic; margin: 0 0 12px 0;">${experience.subtitle}</p>
          <p style="color: #d1d5db; font-size: 14px; line-height: 1.6; margin: 0; display: -webkit-box; -webkit-line-clamp: 2; -webkit-box-orient: vertical; overflow: hidden;">${experience.description}</p>
        </div>

        <!-- Optimal visit time -->
        <div style="display: flex; align-items: center; gap: 8px; color: rgba(251, 191, 36, 0.8); font-size: 12px; margin-top: 16px;">
          <svg style="width: 16px; height: 16px; flex-shrink: 0;" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <circle cx="12" cy="12" r="10"></circle>
            <polyline points="12,6 12,12 16,14"></polyline>
          </svg>
          <span style="font-size: 14px; line-height: 1.3;">${experience.optimalVisit}</span>
        </div>
      </div>
    </div>
  `;

  const createMiniExperienceCardHtml = (experience: any, _index: number) => `
    <div
      style="position: relative; overflow: hidden; border-radius: 12px; background: linear-gradient(to bottom, #1f2937, #000000); border: 1px solid rgba(251, 191, 36, 0.2); box-shadow: 0 10px 25px -5px rgba(0, 0, 0, 0.5); transition: all 0.3s ease; cursor: pointer; height: 140px; margin-bottom: 16px;"
      onclick="goToSlide(${experiences.findIndex((exp) => exp.id === experience.id)})"
      onmouseover="this.style.transform='translateY(-4px)'; this.style.borderColor='rgba(251, 191, 36, 0.4)'; this.style.boxShadow='0 20px 40px -10px rgba(251, 191, 36, 0.2)';"
      onmouseout="this.style.transform='translateY(0)'; this.style.borderColor='rgba(251, 191, 36, 0.2)'; this.style.boxShadow='0 10px 25px -5px rgba(0, 0, 0, 0.5)';"
    >
      <!-- Image -->
      <div style="position: relative; overflow: hidden; height: 60%;">
        <div
          style="position: absolute; inset: 0; background-image: url('${experience.image}'); background-size: cover; background-position: center; transition: transform 0.5s ease;"
        ></div>
        <div style="position: absolute; inset: 0; background: linear-gradient(to top, rgba(0,0,0,0.6) 0%, transparent 100%);"></div>

        <!-- Category badge -->
        <div style="position: absolute; top: 8px; left: 8px;">
          <span style="display: inline-block; padding: 4px 8px; background: rgba(0,0,0,0.6); backdrop-filter: blur(8px); border: 1px solid rgba(251, 191, 36, 0.5); color: #fbbf24; font-size: 10px; letter-spacing: 1px; text-transform: uppercase; font-weight: 300; border-radius: 16px;">
            ${experience.category}
          </span>
        </div>
      </div>

      <!-- Content -->
      <div style="padding: 12px; height: 40%; display: flex; flex-direction: column; justify-content: space-between;">
        <div>
          <h4 style="color: white; font-size: 14px; font-weight: 300; margin: 0 0 4px 0; display: -webkit-box; -webkit-line-clamp: 1; -webkit-box-orient: vertical; overflow: hidden;">${experience.title}</h4>
          <p style="color: rgba(251, 191, 36, 0.8); font-size: 12px; font-style: italic; margin: 0; display: -webkit-box; -webkit-line-clamp: 1; -webkit-box-orient: vertical; overflow: hidden;">${experience.subtitle}</p>
        </div>

        <div style="display: flex; align-items: center; gap: 4px; color: rgba(251, 191, 36, 0.6); font-size: 10px;">
          <svg style="width: 12px; height: 12px; flex-shrink: 0;" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <circle cx="12" cy="12" r="10"></circle>
            <polyline points="12,6 12,12 16,14"></polyline>
          </svg>
          <span style="font-size: 11px; line-height: 1.3; display: -webkit-box; -webkit-line-clamp: 1; -webkit-box-orient: vertical; overflow: hidden;">${experience.optimalVisit}</span>
        </div>
      </div>
    </div>
  `;

  return `
    <style>
      .line-clamp-1 {
        overflow: hidden;
        display: -webkit-box;
        -webkit-box-orient: vertical;
        -webkit-line-clamp: 1;
      }
      .line-clamp-3 {
        overflow: hidden;
        display: -webkit-box;
        -webkit-box-orient: vertical;
        -webkit-line-clamp: 3;
      }
      .scrollbar-hide {
        -ms-overflow-style: none;
        scrollbar-width: none;
      }
      .scrollbar-hide::-webkit-scrollbar {
        display: none;
      }

      /* Force Scrollbar to Always Show */
      #modal-content {
        overflow-y: scroll !important;
        overflow-x: hidden !important;
        scrollbar-width: auto !important;
        scrollbar-color: #D1D5DB #374151 !important;
        /* Force minimum height to trigger scrollbar */
        max-height: calc(90vh - 120px) !important;
      }

      /* Webkit browsers (Chrome, Safari, Edge) */
      #modal-content::-webkit-scrollbar {
        width: 16px !important;
        background: #374151 !important;
      }

      #modal-content::-webkit-scrollbar-track {
        background: #374151 !important;
        border-radius: 10px;
        border: 1px solid #4B5563;
      }

      #modal-content::-webkit-scrollbar-thumb {
        background: #D1D5DB !important;
        border-radius: 10px;
        border: 2px solid #374151;
        min-height: 50px !important;
      }

      #modal-content::-webkit-scrollbar-thumb:hover {
        background: #F3F4F6 !important;
      }

      #modal-content::-webkit-scrollbar-thumb:active {
        background: #FFFFFF !important;
      }

      #modal-content::-webkit-scrollbar-corner {
        background: #374151 !important;
      }

      /* Force scrollbar visibility even with short content */
      #modal-content::after {
        content: '';
        display: block;
        height: 1px;
        width: 100%;
      }
    </style>
    <div style="position: relative; background: white; padding: 32px 16px;">
      <!-- Header -->
      <div style="text-align: center; margin-bottom: 24px;">
        <h2 style="font-size: 28px; color: #2c3e50; font-weight: 600; margin: 0 0 8px 0; display: flex; align-items: center; justify-content: center; gap: 12px;">
          <div class="local-recommendations-logo-container" style="position: relative; display: inline-block; cursor: pointer;">
            <div class="local-recommendations-logo-glow" style="position: absolute; inset: -4px; opacity: 0.7; filter: blur(2px); border-radius: 12px; background: linear-gradient(135deg, #667eea, #764ba2, #5a67d8); transition: all 0.3s ease;"></div>
            <div class="local-recommendations-logo-main" style="position: relative; background: linear-gradient(135deg, #1e293b, #334155); border-radius: 12px; border: 1px solid #475569; box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25); transition: all 0.3s ease;">
              <div style="position: absolute; inset: 0; background: linear-gradient(135deg, #667eea, #764ba2, #5a67d8); opacity: 0.1; border-radius: 12px;"></div>
              <div style="position: relative; z-index: 10; background: linear-gradient(135deg, #e0e7ff, #c7d2fe); padding: 8px; border-radius: 8px; box-shadow: inset 0 2px 4px 0 rgba(0, 0, 0, 0.06);">
                <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="#5a67d8" stroke-width="2.5" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-map-pin" style="display: block; margin: 0 auto;">
                  <path d="M20 10c0 6-8 12-8 12s-8-6-8-12a8 8 0 0 1 16 0Z"/>
                  <circle cx="12" cy="10" r="3"/>
                </svg>
              </div>
              <svg class="local-recommendations-sparkles" xmlns="http://www.w3.org/2000/svg" width="12" height="12" viewBox="0 0 24 24" fill="none" stroke="#fbbf24" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" style="position: absolute; top: 4px; right: 4px; opacity: 0; animation: sparkle 1.5s ease-in-out infinite;">
                <path d="M9.937 15.5A2 2 0 0 0 8.5 14.063l-6.135-1.582a.5.5 0 0 1 0-.962L8.5 9.936A2 2 0 0 0 9.937 8.5l1.582-6.135a.5.5 0 0 1 .963 0L14.063 8.5A2 2 0 0 0 15.5 9.937l6.135 1.582a.5.5 0 0 1 0 .962L15.5 14.063a2 2 0 0 0-1.437 1.437l-1.582 6.135a.5.5 0 0 1-.963 0z"/>
              </svg>
            </div>
          </div>
          Local Recommendations
        </h2>
        <p style="font-size: 16px; color: #667eea; font-style: italic; font-weight: 400; margin: 0;">
          Discover the refined essence of ${destination || 'this destination'} • ${experiences.length} Exclusive Destinations
        </p>
      </div>

      <div style="max-width: 1200px; margin: 0 auto;">
        <div style="display: flex; gap: 24px; align-items: flex-start;">
          <!-- Main featured card - Left side -->
          <div style="flex: 1; position: relative;">
            <div id="main-experience-card">
              ${createExperienceCardHtml(experiences[0], true)}
            </div>

            <!-- Main navigation buttons -->
            <button
              onclick="goToPrevious()"
              style="position: absolute; left: 16px; top: 50%; transform: translateY(-50%); z-index: 30; background: rgba(255,255,255,0.9); color: #667eea; width: 56px; height: 56px; border-radius: 50%; display: flex; align-items: center; justify-content: center; backdrop-filter: blur(8px); border: 1px solid #667eea; transition: all 0.3s ease; box-shadow: 0 4px 12px rgba(102, 126, 234, 0.15); cursor: pointer;"
              onmouseover="this.style.background='#667eea'; this.style.color='white';"
              onmouseout="this.style.background='rgba(255,255,255,0.9)'; this.style.color='#667eea';"
              aria-label="Previous experience"
            >
              <svg style="width: 24px; height: 24px;" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7"></path>
              </svg>
            </button>
            <button
              onclick="goToNext()"
              style="position: absolute; right: 16px; top: 50%; transform: translateY(-50%); z-index: 30; background: rgba(255,255,255,0.9); color: #667eea; width: 56px; height: 56px; border-radius: 50%; display: flex; align-items: center; justify-content: center; backdrop-filter: blur(8px); border: 1px solid #667eea; transition: all 0.3s ease; box-shadow: 0 4px 12px rgba(102, 126, 234, 0.15); cursor: pointer;"
              onmouseover="this.style.background='#667eea'; this.style.color='white';"
              onmouseout="this.style.background='rgba(255,255,255,0.9)'; this.style.color='#667eea';"
              aria-label="Next experience"
            >
              <svg style="width: 24px; height: 24px;" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
              </svg>
            </button>
          </div>

          <!-- Vertical carousel - Right side -->
          <div style="width: 320px; position: relative;">
            <!-- Scroll Up Button -->
            <button
              onclick="scrollUp()"
              id="scroll-up-btn"
              style="position: absolute; top: 16px; left: 50%; transform: translateX(-50%); z-index: 20; background: rgba(255,255,255,0.9); color: #667eea; width: 40px; height: 40px; border-radius: 50%; display: flex; align-items: center; justify-content: center; backdrop-filter: blur(8px); border: 1px solid #667eea; transition: all 0.3s ease; box-shadow: 0 4px 12px rgba(102, 126, 234, 0.15); cursor: pointer;"
              onmouseover="this.style.background='#667eea'; this.style.color='white';"
              onmouseout="this.style.background='rgba(255,255,255,0.9)'; this.style.color='#667eea';"
              aria-label="Scroll up"
            >
              <svg style="width: 20px; height: 20px;" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 15l7-7 7 7"></path>
              </svg>
            </button>

            <!-- Cards container -->
            <div
              id="vertical-carousel-container"
              style="position: relative; height: 500px; overflow: hidden; border-radius: 16px; cursor: grab; user-select: none;"
              onmousedown="handleMouseDown(event)"
              ontouchstart="handleTouchStart(event)"
              onwheel="handleWheel(event)"
            >
              <div
                id="vertical-carousel-track"
                style="position: absolute; width: 100%; transition: transform 0.3s ease-out; transform: translateY(0px);"
              >
                ${experiences
                  .slice(1)
                  .map(
                    (exp, index) => `
                  <div>
                    ${createMiniExperienceCardHtml(exp, index + 1)}
                  </div>
                `,
                  )
                  .join('')}
              </div>
            </div>

            <!-- Scroll Down Button -->
            <button
              onclick="scrollDown()"
              id="scroll-down-btn"
              style="position: absolute; bottom: 16px; left: 50%; transform: translateX(-50%); z-index: 20; background: rgba(255,255,255,0.9); color: #667eea; width: 40px; height: 40px; border-radius: 50%; display: flex; align-items: center; justify-content: center; backdrop-filter: blur(8px); border: 1px solid #667eea; transition: all 0.3s ease; box-shadow: 0 4px 12px rgba(102, 126, 234, 0.15); cursor: pointer;"
              onmouseover="this.style.background='#667eea'; this.style.color='white';"
              onmouseout="this.style.background='rgba(255,255,255,0.9)'; this.style.color='#667eea';"
              aria-label="Scroll down"
            >
              <svg style="width: 20px; height: 20px;" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
              </svg>
            </button>
          </div>
        </div>

        <!-- Indicators -->
        <div style="display: flex; justify-content: center; gap: 8px; margin-top: 24px; flex-wrap: wrap;" id="indicators">
          ${experiences
            .map(
              (_, index) => `
            <button
              onclick="goToSlide(${index})"
              style="width: 32px; height: 4px; transition: all 0.3s ease; border-radius: 20px; border: none; cursor: pointer; ${index === 0 ? 'background: #667eea; box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);' : 'background: #e5e7eb;'}"
              onmouseover="if (${index} !== currentIndex) this.style.background='#9ca3af';"
              onmouseout="if (${index} !== currentIndex) this.style.background='#e5e7eb';"
              aria-label="Go to slide ${index + 1}"
              data-index="${index}"
            ></button>
          `,
            )
            .join('')}
        </div>
      </div>

      <script>
        // Helper functions
        function createExperienceCardHtml(experience, featured = false) {
          return \`
            <div style="position: relative; overflow: hidden; border-radius: 16px; background: white; border: 1px solid #e5e7eb; box-shadow: 0 10px 25px -5px rgba(0, 0, 0, 0.1); height: 500px; transition: all 0.3s ease;">
              <!-- Image -->
              <div style="position: relative; overflow: hidden; height: 70%;">
                <div
                  style="position: absolute; inset: 0; background-image: url('\${experience.image}'); background-size: cover; background-position: center; transition: transform 0.7s ease;"
                ></div>
                <div style="position: absolute; inset: 0; background: linear-gradient(to top, rgba(0,0,0,0.6) 0%, rgba(0,0,0,0.1) 50%, transparent 100%);"></div>

                <!-- Category badge -->
                <div style="position: absolute; top: 16px; left: 16px;">
                  <span style="display: inline-block; padding: 6px 12px; background: rgba(255,255,255,0.9); backdrop-filter: blur(8px); border: 1px solid #667eea; color: #667eea; font-size: 11px; letter-spacing: 2px; text-transform: uppercase; font-weight: 500; border-radius: 20px;">
                    \${experience.category}
                  </span>
                </div>

                <!-- Location -->
                <div style="position: absolute; top: 16px; right: 16px;">
                  <div style="display: flex; align-items: center; gap: 4px; color: white; font-size: 14px; background: rgba(0,0,0,0.5); padding: 4px 8px; border-radius: 12px; backdrop-filter: blur(8px);">
                    <svg style="width: 16px; height: 16px;" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z"></path>
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 11a3 3 0 11-6 0 3 3 0 016 0z"></path>
                    </svg>
                    <span>\${experience.location}</span>
                  </div>
                </div>
              </div>

              <!-- Content -->
              <div style="padding: 24px; height: 30%; display: flex; flex-direction: column; justify-content: space-between; background: white;">
                <div style="flex: 1; display: flex; flex-direction: column;">
                  <h3 style="font-size: 24px; font-weight: 600; color: #2c3e50; margin: 0 0 6px 0; letter-spacing: -0.5px; line-height: 1.2; display: -webkit-box; -webkit-line-clamp: 2; -webkit-box-orient: vertical; overflow: hidden;">
                    \${experience.title}
                  </h3>
                  <p style="font-size: 14px; color: #667eea; font-weight: 500; font-style: italic; margin: 0 0 8px 0; display: -webkit-box; -webkit-line-clamp: 1; -webkit-box-orient: vertical; overflow: hidden;">\${experience.subtitle}</p>
                  <p style="color: #6b7280; font-size: 13px; line-height: 1.5; margin: 0; flex: 1; display: -webkit-box; -webkit-line-clamp: 3; -webkit-box-orient: vertical; overflow: hidden;">\${experience.description}</p>
                </div>

                <!-- Optimal visit time -->
                <div style="display: flex; align-items: center; gap: 8px; color: #667eea; font-size: 12px; margin-top: 12px; padding-top: 8px; border-top: 1px solid #f3f4f6;">
                  <svg style="width: 14px; height: 14px; flex-shrink: 0;" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <circle cx="12" cy="12" r="10"></circle>
                    <polyline points="12,6 12,12 16,14"></polyline>
                  </svg>
                  <span style="font-size: 12px; line-height: 1.3; font-weight: 500; display: -webkit-box; -webkit-line-clamp: 1; -webkit-box-orient: vertical; overflow: hidden;">\${experience.optimalVisit}</span>
                </div>
              </div>
            </div>
          \`;
        }

        function createMiniExperienceCardHtml(experience, index) {
          return \`
            <div
              style="position: relative; overflow: hidden; border-radius: 12px; background: white; border: 1px solid #e5e7eb; box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1); transition: all 0.3s ease; cursor: pointer; height: 140px; margin-bottom: 16px;"
              onclick="goToSlide(\${experiences.findIndex(exp => exp.id === experience.id)})"
              onmouseover="this.style.transform='translateY(-4px)'; this.style.borderColor='#667eea'; this.style.boxShadow='0 10px 25px -5px rgba(102, 126, 234, 0.25)';"
              onmouseout="this.style.transform='translateY(0)'; this.style.borderColor='#e5e7eb'; this.style.boxShadow='0 4px 6px -1px rgba(0, 0, 0, 0.1)';"
            >
              <!-- Image -->
              <div style="position: relative; overflow: hidden; height: 60%; border-radius: 12px 12px 0 0;">
                <div
                  style="position: absolute; inset: 0; background-image: url('\${experience.image}'); background-size: cover; background-position: center; transition: transform 0.5s ease;"
                ></div>
                <div style="position: absolute; bottom: 0; left: 0; right: 0; height: 30%; background: linear-gradient(to top, rgba(0,0,0,0.3) 0%, transparent 100%);"></div>

                <!-- Category badge -->
                <div style="position: absolute; top: 8px; left: 8px;">
                  <span style="display: inline-block; padding: 4px 8px; background: rgba(255,255,255,0.9); backdrop-filter: blur(8px); border: 1px solid #667eea; color: #667eea; font-size: 10px; letter-spacing: 1px; text-transform: uppercase; font-weight: 500; border-radius: 16px;">
                    \${experience.category}
                  </span>
                </div>
              </div>

              <!-- Content -->
              <div style="padding: 12px; height: 40%; display: flex; flex-direction: column; justify-content: space-between; background: white;">
                <div>
                  <h4 style="color: #2c3e50; font-size: 14px; font-weight: 600; margin: 0 0 4px 0; display: -webkit-box; -webkit-line-clamp: 1; -webkit-box-orient: vertical; overflow: hidden;">\${experience.title}</h4>
                  <p style="color: #667eea; font-size: 12px; font-style: italic; font-weight: 500; margin: 0; display: -webkit-box; -webkit-line-clamp: 1; -webkit-box-orient: vertical; overflow: hidden;">\${experience.subtitle}</p>
                </div>

                <div style="display: flex; align-items: center; gap: 4px; color: #667eea; font-size: 10px;">
                  <svg style="width: 12px; height: 12px; flex-shrink: 0;" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <circle cx="12" cy="12" r="10"></circle>
                    <polyline points="12,6 12,12 16,14"></polyline>
                  </svg>
                  <span style="font-size: 11px; line-height: 1.3; font-weight: 500; display: -webkit-box; -webkit-line-clamp: 1; -webkit-box-orient: vertical; overflow: hidden;">\${experience.optimalVisit}</span>
                </div>
              </div>
            </div>
          \`;
        }

        // Global state
        let currentIndex = 0;
        let scrollPosition = 0;
        let isDragging = false;
        let startY = 0;
        let startScrollPosition = 0;
        const experiences = ${JSON.stringify(experiences)};
        const maxScroll = Math.max(0, (experiences.length - 3) * 200);

        function goToSlide(index) {
          currentIndex = index;
          updateMainCard();
          updateIndicators();
        }

        function goToNext() {
          currentIndex = (currentIndex + 1) % experiences.length;
          updateMainCard();
          updateIndicators();
        }

        function goToPrevious() {
          currentIndex = (currentIndex - 1 + experiences.length) % experiences.length;
          updateMainCard();
          updateIndicators();
        }

        function updateMainCard() {
          const mainCard = document.getElementById('main-experience-card');
          if (mainCard) {
            mainCard.innerHTML = createExperienceCardHtml(experiences[currentIndex], true);
          }
        }

        function updateIndicators() {
          const indicators = document.querySelectorAll('[data-index]');
          indicators.forEach((indicator, index) => {
            if (index === currentIndex) {
              indicator.style.background = '#667eea';
              indicator.style.boxShadow = '0 4px 12px rgba(102, 126, 234, 0.3)';
            } else {
              indicator.style.background = '#e5e7eb';
              indicator.style.boxShadow = 'none';
            }
          });
        }

        function scrollUp() {
          scrollPosition = Math.max(0, scrollPosition - 200);
          updateVerticalCarousel();
        }

        function scrollDown() {
          scrollPosition = Math.min(maxScroll, scrollPosition + 200);
          updateVerticalCarousel();
        }

        function updateVerticalCarousel() {
          const track = document.getElementById('vertical-carousel-track');
          if (track) {
            track.style.transform = \`translateY(-\${scrollPosition}px)\`;
          }

          // Update button states
          const upBtn = document.getElementById('scroll-up-btn');
          const downBtn = document.getElementById('scroll-down-btn');

          if (upBtn) {
            upBtn.style.opacity = scrollPosition === 0 ? '0.3' : '1';
            upBtn.style.cursor = scrollPosition === 0 ? 'not-allowed' : 'pointer';
          }

          if (downBtn) {
            downBtn.style.opacity = scrollPosition >= maxScroll ? '0.3' : '1';
            downBtn.style.cursor = scrollPosition >= maxScroll ? 'not-allowed' : 'pointer';
          }
        }

        // Drag/swipe functionality
        function handleMouseDown(e) {
          isDragging = true;
          startY = e.clientY;
          startScrollPosition = scrollPosition;
          e.preventDefault();

          document.addEventListener('mousemove', handleMouseMove);
          document.addEventListener('mouseup', handleMouseUp);
        }

        function handleMouseMove(e) {
          if (!isDragging) return;

          const deltaY = startY - e.clientY;
          const newScrollPosition = Math.max(0, Math.min(maxScroll, startScrollPosition + deltaY));
          scrollPosition = newScrollPosition;
          updateVerticalCarousel();
        }

        function handleMouseUp() {
          isDragging = false;
          document.removeEventListener('mousemove', handleMouseMove);
          document.removeEventListener('mouseup', handleMouseUp);
        }

        function handleTouchStart(e) {
          isDragging = true;
          startY = e.touches[0].clientY;
          startScrollPosition = scrollPosition;
        }

        function handleWheel(e) {
          e.preventDefault();
          const delta = e.deltaY > 0 ? 100 : -100;
          scrollPosition = Math.max(0, Math.min(maxScroll, scrollPosition + delta));
          updateVerticalCarousel();
        }



        // Initialize
        updateVerticalCarousel();
      </script>
    </div>
  `;
}

/**
 * Créer un carrousel horizontal pour les hébergements avec navigation par boutons
 */
function createAccommodationCarousel(
  accommodations: any[],
  destination?: string,
): string {
  if (!accommodations || accommodations.length === 0) {
    return `<div class="text-center p-8 text-gray-500">No accommodations available</div>`;
  }

  return `
    <div id="accommodation-carousel" class="accommodation-carousel" style="margin: 30px 0; position: relative;">
      <div style="text-align: center; margin-bottom: 24px;">
        <h2 style="font-size: 28px; color: #2c3e50; font-weight: 600; margin: 0 0 8px 0; display: flex; align-items: center; justify-content: center; gap: 12px;">
          <div class="accommodation-logo-container" style="position: relative; display: inline-block; cursor: pointer;">
            <div class="accommodation-logo-glow" style="position: absolute; inset: -4px; opacity: 0.7; filter: blur(2px); border-radius: 12px; background: linear-gradient(135deg, #3b82f6, #2563eb, #1d4ed8); transition: all 0.3s ease;"></div>
            <div class="accommodation-logo-main" style="position: relative; background: linear-gradient(135deg, #1e293b, #334155); border-radius: 12px; border: 1px solid #475569; box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25); transition: all 0.3s ease;">
              <div style="position: absolute; inset: 0; background: linear-gradient(135deg, #3b82f6, #2563eb, #1d4ed8); opacity: 0.1; border-radius: 12px;"></div>
              <div style="position: relative; z-index: 10; background: linear-gradient(135deg, #dbeafe, #bfdbfe); padding: 8px; border-radius: 8px; box-shadow: inset 0 2px 4px 0 rgba(0, 0, 0, 0.06);">
                <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="#1d4ed8" stroke-width="2.5" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-hotel" style="display: block; margin: 0 auto;">
                  <path d="M10 22v-6.57"/>
                  <path d="M12 11h.01"/>
                  <path d="M12 7h.01"/>
                  <path d="M14 15.43V22"/>
                  <path d="M15 16a5 5 0 0 0-6 0"/>
                  <path d="M16 11h.01"/>
                  <path d="M16 7h.01"/>
                  <path d="M8 11h.01"/>
                  <path d="M8 7h.01"/>
                  <rect x="4" y="2" width="16" height="20" rx="2"/>
                </svg>
              </div>
              <svg class="accommodation-sparkles" xmlns="http://www.w3.org/2000/svg" width="12" height="12" viewBox="0 0 24 24" fill="none" stroke="#fbbf24" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" style="position: absolute; top: 4px; right: 4px; opacity: 0; animation: sparkle 1.5s ease-in-out infinite;">
                <path d="M9.937 15.5A2 2 0 0 0 8.5 14.063l-6.135-1.582a.5.5 0 0 1 0-.962L8.5 9.936A2 2 0 0 0 9.937 8.5l1.582-6.135a.5.5 0 0 1 .963 0L14.063 8.5A2 2 0 0 0 15.5 9.937l6.135 1.582a.5.5 0 0 1 0 .962L15.5 14.063a2 2 0 0 0-1.437 1.437l-1.582 6.135a.5.5 0 0 1-.963 0z"/>
              </svg>
            </div>
          </div>
          Recommended Accommodations
        </h2>
        <p style="font-size: 16px; color: #667eea; font-style: italic; font-weight: 400; margin: 0;">
          Discover exceptional stays • Comfort & Luxury Combined
        </p>
      </div>
      <div class="carousel-container" style="position: relative; display: flex; align-items: center;">
        <button class="carousel-btn prev-btn" onclick="scrollAccommodationCarousel('accommodation-carousel', -1)"
                style="position: absolute; left: -20px; top: 50%; transform: translateY(-50%); background: rgba(0,0,0,0.7); color: white; border: none; width: 40px; height: 40px; border-radius: 50%; font-size: 20px; cursor: pointer; z-index: 10;">
          ‹
        </button>
        <div class="carousel-track" style="display: flex; overflow-x: auto; scroll-behavior: smooth; gap: 24px; padding: 15px 0; scrollbar-width: none; -ms-overflow-style: none;">
          ${accommodations
            .map(
              (item) => `
            <div class="accommodation-slide" style="flex: 0 0 300px; min-width: 300px;">
              <div class="card" style="background: white; border-radius: 12px; box-shadow: 0 4px 12px rgba(0,0,0,0.1); overflow: hidden; min-height: 450px; display: flex; flex-direction: column; transition: transform 0.3s ease, box-shadow 0.3s ease;">
                <div style="width: 100%; height: 200px; overflow: hidden; position: relative; background: #f8f9fa; flex-shrink: 0;">
                  ${
                    item.imageUrl
                      ? `<img src="${item.imageUrl}" alt="${item.name}" style="width: 100%; height: 100%; object-fit: cover; display: block; position: absolute; top: 0; left: 0;"
                           onerror="this.style.display='none'; this.parentElement.innerHTML='<div style=\\'width: 100%; height: 100%; background: linear-gradient(135deg, #3498db 0%, #2980b9 100%); display: flex; align-items: center; justify-content: center; color: white; font-size: 1.2em; position: absolute; top: 0; left: 0;\\'>🏨 ${item.name}</div>';">`
                      : `<div style="width: 100%; height: 100%; background: linear-gradient(135deg, #3498db 0%, #2980b9 100%); display: flex; align-items: center; justify-content: center; color: white; font-size: 1.2em; position: absolute; top: 0; left: 0;">
                    🏨 ${item.name}
                  </div>`
                  }
                  ${item.rating ? `<div style="position: absolute; bottom: 8px; right: 8px; background: rgba(0, 0, 0, 0.8); color: white; padding: 4px 8px; border-radius: 12px; font-size: 0.8em; font-weight: 500; display: flex; align-items: center; backdrop-filter: blur(4px);">⭐ ${item.rating.replace(/⭐+\s*/g, '').trim()}</div>` : ''}
                </div>
                <div class="card-content" style="padding: 15px; flex: 1; display: flex; flex-direction: column;">
                  <div style="flex: 1;">
                    <h4 style="color: #2c3e50; margin-bottom: 10px; font-size: 1.1em; margin-top: 0; font-weight: 600; line-height: 1.3;">${item.name}</h4>
                    ${
                      item.category
                        ? `
                      <div style="background: #3498db; color: white; padding: 4px 12px; border-radius: 20px; font-size: 0.8em; display: inline-block; margin-bottom: 10px; font-weight: 500;">
                        ${item.category}
                      </div>
                    `
                        : ''
                    }
                    <p style="color: #555; line-height: 1.5; margin-bottom: 15px; font-size: 0.9em; word-wrap: break-word; overflow-wrap: break-word;">${item.description}</p>
                  </div>
                  <div class="item-details" style="font-size: 0.85em; border-top: 1px solid #f0f0f0; padding-top: 10px; margin-top: auto;">
                    ${
                      item.extra
                        ? `<div style="margin-bottom: 8px; display: flex; align-items: flex-start;"><strong style="color: #3498db; margin-right: 6px; flex-shrink: 0;">📍 Address:</strong> <span style="color: #2c3e50; line-height: 1.4; word-wrap: break-word; overflow-wrap: break-word;">${item.extra}</span></div>`
                        : ''
                    }

                    ${item.phoneNumber ? `<div style="margin-bottom: 12px; display: flex; align-items: center;"><strong style="color: #27ae60; margin-right: 6px;">📞 Phone:</strong> <span style="color: #2c3e50;">${item.phoneNumber}</span></div>` : ''}
                    <button onclick="openAccommodationPopup('${item.name.replace(/'/g, "\\'")}', '${(item.extra || '').replace(/'/g, "\\'")}', '${(item.phoneNumber || '').replace(/'/g, "\\'")}', '${(item.website || '').replace(/'/g, "\\'")}', '${destination || ''}', ${item.latitude || 'null'}, ${item.longitude || 'null'})"
                            style="width: 100%; background: linear-gradient(135deg, #3498db 0%, #2980b9 100%); color: white; border: none; padding: 8px 12px; border-radius: 6px; font-size: 0.85em; font-weight: 500; cursor: pointer; transition: all 0.3s ease; margin-top: 8px;">
                      🏨 View Details & Map
                    </button>
                  </div>
                </div>
              </div>
            </div>
          `,
            )
            .join('')}
        </div>
        <button class="carousel-btn next-btn" onclick="scrollAccommodationCarousel('accommodation-carousel', 1)"
                style="position: absolute; right: -20px; top: 50%; transform: translateY(-50%); background: rgba(0,0,0,0.7); color: white; border: none; width: 40px; height: 40px; border-radius: 50%; font-size: 20px; cursor: pointer; z-index: 10;">
          ›
        </button>
      </div>
    </div>

    <script>
      // Accommodation carousel functionality - defined immediately
      window.scrollAccommodationCarousel = function(carouselId, direction) {
        console.log('🏨 Scrolling accommodation carousel:', carouselId, 'direction:', direction);
        const carousel = document.querySelector('#' + carouselId + ' .carousel-track');
        if (carousel) {
          const scrollAmount = 320; // 300px card width + 20px gap
          carousel.scrollBy({ left: direction * scrollAmount, behavior: 'smooth' });
          console.log('✅ Accommodation carousel scrolled');
        } else {
          console.warn('❌ Accommodation carousel not found:', carouselId);
        }
      };

      // Also make it available as a global function
      function scrollAccommodationCarousel(carouselId, direction) {
        window.scrollAccommodationCarousel(carouselId, direction);
      }

      console.log('🏨 Accommodation carousel functions loaded');

      // Accommodation popup functionality
      window.openAccommodationPopup = function(name, address, phone, website, destination, latitude, longitude) {
        console.log('🏨 Opening accommodation popup for:', name, 'at coordinates:', latitude, longitude);

        // Create popup overlay
        const overlay = document.createElement('div');
        overlay.id = 'accommodation-popup-overlay';
        overlay.style.cssText = \`
          position: fixed;
          top: 0;
          left: 0;
          width: 100%;
          height: 100%;
          background: rgba(0, 0, 0, 0.7);
          z-index: 10000;
          display: flex;
          align-items: center;
          justify-content: center;
          backdrop-filter: blur(5px);
        \`;

        // Create popup content
        const popup = document.createElement('div');
        popup.style.cssText = \`
          background: white;
          border-radius: 16px;
          max-width: 90vw;
          max-height: 90vh;
          width: 800px;
          overflow: hidden;
          box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.5);
          position: relative;
        \`;

        // Utiliser les coordonnées GPS si disponibles, sinon recherche textuelle
        let mapSrc;
        if (latitude && longitude) {
          // Utiliser les coordonnées précises avec un marqueur
          mapSrc = \`https://www.openstreetmap.org/export/embed.html?bbox=\${longitude-0.01},\${latitude-0.01},\${longitude+0.01},\${latitude+0.01}&layer=mapnik&marker=\${latitude},\${longitude}\`;
          console.log('🗺️ Using GPS coordinates for map:', latitude, longitude);
        } else {
          // Fallback vers recherche textuelle
          const mapQuery = encodeURIComponent(\`\${name} \${address} \${destination}\`);
          mapSrc = \`https://www.openstreetmap.org/export/embed.html?bbox=&layer=mapnik&marker=&query=\${mapQuery}\`;
          console.log('🔍 Using text search for map:', mapQuery);
        }

        popup.innerHTML = \`
          <div style="padding: 24px; border-bottom: 1px solid #e5e7eb;">
            <div style="display: flex; justify-content: space-between; align-items: flex-start; margin-bottom: 16px;">
              <h2 style="margin: 0; color: #1f2937; font-size: 1.5em; font-weight: 600;">\${name}</h2>
              <button onclick="closeAccommodationPopup()" style="background: none; border: none; font-size: 24px; cursor: pointer; color: #6b7280; padding: 0; width: 32px; height: 32px; display: flex; align-items: center; justify-content: center; border-radius: 50%; transition: all 0.2s;">
                ×
              </button>
            </div>
            \${address ? \`<p style="margin: 0; color: #6b7280; display: flex; align-items: center;"><span style="margin-right: 8px;">📍</span>\${address}</p>\` : ''}
            \${latitude && longitude ? \`<p style="margin: 8px 0 0 0; color: #6b7280; font-size: 0.9em; display: flex; align-items: center;"><span style="margin-right: 8px;">🌐</span>GPS: \${latitude.toFixed(6)}, \${longitude.toFixed(6)}</p>\` : ''}
          </div>

          <div style="height: 300px; position: relative; background: #f3f4f6;">
            <iframe
              src="\${mapSrc}"
              style="width: 100%; height: 100%; border: none;"
              title="Accommodation Location Map">
            </iframe>
          </div>

          <div style="padding: 24px;">
            <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 16px;">
              \${phone ? \`
                <a href="tel:\${phone}" style="display: flex; align-items: center; justify-content: center; padding: 12px 16px; background: linear-gradient(135deg, #10b981 0%, #059669 100%); color: white; text-decoration: none; border-radius: 8px; font-weight: 500; transition: all 0.3s ease;">
                  <span style="margin-right: 8px;">📞</span>
                  Call Now
                </a>
              \` : ''}
              \${website ? \`
                <a href="\${website}" target="_blank" style="display: flex; align-items: center; justify-content: center; padding: 12px 16px; background: linear-gradient(135deg, #3b82f6 0%, #2563eb 100%); color: white; text-decoration: none; border-radius: 8px; font-weight: 500; transition: all 0.3s ease;">
                  <span style="margin-right: 8px;">🌐</span>
                  Visit Website
                </a>
              \` : \`
                <a href="https://www.google.com/search?q=\${encodeURIComponent(name + ' ' + address + ' ' + destination)}" target="_blank" style="display: flex; align-items: center; justify-content: center; padding: 12px 16px; background: linear-gradient(135deg, #3b82f6 0%, #2563eb 100%); color: white; text-decoration: none; border-radius: 8px; font-weight: 500; transition: all 0.3s ease;">
                  <span style="margin-right: 8px;">🔍</span>
                  Search Online
                </a>
              \`}
            </div>
          </div>
        \`;

        overlay.appendChild(popup);
        document.body.appendChild(overlay);

        // Close on overlay click
        overlay.addEventListener('click', function(e) {
          if (e.target === overlay) {
            closeAccommodationPopup();
          }
        });

        // Close on escape key
        document.addEventListener('keydown', function(e) {
          if (e.key === 'Escape') {
            closeAccommodationPopup();
          }
        });
      };

      window.closeAccommodationPopup = function() {
        const overlay = document.getElementById('accommodation-popup-overlay');
        if (overlay) {
          overlay.remove();
        }
      };

      // Add hover effects to cards and uniform height
      document.addEventListener('DOMContentLoaded', function() {
        const cards = document.querySelectorAll('#accommodation-carousel .card');

        // Uniformiser la hauteur des cartes
        setTimeout(() => {
          let maxHeight = 0;
          cards.forEach(card => {
            const height = card.offsetHeight;
            if (height > maxHeight) {
              maxHeight = height;
            }
          });

          // Appliquer la hauteur maximale à toutes les cartes
          cards.forEach(card => {
            card.style.height = maxHeight + 'px';
          });

          console.log('🏨 Accommodation cards height uniformized to:', maxHeight + 'px');
        }, 100);

        // Effets de survol
        cards.forEach(card => {
          card.addEventListener('mouseenter', function() {
            this.style.transform = 'translateY(-5px)';
            this.style.boxShadow = '0 8px 20px rgba(0,0,0,0.15)';
          });
          card.addEventListener('mouseleave', function() {
            this.style.transform = 'translateY(0)';
            this.style.boxShadow = '0 4px 12px rgba(0,0,0,0.1)';
          });
        });

        // Add hover effect to popup buttons
        document.addEventListener('click', function(e) {
          if (e.target.matches('button[onclick*="openAccommodationPopup"]')) {
            e.target.style.transform = 'translateY(-2px)';
            e.target.style.boxShadow = '0 4px 12px rgba(52, 152, 219, 0.3)';
            setTimeout(() => {
              e.target.style.transform = '';
              e.target.style.boxShadow = '';
            }, 150);
          }
        });
      });
    </script>
  `;
}

/**
 * Create restaurant carousel with horizontal scrolling
 */
async function createRestaurantCarousel(
  items: Array<{
    name: string;
    description: string;
    extra?: string;
    imageUrl?: string;
    category?: string;
    rating?: string;
    type?: string;
    phoneNumber?: string;
    website?: string;
    latitude?: number | null;
    longitude?: number | null;
  }>,
  destination?: string,
): Promise<string> {
  console.log(
    `🍽️ Creating restaurant carousel with ${items.length} items for ${destination}`,
  );

  if (!items || items.length === 0) {
    return `<div class="text-center p-8 text-gray-500">No restaurants available</div>`;
  }

  // Récupérer les images pour chaque restaurant
  const itemsWithImages = await Promise.all(
    items.slice(0, 12).map(async (item) => {
      let imageUrl = item.imageUrl;

      // Si pas d'image fournie, récupérer via Serper API
      if (!imageUrl) {
        try {
          imageUrl = await getRestaurantImageUrl(item.name, destination);
          console.log(`🖼️ Image récupérée pour ${item.name}: ${imageUrl}`);
        } catch (error) {
          console.error(
            `Erreur lors de la récupération d'image pour ${item.name}:`,
            error,
          );
          // Utiliser l'image de fallback appropriée
          imageUrl = getFallbackImage('restaurant', item.name);
        }
      }

      return { ...item, imageUrl };
    }),
  );

  return `
    <div id="restaurant-carousel" class="restaurant-carousel" style="margin: 30px 0; position: relative;">
      <div style="text-align: center; margin-bottom: 24px;">
        <h2 style="font-size: 28px; color: #2c3e50; font-weight: 600; margin: 0 0 8px 0; display: flex; align-items: center; justify-content: center; gap: 12px;">
          <div class="dining-logo-container" style="position: relative; display: inline-block; cursor: pointer;">
            <div class="dining-logo-glow" style="position: absolute; inset: -4px; opacity: 0.7; filter: blur(2px); border-radius: 12px; background: linear-gradient(135deg, #f59e0b, #d97706, #b45309); transition: all 0.3s ease;"></div>
            <div class="dining-logo-main" style="position: relative; background: linear-gradient(135deg, #1e293b, #334155); border-radius: 12px; border: 1px solid #475569; box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25); transition: all 0.3s ease;">
              <div style="position: absolute; inset: 0; background: linear-gradient(135deg, #f59e0b, #d97706, #b45309); opacity: 0.1; border-radius: 12px;"></div>
              <div style="position: relative; z-index: 10; background: linear-gradient(135deg, #fef3c7, #fde68a); padding: 8px; border-radius: 8px; box-shadow: inset 0 2px 4px 0 rgba(0, 0, 0, 0.06);">
                <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="#b45309" stroke-width="2.5" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-utensils-crossed" style="display: block; margin: 0 auto;">
                  <path d="m16 2-2.3 2.3a3 3 0 0 0 0 4.2l1.8 1.8a3 3 0 0 0 4.2 0L22 8"/>
                  <path d="M15 15 3.3 3.3a4.2 4.2 0 0 0 0 6l7.3 7.3c.7.7 2 .7 2.8 0L15 15Zm0 0 7 7"/>
                  <path d="m2.1 21.8 6.4-6.3"/>
                  <path d="m19 5-7 7"/>
                </svg>
              </div>
              <svg class="dining-sparkles" xmlns="http://www.w3.org/2000/svg" width="12" height="12" viewBox="0 0 24 24" fill="none" stroke="#fbbf24" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" style="position: absolute; top: 4px; right: 4px; opacity: 0; animation: sparkle 1.5s ease-in-out infinite;">
                <path d="M9.937 15.5A2 2 0 0 0 8.5 14.063l-6.135-1.582a.5.5 0 0 1 0-.962L8.5 9.936A2 2 0 0 0 9.937 8.5l1.582-6.135a.5.5 0 0 1 .963 0L14.063 8.5A2 2 0 0 0 15.5 9.937l6.135 1.582a.5.5 0 0 1 0 .962L15.5 14.063a2 2 0 0 0-1.437 1.437l-1.582 6.135a.5.5 0 0 1-.963 0z"/>
              </svg>
            </div>
          </div>
          Recommended Restaurants
        </h2>
        <p style="font-size: 16px; color: #667eea; font-style: italic; font-weight: 400; margin: 0;">
          Discover the finest culinary experiences • Authentic Local Flavors
        </p>
      </div>
      <div class="carousel-container" style="position: relative; display: flex; align-items: center;">
        <button class="carousel-btn prev-btn" onclick="scrollRestaurantCarousel('restaurant-carousel', -1)"
                style="position: absolute; left: -20px; top: 50%; transform: translateY(-50%); background: rgba(0,0,0,0.7); color: white; border: none; width: 40px; height: 40px; border-radius: 50%; font-size: 20px; cursor: pointer; z-index: 10;">
          ‹
        </button>
        <div class="carousel-track" style="display: flex; overflow-x: auto; scroll-behavior: smooth; gap: 24px; padding: 15px 0; scrollbar-width: none; -ms-overflow-style: none;">
          ${itemsWithImages
            .map(
              (item) => `
            <div class="restaurant-slide" style="flex: 0 0 300px; min-width: 300px;">
              <div class="card" style="background: white; border-radius: 12px; box-shadow: 0 4px 12px rgba(0,0,0,0.1); overflow: hidden; min-height: 450px; display: flex; flex-direction: column; transition: transform 0.3s ease, box-shadow 0.3s ease;">
                <div style="width: 100%; height: 200px; overflow: hidden; position: relative; background: #f8f9fa; flex-shrink: 0;">
                  ${
                    item.imageUrl
                      ? `<img src="${item.imageUrl}" alt="${item.name}" style="width: 100%; height: 100%; object-fit: cover; display: block; position: absolute; top: 0; left: 0;"
                           onerror="this.style.display='none'; this.parentElement.innerHTML='<div style=\\'width: 100%; height: 100%; background: linear-gradient(135deg, #ff6b6b 0%, #ee5a24 100%); display: flex; align-items: center; justify-content: center; color: white; font-size: 1.2em; position: absolute; top: 0; left: 0;\\'>🍽️ ${item.name}</div>';">`
                      : `<div style="width: 100%; height: 100%; background: linear-gradient(135deg, #ff6b6b 0%, #ee5a24 100%); display: flex; align-items: center; justify-content: center; color: white; font-size: 1.2em; position: absolute; top: 0; left: 0;">
                    🍽️ ${item.name}
                  </div>`
                  }
                  ${item.rating ? `<div style="position: absolute; bottom: 8px; right: 8px; background: rgba(0, 0, 0, 0.8); color: white; padding: 4px 8px; border-radius: 12px; font-size: 0.8em; font-weight: 500; display: flex; align-items: center; backdrop-filter: blur(4px);">⭐ ${item.rating.replace(/⭐+\s*/g, '').trim()}</div>` : ''}
                </div>
                <div class="card-content" style="padding: 15px; flex: 1; display: flex; flex-direction: column;">
                  <div style="flex: 1;">
                    <h4 style="color: #2c3e50; margin-bottom: 10px; font-size: 1.1em; margin-top: 0; font-weight: 600; line-height: 1.3;">${item.name}</h4>
                    ${
                      item.category
                        ? `
                      <div style="background: #e74c3c; color: white; padding: 4px 12px; border-radius: 20px; font-size: 0.8em; display: inline-block; margin-bottom: 10px; font-weight: 500;">
                        ${item.category}
                      </div>
                    `
                        : ''
                    }
                    <p style="color: #555; line-height: 1.5; margin-bottom: 15px; font-size: 0.9em; word-wrap: break-word; overflow-wrap: break-word;">${item.description}</p>
                  </div>
                  <div class="item-details" style="font-size: 0.85em; border-top: 1px solid #f0f0f0; padding-top: 10px; margin-top: auto;">
                    ${
                      item.extra
                        ? `<div style="margin-bottom: 8px; display: flex; align-items: flex-start;"><strong style="color: #3498db; margin-right: 6px; flex-shrink: 0;">📍 Address:</strong> <span style="color: #2c3e50; line-height: 1.4; word-wrap: break-word; overflow-wrap: break-word;">${item.extra}</span></div>`
                        : ''
                    }

                    ${item.phoneNumber ? `<div style="margin-bottom: 12px; display: flex; align-items: center;"><strong style="color: #27ae60; margin-right: 6px;">📞 Phone:</strong> <span style="color: #2c3e50;">${item.phoneNumber}</span></div>` : ''}
                    <button onclick="openRestaurantPopup('${item.name.replace(/'/g, "\\'")}', '${(item.extra || '').replace(/'/g, "\\'")}', '${(item.phoneNumber || '').replace(/'/g, "\\'")}', '${(item.website || '').replace(/'/g, "\\'")}', '${destination || ''}', ${item.latitude || 'null'}, ${item.longitude || 'null'})"
                            style="width: 100%; background: linear-gradient(135deg, #e74c3c 0%, #c0392b 100%); color: white; border: none; padding: 8px 12px; border-radius: 6px; font-size: 0.85em; font-weight: 500; cursor: pointer; transition: all 0.3s ease; margin-top: 8px;">
                      📍 View Details & Map
                    </button>
                  </div>
                </div>
              </div>
            </div>
          `,
            )
            .join('')}
        </div>
        <button class="carousel-btn next-btn" onclick="scrollRestaurantCarousel('restaurant-carousel', 1)"
                style="position: absolute; right: -20px; top: 50%; transform: translateY(-50%); background: rgba(0,0,0,0.7); color: white; border: none; width: 40px; height: 40px; border-radius: 50%; font-size: 20px; cursor: pointer; z-index: 10;">
          ›
        </button>
      </div>
    </div>

    <script>
      // Restaurant carousel functionality - defined immediately
      window.scrollRestaurantCarousel = function(carouselId, direction) {
        console.log('🎠 Scrolling restaurant carousel:', carouselId, 'direction:', direction);
        const carousel = document.querySelector('#' + carouselId + ' .carousel-track');
        if (carousel) {
          const scrollAmount = 320; // 300px card width + 20px gap
          carousel.scrollBy({ left: direction * scrollAmount, behavior: 'smooth' });
          console.log('✅ Restaurant carousel scrolled');
        } else {
          console.warn('❌ Restaurant carousel not found:', carouselId);
        }
      };

      // Also make it available as a global function
      function scrollRestaurantCarousel(carouselId, direction) {
        window.scrollRestaurantCarousel(carouselId, direction);
      }

      console.log('🍽️ Restaurant carousel functions loaded');

      // Restaurant popup functionality
      window.openRestaurantPopup = function(name, address, phone, website, destination, latitude, longitude) {
        console.log('🍽️ Opening restaurant popup for:', name, 'at coordinates:', latitude, longitude);

        // Create popup overlay
        const overlay = document.createElement('div');
        overlay.id = 'restaurant-popup-overlay';
        overlay.style.cssText = \`
          position: fixed;
          top: 0;
          left: 0;
          width: 100%;
          height: 100%;
          background: rgba(0, 0, 0, 0.7);
          z-index: 10000;
          display: flex;
          align-items: center;
          justify-content: center;
          backdrop-filter: blur(5px);
        \`;

        // Create popup content
        const popup = document.createElement('div');
        popup.style.cssText = \`
          background: white;
          border-radius: 16px;
          max-width: 90vw;
          max-height: 90vh;
          width: 800px;
          overflow: hidden;
          box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.5);
          position: relative;
        \`;

        // Utiliser les coordonnées GPS si disponibles, sinon recherche textuelle
        let mapSrc;
        if (latitude && longitude) {
          // Utiliser les coordonnées précises avec un marqueur
          mapSrc = \`https://www.openstreetmap.org/export/embed.html?bbox=\${longitude-0.01},\${latitude-0.01},\${longitude+0.01},\${latitude+0.01}&layer=mapnik&marker=\${latitude},\${longitude}\`;
          console.log('🗺️ Using GPS coordinates for map:', latitude, longitude);
        } else {
          // Fallback vers recherche textuelle
          const mapQuery = encodeURIComponent(\`\${name} \${address} \${destination}\`);
          mapSrc = \`https://www.openstreetmap.org/export/embed.html?bbox=&layer=mapnik&marker=&query=\${mapQuery}\`;
          console.log('🔍 Using text search for map:', mapQuery);
        }

        popup.innerHTML = \`
          <div style="padding: 24px; border-bottom: 1px solid #e5e7eb;">
            <div style="display: flex; justify-content: space-between; align-items: flex-start; margin-bottom: 16px;">
              <h2 style="margin: 0; color: #1f2937; font-size: 1.5em; font-weight: 600;">\${name}</h2>
              <button onclick="closeRestaurantPopup()" style="background: none; border: none; font-size: 24px; cursor: pointer; color: #6b7280; padding: 0; width: 32px; height: 32px; display: flex; align-items: center; justify-content: center; border-radius: 50%; transition: all 0.2s;">
                ×
              </button>
            </div>
            \${address ? \`<p style="margin: 0; color: #6b7280; display: flex; align-items: center;"><span style="margin-right: 8px;">📍</span>\${address}</p>\` : ''}
            \${latitude && longitude ? \`<p style="margin: 8px 0 0 0; color: #6b7280; font-size: 0.9em; display: flex; align-items: center;"><span style="margin-right: 8px;">🌐</span>GPS: \${latitude.toFixed(6)}, \${longitude.toFixed(6)}</p>\` : ''}
          </div>

          <div style="height: 300px; position: relative; background: #f3f4f6;">
            <iframe
              src="\${mapSrc}"
              style="width: 100%; height: 100%; border: none;"
              title="Restaurant Location Map">
            </iframe>
          </div>

          <div style="padding: 24px;">
            <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 16px;">
              \${phone ? \`
                <a href="tel:\${phone}" style="display: flex; align-items: center; justify-content: center; padding: 12px 16px; background: linear-gradient(135deg, #10b981 0%, #059669 100%); color: white; text-decoration: none; border-radius: 8px; font-weight: 500; transition: all 0.3s ease;">
                  <span style="margin-right: 8px;">📞</span>
                  Call Now
                </a>
              \` : ''}
              \${website ? \`
                <a href="\${website}" target="_blank" style="display: flex; align-items: center; justify-content: center; padding: 12px 16px; background: linear-gradient(135deg, #3b82f6 0%, #2563eb 100%); color: white; text-decoration: none; border-radius: 8px; font-weight: 500; transition: all 0.3s ease;">
                  <span style="margin-right: 8px;">🌐</span>
                  Visit Website
                </a>
              \` : \`
                <a href="https://www.google.com/search?q=\${encodeURIComponent(name + ' ' + address + ' ' + destination)}" target="_blank" style="display: flex; align-items: center; justify-content: center; padding: 12px 16px; background: linear-gradient(135deg, #3b82f6 0%, #2563eb 100%); color: white; text-decoration: none; border-radius: 8px; font-weight: 500; transition: all 0.3s ease;">
                  <span style="margin-right: 8px;">🔍</span>
                  Search Online
                </a>
              \`}
            </div>
          </div>
        \`;

        overlay.appendChild(popup);
        document.body.appendChild(overlay);

        // Close on overlay click
        overlay.addEventListener('click', function(e) {
          if (e.target === overlay) {
            closeRestaurantPopup();
          }
        });

        // Close on escape key
        document.addEventListener('keydown', function(e) {
          if (e.key === 'Escape') {
            closeRestaurantPopup();
          }
        });
      };

      window.closeRestaurantPopup = function() {
        const overlay = document.getElementById('restaurant-popup-overlay');
        if (overlay) {
          overlay.remove();
        }
      };

      // Add hover effects to cards and uniform height
      document.addEventListener('DOMContentLoaded', function() {
        const cards = document.querySelectorAll('#restaurant-carousel .card');

        // Uniformiser la hauteur des cartes
        setTimeout(() => {
          let maxHeight = 0;
          cards.forEach(card => {
            const height = card.offsetHeight;
            if (height > maxHeight) {
              maxHeight = height;
            }
          });

          // Appliquer la hauteur maximale à toutes les cartes
          cards.forEach(card => {
            card.style.height = maxHeight + 'px';
          });

          console.log('🍽️ Restaurant cards height uniformized to:', maxHeight + 'px');
        }, 100);

        // Effets de survol
        cards.forEach(card => {
          card.addEventListener('mouseenter', function() {
            this.style.transform = 'translateY(-5px)';
            this.style.boxShadow = '0 8px 20px rgba(0,0,0,0.15)';
          });
          card.addEventListener('mouseleave', function() {
            this.style.transform = 'translateY(0)';
            this.style.boxShadow = '0 4px 12px rgba(0,0,0,0.1)';
          });
        });

        // Add hover effect to popup buttons
        document.addEventListener('click', function(e) {
          if (e.target.matches('button[onclick*="openRestaurantPopup"]')) {
            e.target.style.transform = 'translateY(-2px)';
            e.target.style.boxShadow = '0 4px 12px rgba(231, 76, 60, 0.3)';
            setTimeout(() => {
              e.target.style.transform = '';
              e.target.style.boxShadow = '';
            }, 150);
          }
        });
      });
    </script>
  `;
}

/**
 * Create simple text and image layout (replacing carousel) with real images
 */
async function createSimpleLayout(
  items: Array<{
    name: string;
    description: string;
    extra?: string;
    imageUrl?: string;
    category?: string;
    rating?: string;
    type?: string;
  }>,
  layoutType: 'restaurant' | 'hotel' | 'poi',
  destination?: string,
): Promise<string> {
  console.log(
    `📝 Creating simple layout with ${items.length} items for ${layoutType}`,
  );

  if (!items || items.length === 0) {
    return `<div class="text-center p-8 text-gray-500">No ${layoutType}s available</div>`;
  }

  // Use sophisticated carousel for POI/local recommendations
  if (layoutType === 'poi') {
    return createLocalRecommendationsCarousel(items, destination);
  }

  // Use carousel for restaurants
  if (layoutType === 'restaurant') {
    return createRestaurantCarousel(items, destination);
  }

  // Only hotels reach this point
  // Récupérer les images pour chaque item
  const itemsWithImages = await Promise.all(
    items.slice(0, 15).map(async (item) => {
      let imageUrl = item.imageUrl;

      // Si pas d'image fournie, récupérer via Serper API
      if (!imageUrl) {
        try {
          // Only hotels reach this point since restaurants are handled above
          imageUrl = await getHotelImageUrl(item.name, destination);
          console.log(`🖼️ Image récupérée pour ${item.name}: ${imageUrl}`);
        } catch (error) {
          console.error(
            `Erreur lors de la récupération d'image pour ${item.name}:`,
            error,
          );
          // Utiliser l'image de fallback appropriée
          imageUrl = getFallbackImage('hotel', item.name);
        }
      }

      return { ...item, imageUrl };
    }),
  );

  // Use carousel for accommodations - return the carousel directly with its own title
  return createAccommodationCarousel(itemsWithImages, destination);
}

/**
 * Récupérer de vraies données d'hébergements depuis l'API Serper Maps
 */
async function getWebAccommodations(
  destination: string,
  country: string,
  maxResults = 12,
): Promise<
  Array<{
    name: string;
    description: string;
    extra: string;
    category: string;
    rating: string;
    type: string;
    imageUrl?: string;
    phoneNumber?: string;
    website?: string;
    latitude?: number | null;
    longitude?: number | null;
  }>
> {
  console.log(
    `🏨 Recherche d'hébergements réels via Serper Maps pour ${destination}, ${country}`,
  );

  try {
    const apiKey = process.env.SERPER_API as string;
    if (!apiKey) {
      console.warn('Serper API key not found, using fallback accommodations');
      return generateFallbackHotels(destination, country, maxResults);
    }

    // Construire la requête de recherche pour les hôtels
    const searchQuery = `hotel ${destination} ${country}`;
    const url = `https://google.serper.dev/maps?q=${encodeURIComponent(searchQuery)}&apiKey=${apiKey}`;

    console.log(`[Serper Maps] Recherche: "${searchQuery}"`);

    const requestOptions = {
      method: 'GET',
      redirect: 'follow' as RequestRedirect,
    };

    const response = await fetch(url, requestOptions);

    if (!response.ok) {
      throw new Error(`Serper Maps API error: ${response.status}`);
    }

    const data = await response.json();
    console.log(`[Serper Maps] ${data.places?.length || 0} hôtels trouvés`);

    if (!data.places || data.places.length === 0) {
      console.log('[Serper Maps] Aucun hôtel trouvé, utilisation du fallback');
      return generateFallbackHotels(destination, country, maxResults);
    }

    // Convertir les données Serper Maps en format accommodations
    const accommodations: Array<{
      name: string;
      description: string;
      extra: string;
      category: string;
      rating: string;
      type: string;
      imageUrl?: string;
      phoneNumber?: string;
      website?: string;
      latitude?: number | null;
      longitude?: number | null;
    }> = [];

    // Traiter chaque hôtel trouvé par Serper Maps
    for (let i = 0; i < Math.min(data.places.length, maxResults); i++) {
      const place = data.places[i];

      console.log(`[Serper Maps] Traitement de l'hôtel: "${place.title}"`);

      // Extraire les informations de l'hôtel
      const hotelName = place.title || `Hotel ${i + 1}`;
      const description =
        place.description || 'Excellent hotel with great amenities';
      const address = place.address || '';

      // Construire le rating avec étoiles
      let ratingDisplay = '⭐⭐⭐⭐ 4.0/5';
      if (place.rating && place.ratingCount) {
        const stars = '⭐'.repeat(Math.floor(place.rating));
        ratingDisplay = `${stars} ${place.rating}/5 (${place.ratingCount} reviews)`;
      }

      // Déterminer la catégorie basée sur le type
      let category = 'Standard Hotel';
      if (place.type) {
        if (
          place.type.toLowerCase().includes('luxury') ||
          place.rating >= 4.5
        ) {
          category = 'Luxury Hotel';
        } else if (place.type.toLowerCase().includes('business')) {
          category = 'Business Hotel';
        } else if (place.type.toLowerCase().includes('boutique')) {
          category = 'Boutique Hotel';
        } else if (place.rating < 3.5) {
          category = 'Budget Hotel';
        }
      }

      // Construire les informations supplémentaires
      let extraInfo = 'Contact for pricing';
      if (address) {
        extraInfo = address.split(',').slice(0, 2).join(', '); // Première partie de l'adresse
      }

      // Séparer le numéro de téléphone et le site web
      const phoneNumber = place.phoneNumber || '';
      const website = place.website || '';

      // Récupérer les coordonnées GPS
      const latitude = place.latitude || null;
      const longitude = place.longitude || null;

      // Utiliser l'image thumbnail de Google Maps si disponible
      let imageUrl = place.thumbnailUrl;
      if (!imageUrl) {
        // Fallback vers notre système d'images
        try {
          imageUrl = await getHotelImageUrlWithRetry(hotelName, destination);
        } catch (error) {
          console.warn(`Erreur récupération image pour ${hotelName}:`, error);
          imageUrl = getFallbackImage('hotel', hotelName);
        }
      }

      accommodations.push({
        name: hotelName,
        description: description,
        extra: extraInfo,
        category: category,
        rating: ratingDisplay,
        type: 'hotel',
        imageUrl: imageUrl,
        phoneNumber: phoneNumber,
        website: website,
        latitude: latitude,
        longitude: longitude,
      });

      console.log(
        `[Serper Maps] ✅ Hôtel ajouté: "${hotelName}" (${category})`,
      );
    }

    console.log(`[Web Hotels] ${accommodations.length} hébergements trouvés`);

    // Si pas assez de résultats, ajouter des hébergements génériques
    if (accommodations.length < 6) {
      const fallbackHotels = generateFallbackHotels(
        destination,
        country,
        maxResults - accommodations.length,
      );
      accommodations.push(...fallbackHotels);
    }

    return accommodations.slice(0, maxResults);
  } catch (error) {
    console.error(
      'Erreur lors de la récupération des hébergements web:',
      error,
    );

    // Retourner des hébergements de fallback en cas d'erreur
    return generateFallbackHotels(destination, country, maxResults);
  }
}

/**
 * Fonction utilitaire pour récupérer des images Tavily pour un lieu (POI)
 */
async function getLocationImagesWithTavily(
  location: string,
  city: string,
  country: string,
): Promise<string[]> {
  console.log(
    `[Tavily] Recherche d'images pour ${location} à ${city}, ${country}`,
  );

  const mockSession = {
    user: { id: 'system', type: 'guest' as const },
    expires: new Date(Date.now() + 24 * 60 * 60 * 1000).toISOString(),
  };
  const mockDataStream: UIMessageStreamWriter<ChatMessage> = {
    write: () => {},
  } as any;

  const searchTool = web_search({
    session: mockSession,
    dataStream: mockDataStream,
  });

  const queries = [
    `${location} ${city} ${country} high resolution photo`,
    `${location} ${city} ${country} landmark image`,
    `${location} ${city} ${country} travel`,
  ];

  console.log('[Tavily] Requêtes de recherche:', queries);

  const searchResults = await Promise.all(
    queries.map((query) =>
      (searchTool.execute as any)({
        queries: [query],
        maxResults: [5],
        topics: ['general'],
        searchDepth: ['advanced'],
      }),
    ),
  );

  console.log('[Tavily] Résultats de recherche reçus:', searchResults.length);

  const imageUrls: string[] = [];
  for (const result of searchResults) {
    if (
      result?.searches?.[0]?.images &&
      Array.isArray(result.searches[0].images)
    ) {
      for (const image of result.searches[0].images) {
        if (typeof image === 'string') {
          console.log('[Tavily] Image trouvée (string):', image);
          imageUrls.push(image);
        } else if (image && typeof image === 'object' && image.url) {
          console.log('[Tavily] Image trouvée (object):', image.url);
          imageUrls.push(image.url);
        }
      }
    }
  }

  // Filtrer les doublons et les images de mauvaise qualité
  const filtered = imageUrls.filter(
    (url, idx, arr) =>
      url &&
      arr.indexOf(url) === idx &&
      !url.includes('icon') &&
      !url.includes('thumb') &&
      !url.includes('small'),
  );

  console.log('[Tavily] Images filtrées:', filtered.length);
  console.log('[Tavily] URLs des images:', filtered);

  // Retourner jusqu'à 3 images
  return filtered.slice(0, 3);
}

/**
 * Extraire le nom de l'hôtel depuis le titre de recherche
 * @deprecated Utility function - not currently used
 */
function _extractHotelName(title: string, _destination: string): string | null {
  console.log(`[Extract Hotel] Titre original: "${title}"`);

  // Nettoyer le titre de manière moins agressive
  const hotelName = title
    // Supprimer les sites de booking à la fin
    .replace(
      /\s*-\s*(Booking\.com|TripAdvisor|Hotels\.com|Expedia|Agoda|booking\.com).*$/i,
      '',
    )
    // Supprimer les pipes et ce qui suit
    .replace(/\s*\|\s*.*$/i, '')
    // Supprimer les parenthèses avec des chiffres (reviews, etc.)
    .replace(/\s*\(\d+.*\).*$/i, '')
    // Supprimer "star" ratings à la fin
    .replace(/\s*\d+\s*star.*$/i, '')
    // Supprimer seulement les patterns génériques, pas le mot "hotel" dans le nom
    .replace(/^(Best|Top|Cheap|Budget)\s+/i, '')
    .replace(/\s+(in|near|at)\s+.*$/i, '')
    .trim();

  console.log(`[Extract Hotel] Après nettoyage: "${hotelName}"`);

  // Vérifications plus permissives
  if (
    hotelName.length > 2 &&
    hotelName.length < 150 &&
    !hotelName
      .toLowerCase()
      .match(/^(best|top|cheap|budget|hotels?|accommodation)$/i) &&
    !hotelName.toLowerCase().includes('list of') &&
    !hotelName.toLowerCase().includes('compare prices')
  ) {
    console.log(`[Extract Hotel] ✅ Nom valide: "${hotelName}"`);
    return hotelName;
  }

  console.log(`[Extract Hotel] ❌ Nom rejeté: "${hotelName}"`);
  return null;
}

/**
 * Extraire le rating depuis le snippet
 * @deprecated Utility function - not currently used
 */
function _extractRating(snippet: string): string | null {
  // Chercher des patterns de rating
  const ratingPatterns = [
    /(\d+\.?\d*)\s*\/\s*5/i,
    /(\d+\.?\d*)\s*out\s*of\s*5/i,
    /rating:\s*(\d+\.?\d*)/i,
    /(\d+\.?\d*)\s*stars?/i,
  ];

  for (const pattern of ratingPatterns) {
    const match = snippet.match(pattern);
    if (match) {
      const rating = Number.parseFloat(match[1]);
      if (rating >= 1 && rating <= 5) {
        const stars = '⭐'.repeat(Math.floor(rating));
        return `${stars} ${rating}/5`;
      }
    }
  }

  return null;
}

/**
 * Extraire la gamme de prix depuis le snippet
 */
function _extractPriceRange(snippet: string): string | null {
  // Chercher des patterns de prix
  const pricePatterns = [
    /\$(\d+)-\$?(\d+)/,
    /€(\d+)-€?(\d+)/,
    /£(\d+)-£?(\d+)/,
    /from\s*\$(\d+)/i,
    /from\s*€(\d+)/i,
    /from\s*£(\d+)/i,
    /\$(\d+)\s*per\s*night/i,
    /€(\d+)\s*per\s*night/i,
    /£(\d+)\s*per\s*night/i,
  ];

  for (const pattern of pricePatterns) {
    const match = snippet.match(pattern);
    if (match) {
      if (match[2]) {
        return `${match[0]}/night`;
      } else {
        return `From ${match[0]}/night`;
      }
    }
  }

  return null;
}

/**
 * Déterminer la catégorie de l'hôtel
 */
function _determineHotelCategory(title: string, snippet: string): string {
  const text = `${title} ${snippet}`.toLowerCase();

  if (
    text.includes('luxury') ||
    text.includes('5 star') ||
    text.includes('premium') ||
    text.includes('resort')
  ) {
    return 'Luxury Hotel';
  } else if (
    text.includes('boutique') ||
    text.includes('design') ||
    text.includes('unique')
  ) {
    return 'Boutique Hotel';
  } else if (
    text.includes('budget') ||
    text.includes('cheap') ||
    text.includes('affordable') ||
    text.includes('hostel')
  ) {
    return 'Budget Hotel';
  } else if (
    text.includes('business') ||
    text.includes('conference') ||
    text.includes('meeting')
  ) {
    return 'Business Hotel';
  } else if (
    text.includes('family') ||
    text.includes('kids') ||
    text.includes('children')
  ) {
    return 'Family Hotel';
  } else {
    return 'Standard Hotel';
  }
}

/**
 * Nettoyer la description de manière moins agressive
 */
function _cleanDescription(snippet: string): string {
  console.log(
    `[Clean Description] Original: "${snippet.substring(0, 100)}..."`,
  );

  const cleaned = snippet
    // Supprimer les sites de booking à la fin
    .replace(
      /\s*-\s*(Booking\.com|TripAdvisor|Hotels\.com|Expedia|Agoda|booking\.com).*$/i,
      '',
    )
    // Supprimer les appels à l'action
    .replace(/Book now.*$/i, '')
    .replace(/Check availability.*$/i, '')
    .replace(/Reserve now.*$/i, '')
    .replace(/View deals.*$/i, '')
    // Supprimer les dates spécifiques
    .replace(/\d{1,2}\/\d{1,2}\/\d{4}/g, '')
    // Garder les prix mais les nettoyer
    .replace(/from\s*[\$€£]\d+/gi, (match) => `Starting ${match}`)
    // Supprimer les caractères en trop
    .replace(/\s+/g, ' ')
    .trim();

  const result =
    cleaned.length > 200 ? `${cleaned.substring(0, 200)}...` : cleaned;

  console.log(`[Clean Description] Nettoyé: "${result.substring(0, 100)}..."`);
  return result;
}

/**
 * Générer des hôtels de fallback
 */
function generateFallbackHotels(
  destination: string,
  _country: string,
  count: number,
): Array<{
  name: string;
  description: string;
  extra: string;
  category: string;
  rating: string;
  type: string;
  phoneNumber?: string;
  website?: string;
  latitude?: number | null;
  longitude?: number | null;
}> {
  const fallbackHotels = [
    {
      name: `Grand Hotel ${destination}`,
      description: `Elegant luxury hotel in the heart of ${destination} with exceptional service and premium amenities.`,
      extra: '€150-300/night',
      category: 'Luxury Hotel',
      rating: '⭐⭐⭐⭐⭐ 4.8/5',
      type: 'hotel',
      phoneNumber: '+33 1 42 61 50 50',
      website: 'https://www.grandhotel-luxury.com',
    },
    {
      name: `${destination} Boutique Inn`,
      description: `Charming boutique accommodation featuring unique design and personalized service in ${destination}.`,
      extra: '€80-150/night',
      category: 'Boutique Hotel',
      rating: '⭐⭐⭐⭐ 4.5/5',
      type: 'hotel',
      phoneNumber: '+33 1 44 58 10 10',
      website: 'https://www.boutique-inn.fr',
    },
    {
      name: `Central ${destination} Hotel`,
      description: `Modern hotel perfectly located in central ${destination} with easy access to major attractions.`,
      extra: '€60-120/night',
      category: 'Standard Hotel',
      rating: '⭐⭐⭐⭐ 4.2/5',
      type: 'hotel',
      phoneNumber: '+33 1 47 23 78 29',
      website: 'https://www.centralhotel.com',
    },
    {
      name: `Budget Stay ${destination}`,
      description: `Comfortable and affordable accommodation option for budget-conscious travelers visiting ${destination}.`,
      extra: '€30-60/night',
      category: 'Budget Hotel',
      rating: '⭐⭐⭐ 3.8/5',
      type: 'hotel',
      phoneNumber: '+33 1 48 78 32 18',
      website: 'https://www.budgetstay.fr',
    },
    {
      name: `${destination} Business Hotel`,
      description: `Professional hotel catering to business travelers with meeting facilities and business center.`,
      extra: '€90-180/night',
      category: 'Business Hotel',
      rating: '⭐⭐⭐⭐ 4.3/5',
      type: 'hotel',
      phoneNumber: '+33 1 53 05 90 90',
      website: 'https://www.businesshotel.com',
    },
    {
      name: `Family Resort ${destination}`,
      description: `Family-friendly accommodation with activities for children and spacious family rooms in ${destination}.`,
      extra: '€100-200/night',
      category: 'Family Hotel',
      rating: '⭐⭐⭐⭐ 4.4/5',
      type: 'hotel',
      phoneNumber: '+33 1 45 55 74 00',
      website: 'https://www.familyresort.fr',
    },
  ];

  return fallbackHotels.slice(0, count);
}

/**
 * Récupérer de vraies données de restaurants depuis l'API Serper Maps
 */
async function getWebRestaurants(
  destination: string,
  country: string,
  maxResults = 12,
): Promise<
  Array<{
    name: string;
    description: string;
    extra: string;
    category: string;
    rating: string;
    type: string;
    imageUrl?: string;
    phoneNumber?: string;
    website?: string;
    latitude?: number | null;
    longitude?: number | null;
  }>
> {
  console.log(
    `🍽️ Recherche de restaurants réels via Serper Maps pour ${destination}, ${country}`,
  );

  try {
    const apiKey = process.env.SERPER_API as string;
    if (!apiKey) {
      console.warn('Serper API key not found, using fallback restaurants');
      return generateFallbackRestaurants(destination, country, maxResults);
    }

    // Construire la requête de recherche pour les restaurants
    const searchQuery = `restaurant ${destination} ${country}`;
    const url = `https://google.serper.dev/maps?q=${encodeURIComponent(searchQuery)}&apiKey=${apiKey}`;

    console.log(`[Serper Maps] Recherche restaurants: "${searchQuery}"`);

    const requestOptions = {
      method: 'GET',
      redirect: 'follow' as RequestRedirect,
    };

    const response = await fetch(url, requestOptions);

    if (!response.ok) {
      throw new Error(`Serper Maps API error: ${response.status}`);
    }

    const data = await response.json();
    console.log(
      `[Serper Maps] ${data.places?.length || 0} restaurants trouvés`,
    );

    if (!data.places || data.places.length === 0) {
      console.log(
        '[Serper Maps] Aucun restaurant trouvé, utilisation du fallback',
      );
      return generateFallbackRestaurants(destination, country, maxResults);
    }

    // Convertir les données Serper Maps en format restaurants
    const restaurants: Array<{
      name: string;
      description: string;
      extra: string;
      category: string;
      rating: string;
      type: string;
      imageUrl?: string;
      phoneNumber?: string;
      website?: string;
      latitude?: number | null;
      longitude?: number | null;
    }> = [];

    // Traiter chaque restaurant trouvé par Serper Maps
    for (let i = 0; i < Math.min(data.places.length, maxResults); i++) {
      const place = data.places[i];

      console.log(`[Serper Maps] Traitement du restaurant: "${place.title}"`);

      // Extraire les informations du restaurant
      const restaurantName = place.title || `Restaurant ${i + 1}`;
      const description =
        place.description || 'Excellent restaurant with great cuisine';
      const address = place.address || '';

      // Construire le rating avec étoiles
      let ratingDisplay = '⭐⭐⭐⭐ 4.0/5';
      if (place.rating && place.ratingCount) {
        const stars = '⭐'.repeat(Math.floor(place.rating));
        ratingDisplay = `${stars} ${place.rating}/5 (${place.ratingCount} reviews)`;
      }

      // Déterminer la catégorie basée sur le type
      let category = 'Restaurant';
      if (place.type) {
        if (
          place.type.toLowerCase().includes('fine dining') ||
          place.rating >= 4.5
        ) {
          category = 'Fine Dining';
        } else if (
          place.type.toLowerCase().includes('fast food') ||
          place.type.toLowerCase().includes('quick')
        ) {
          category = 'Fast Food';
        } else if (
          place.type.toLowerCase().includes('cafe') ||
          place.type.toLowerCase().includes('coffee')
        ) {
          category = 'Café';
        } else if (
          place.type.toLowerCase().includes('bar') ||
          place.type.toLowerCase().includes('pub')
        ) {
          category = 'Bar & Grill';
        } else if (place.type.toLowerCase().includes('pizza')) {
          category = 'Pizzeria';
        } else if (
          place.type.toLowerCase().includes('asian') ||
          place.type.toLowerCase().includes('chinese') ||
          place.type.toLowerCase().includes('japanese')
        ) {
          category = 'Asian Cuisine';
        }
      }

      // Construire les informations supplémentaires
      let extraInfo = 'See menu for pricing';
      if (address) {
        extraInfo = address.split(',').slice(0, 2).join(', '); // Première partie de l'adresse
      }

      // Séparer le numéro de téléphone et le site web
      const phoneNumber = place.phoneNumber || '';
      const website = place.website || '';

      // Récupérer les coordonnées GPS
      const latitude = place.latitude || null;
      const longitude = place.longitude || null;

      // Utiliser l'image thumbnail de Google Maps si disponible
      let imageUrl = place.thumbnailUrl;
      if (!imageUrl) {
        // Fallback vers notre système d'images
        try {
          imageUrl = await getRestaurantImageUrl(restaurantName, destination);
        } catch (error) {
          console.warn(
            `Erreur récupération image pour ${restaurantName}:`,
            error,
          );
          imageUrl = getFallbackImage('restaurant', restaurantName);
        }
      }

      restaurants.push({
        name: restaurantName,
        description: description,
        extra: extraInfo,
        category: category,
        rating: ratingDisplay,
        type: 'restaurant',
        imageUrl: imageUrl,
        phoneNumber: phoneNumber,
        website: website,
        latitude: latitude,
        longitude: longitude,
      });

      console.log(
        `[Serper Maps] ✅ Restaurant ajouté: "${restaurantName}" (${category})`,
      );
    }

    console.log(`[Web Restaurants] ${restaurants.length} restaurants trouvés`);

    // Si pas assez de résultats, ajouter des restaurants génériques
    if (restaurants.length < 6) {
      const fallbackRestaurants = generateFallbackRestaurants(
        destination,
        country,
        maxResults - restaurants.length,
      );
      restaurants.push(...fallbackRestaurants);
    }

    return restaurants.slice(0, maxResults);
  } catch (error) {
    console.error('Erreur lors de la récupération des restaurants web:', error);

    // Retourner des restaurants de fallback en cas d'erreur
    return generateFallbackRestaurants(destination, country, maxResults);
  }
}

/**
 * Générer des restaurants de fallback
 */
function generateFallbackRestaurants(
  destination: string,
  _country: string,
  count: number,
): Array<{
  name: string;
  description: string;
  extra: string;
  category: string;
  rating: string;
  type: string;
  phoneNumber?: string;
  website?: string;
  latitude?: number | null;
  longitude?: number | null;
}> {
  const fallbackRestaurants = [
    {
      name: `Le Gourmet ${destination}`,
      description: `Fine dining restaurant featuring local cuisine and international flavors in the heart of ${destination}.`,
      extra: '€€€ • Fine Dining',
      category: 'Fine Dining',
      rating: '⭐⭐⭐⭐⭐ 4.8/5',
      type: 'restaurant',
      phoneNumber: '+33 1 42 86 87 88',
      website: 'https://www.legourmet-restaurant.com',
    },
    {
      name: `${destination} Bistro`,
      description: `Charming local bistro serving traditional dishes with a modern twist and excellent wine selection.`,
      extra: '€€ • Bistro',
      category: 'Bistro',
      rating: '⭐⭐⭐⭐ 4.5/5',
      type: 'restaurant',
      phoneNumber: '+33 1 45 23 45 67',
      website: 'https://www.bistro-local.fr',
    },
    {
      name: `Café Central ${destination}`,
      description: `Popular café perfect for breakfast, lunch, and coffee breaks with fresh pastries and local specialties.`,
      extra: '€ • Café',
      category: 'Café',
      rating: '⭐⭐⭐⭐ 4.3/5',
      type: 'restaurant',
      phoneNumber: '+33 1 48 87 65 43',
      website: 'https://www.cafecentral.com',
    },
    {
      name: `${destination} Street Food`,
      description: `Authentic street food experience with local flavors and quick, delicious meals for travelers.`,
      extra: '€ • Street Food',
      category: 'Fast Food',
      rating: '⭐⭐⭐⭐ 4.1/5',
      type: 'restaurant',
      phoneNumber: '+33 6 12 34 56 78',
    },
    {
      name: `Rooftop Bar ${destination}`,
      description: `Stylish rooftop bar with panoramic city views, craft cocktails, and light dining options.`,
      extra: '€€€ • Bar & Grill',
      category: 'Bar & Grill',
      rating: '⭐⭐⭐⭐ 4.4/5',
      type: 'restaurant',
      phoneNumber: '+33 1 56 78 90 12',
      website: 'https://www.rooftopbar.fr',
    },
    {
      name: `Pizzeria ${destination}`,
      description: `Authentic wood-fired pizza restaurant with fresh ingredients and traditional Italian recipes.`,
      extra: '€€ • Italian',
      category: 'Pizzeria',
      rating: '⭐⭐⭐⭐ 4.2/5',
      type: 'restaurant',
      phoneNumber: '+33 1 34 56 78 90',
      website: 'https://www.pizzeria-italiana.com',
    },
  ];

  return fallbackRestaurants.slice(0, count);
}

/**
 * Exécuter des recherches avec délais pour éviter les erreurs 429
 */
async function _executeSearchesWithDelay(
  queries: string[],
  searchTool: any,
  delayMs: number,
): Promise<any[]> {
  const results: any[] = [];

  for (let i = 0; i < queries.length; i++) {
    try {
      console.log(
        `[Rate Limit] Exécution recherche ${i + 1}/${queries.length}: ${queries[i]}`,
      );

      const result = await (searchTool.execute as any)({
        queries: [queries[i]],
        maxResults: [8],
        topics: ['general'],
        searchDepth: ['advanced'],
      });

      results.push(result);

      // Ajouter un délai entre les recherches (sauf pour la dernière)
      if (i < queries.length - 1) {
        console.log(
          `[Rate Limit] Attente de ${delayMs}ms avant la prochaine recherche...`,
        );
        await new Promise((resolve) => setTimeout(resolve, delayMs));
      }
    } catch (error) {
      console.warn(
        `[Rate Limit] Erreur lors de la recherche "${queries[i]}":`,
        error,
      );
      // Continuer avec les autres recherches même si une échoue
      results.push(null);

      // Attendre plus longtemps en cas d'erreur
      if (i < queries.length - 1) {
        console.log(`[Rate Limit] Attente de ${delayMs * 2}ms après erreur...`);
        await new Promise((resolve) => setTimeout(resolve, delayMs * 2));
      }
    }
  }

  return results.filter((result) => result !== null);
}

/**
 * Récupérer une image d'hôtel avec gestion améliorée des erreurs 429
 */
async function getHotelImageUrlWithRetry(
  hotelName: string,
  destination?: string,
  maxRetries = 2,
): Promise<string> {
  for (let attempt = 1; attempt <= maxRetries; attempt++) {
    try {
      console.log(
        `[Image Retry] Tentative ${attempt}/${maxRetries} pour ${hotelName}`,
      );

      // Ajouter un délai avant chaque tentative (sauf la première)
      if (attempt > 1) {
        const delay = attempt * 1000; // 1s, 2s, etc.
        console.log(
          `[Image Retry] Attente de ${delay}ms avant tentative ${attempt}...`,
        );
        await new Promise((resolve) => setTimeout(resolve, delay));
      }

      return await getHotelImageUrl(hotelName, destination);
    } catch (error: any) {
      console.warn(
        `[Image Retry] Tentative ${attempt} échouée pour ${hotelName}:`,
        error.message,
      );

      // Si c'est une erreur 429 et qu'il reste des tentatives, continuer
      if (error.message?.includes('429') && attempt < maxRetries) {
        console.log(
          `[Image Retry] Erreur 429 détectée, nouvelle tentative dans ${attempt * 2}s...`,
        );
        await new Promise((resolve) => setTimeout(resolve, attempt * 2000));
        continue;
      }

      // Si toutes les tentatives échouent, retourner une image de fallback
      console.log(
        `[Image Retry] Toutes les tentatives échouées pour ${hotelName}, utilisation du fallback`,
      );
      return getFallbackImage('hotel', hotelName);
    }
  }

  // Fallback final
  return getFallbackImage('hotel', hotelName);
}
